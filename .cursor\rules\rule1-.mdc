---
alwaysApply: true
---
1.写测试脚本需要访问MySQL数据库时，请使用一下账号密码！

            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': 'aps',
            'charset': 'utf8mb4'
2.涉及认证相关的，请用admin的密码：admin
3.当你根据新功能需要新建表时，优先确认数据库中是否有相同功能的表！避免重复建表！
4.数据库随时保持统一的数据库接口，因为我们可能随时切换数据库，比如从MySQL切换到其他工业生产的数据库
5.系统架构限定在以下架构中：
- **后端**: Flask 

- **前端**: HTML5 + CSS3 + JavaScript + Bootstrap，紧凑干净的Bootstrap界面
- **数据库**： MySQL数据库
- 前端设计时使用**图标**: Font Awesome 6，其他源代码文件里不要使用图标，以免造成编码问题



