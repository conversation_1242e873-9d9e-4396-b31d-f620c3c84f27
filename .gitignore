# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发 / 打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试 / 覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 数据库文件
*.db
*.sqlite
*.sqlite3
MySQLDATA/

# 临时文件
*.tmp
*.temp
.tmp/
.temp/

# 实例配置
instance/

# Flask 会话文件
flask_session/

# 上传文件
uploads/
exports/

# 静态文件（如果是动态生成的）
static/exports/

# 备份文件
*.bak
*.backup

# 开发工具
debug_*.py
quick_debug.py
*.sql

# 任务文件
.tasks/

# 中文文件名（避免编码问题）
*中文*
*智能*
*调度*
*平台*
*.md
!README.md
!docs/*.md

# 缓存目录
.cache/
.pytest_cache/

# 部署包
APS-Deployment-Package/ 