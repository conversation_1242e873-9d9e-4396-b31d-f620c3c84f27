"""
API v2 - 新的模块化API结构

这是重构后的API结构，采用模块化设计，提供更好的代码组织和维护性。
通过功能开关控制是否启用新的API结构。
"""

from flask import Blueprint
from app.config.feature_flags import is_feature_enabled
from app.utils.api_config import get_api_prefix

# 创建API v2主蓝图 - 使用配置化的URL前缀
api_v2_bp = Blueprint('api_v2', __name__, url_prefix=get_api_prefix())

# 导入并注册子蓝图
from .auth import auth_bp
from .production import production_bp
from .resources import resources_bp
from .system import system_bp
from .orders import orders_bp

def register_blueprints(app):
    """注册所有API v2蓝图"""
    import logging
    logger = logging.getLogger(__name__)
    
    # 先注册主蓝图（提供/api/v2端点）
    app.register_blueprint(api_v2_bp)
    logger.info("✅ API v2 主蓝图已注册")
    
    # 注册子模块蓝图（使用明确的变量名避免冲突）
    app.register_blueprint(auth_bp)  # 这是API v2的auth_bp，URL前缀为/api/v2/auth
    logger.info("✅ API v2 认证模块已注册")
    
    app.register_blueprint(production_bp) 
    logger.info("✅ API v2 生产模块已注册")
    
    app.register_blueprint(system_bp)
    logger.info("✅ API v2 系统模块已注册")
    
    # 注册缓存监控蓝图
    try:
        from .system.cache_monitor import cache_monitor_bp
        app.register_blueprint(cache_monitor_bp, url_prefix='/api/v2/system')
        logger.info("✅ API v2 缓存监控模块已注册")
    except ImportError as e:
        logger.warning(f"⚠️ API v2 缓存监控模块导入失败: {e}")
    
    # 注册资源管理蓝图
    app.register_blueprint(resources_bp)
    logger.info("✅ API v2 资源模块已注册")
    
    # 注册订单管理蓝图
    app.register_blueprint(orders_bp)
    logger.info("✅ API v2 订单模块已注册")
    
    # 注册优先级配置蓝图
    try:
        from .production.priority_api import priority_bp
        app.register_blueprint(priority_bp)
        logger.info("✅ API v2 优先级模块已注册")
    except ImportError as e:
        logger.warning(f"⚠️ API v2 优先级模块导入失败: {e}")
    
    # 注册批次管理蓝图
    try:
        from .production.wait_lots_api import wait_lots_bp
        from .production.done_lots_api import done_lots_bp
        app.register_blueprint(wait_lots_bp)
        app.register_blueprint(done_lots_bp)
        logger.info("✅ API v2 批次管理模块已注册")
    except ImportError as e:
        logger.warning(f"⚠️ API v2 批次管理模块导入失败: {e}")
    
    # 优化解析器已移至订单模块，不再在生产模块中注册

@api_v2_bp.route('/health')
def health_check():
    """API v2总体健康检查"""
    from datetime import datetime
    from flask import jsonify
    
    # 检查各模块状态
    module_status = {}
    try:
        # 检查生产模块
        module_status['production'] = 'ok'
    except:
        module_status['production'] = 'error'
    
    try:
        # 检查资源模块  
        module_status['resources'] = 'ok'
    except:
        module_status['resources'] = 'error'
        
    try:
        # 检查系统模块
        module_status['system'] = 'ok'
    except:
        module_status['system'] = 'error'
    
    overall_status = 'ok' if all(status == 'ok' for status in module_status.values()) else 'degraded'
    
    return jsonify({
        'status': overall_status,
        'service': 'api_v2',
        'version': '2.0',
        'timestamp': datetime.now().isoformat(),
        'modules': module_status,
        'endpoints': {
            'auth': f'{get_api_prefix()}/auth/health',
            'production': f'{get_api_prefix()}/production/health', 
            'resources': f'{get_api_prefix()}/resources/health',
            'system': f'{get_api_prefix()}/system/health'
        }
    })

def api_info():
    """API v2信息端点"""
    from datetime import datetime
    from flask import jsonify, current_app
    
    return jsonify({
        'api_version': '2.0',
        'service_name': 'APS智能调度平台API v2',
        'timestamp': datetime.now().isoformat(),
        'app_version': current_app.config.get('APP_VERSION', '1.0.0'),
        'features': {
            'production_scheduling': True,
            'resource_management': True,
            'priority_optimization': True,
            'excel_integration': True,
            'real_time_monitoring': True
        },
        'available_endpoints': {
            'auth': f'{get_api_prefix()}/auth/*',
            'production': f'{get_api_prefix()}/production/*',
            'resources': f'{get_api_prefix()}/resources/*', 
            'system': f'{get_api_prefix()}/system/*'
        },
        'documentation': f'{get_api_prefix()}/docs',
        'health_check': f'{get_api_prefix()}/health'
    })

 