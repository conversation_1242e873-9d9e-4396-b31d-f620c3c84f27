from flask import jsonify, request, current_app, send_file
from flask_login import login_required, current_user
from . import production_bp
from app import db
from app.models import ProductionOrder, ProductionSchedule, CustomerOrder, OrderItem, Product, DevicePriorityConfig, LotPriorityConfig
from app.models.production.scheduling_history import get_history_manager
from .scheduling_service import ProductionSchedulingServiceV2
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict
import pandas as pd
import io
import os
import logging
from werkzeug.utils import secure_filename
from sqlalchemy import text

logger = logging.getLogger(__name__)

# 初始化调度服务
scheduling_service = ProductionSchedulingServiceV2()

@production_bp.route('/health')
def health_check():
    """生产管理模块健康检查"""
    return jsonify({
        'status': 'ok',
        'service': 'production_v2',
        'timestamp': datetime.now().isoformat()
    })

@production_bp.route('/orders')
@login_required
def get_orders():
    """获取客户订单列表"""
    try:
        search = request.args.get('search', '')
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        query = CustomerOrder.query
        if search:
            query = query.filter(
                db.or_(
                    CustomerOrder.order_number.ilike(f'%{search}%'),
                    CustomerOrder.customer_name.ilike(f'%{search}%')
                )
            )
        
        orders = query.order_by(CustomerOrder.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'items': [{
                'id': order.id,
                'order_number': order.order_number,
                'customer_name': order.customer_name,
                'status': order.status,
                'total_amount': order.total_amount,
                'created_at': order.created_at.isoformat(),
                'items': [{
                    'id': item.id,
                    'product': {
                        'id': item.product.id,
                        'name': item.product.name,
                        'code': item.product.code
                    } if item.product else None,
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'total_price': item.total_price
                } for item in order.items]
            } for order in orders.items],
            'total': orders.total,
            'pages': orders.pages,
            'current_page': orders.page
        })
    except Exception as e:
        logger.error(f"获取订单列表失败: {str(e)}")
        return jsonify({'error': '获取订单列表失败'}), 500

@production_bp.route('/orders/<int:order_id>')
@login_required
def get_order(order_id):
    """获取单个订单详情"""
    try:
        order = CustomerOrder.query.get_or_404(order_id)
        return jsonify({
            'id': order.id,
            'order_number': order.order_number,
            'customer_name': order.customer_name,
            'status': order.status,
            'total_amount': order.total_amount,
            'created_at': order.created_at.isoformat(),
            'items': [{
                'id': item.id,
                'product': {
                    'id': item.product.id,
                    'name': item.product.name,
                    'code': item.product.code
                } if item.product else None,
                'quantity': item.quantity,
                'unit_price': item.unit_price,
                'total_price': item.total_price
            } for item in order.items]
        })
    except Exception as e:
        logger.error(f"获取订单详情失败: {str(e)}")
        return jsonify({'error': '获取订单详情失败'}), 500

@production_bp.route('/orders', methods=['POST'])
@login_required
def create_order():
    """创建新订单"""
    try:
        data = request.get_json()
        
        # 验证数据
        if not all(k in data for k in ('customer_name', 'items')):
            return jsonify({'error': '缺少必需字段'}), 400
        
        # 生成订单编号
        order_number = f'ORD-{datetime.now().strftime("%Y%m%d%H%M%S")}'
        
        # 创建订单
        order = CustomerOrder(
            order_number=order_number,
            customer_name=data['customer_name'],
            status='new',
            total_amount=0
        )
        
        db.session.add(order)
        db.session.flush()  # 获取订单ID
        
        # 添加订单项
        total_amount = 0
        for item_data in data['items']:
            product = Product.query.get(item_data['product_id'])
            if not product:
                return jsonify({'error': f'产品ID {item_data["product_id"]} 不存在'}), 400
            
            item = OrderItem(
                order_id=order.id,
                product_id=product.id,
                quantity=item_data['quantity'],
                unit_price=item_data.get('unit_price', product.price or 0),
                total_price=item_data['quantity'] * item_data.get('unit_price', product.price or 0)
            )
            db.session.add(item)
            total_amount += item.total_price
        
        # 更新订单总金额
        order.total_amount = total_amount
        db.session.commit()
        
        return jsonify({
            'id': order.id,
            'order_number': order.order_number,
            'message': '订单创建成功'
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建订单失败: {str(e)}")
        return jsonify({'error': '创建订单失败'}), 500

@production_bp.route('/schedules')
@login_required
def get_schedules():
    """获取生产调度列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', '')
        
        query = ProductionSchedule.query
        if status:
            query = query.filter(ProductionSchedule.status == status)
        
        schedules = query.order_by(ProductionSchedule.scheduled_start.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        
        return jsonify({
            'items': [{
                'id': schedule.id,
                'production_order_id': schedule.production_order_id,
                'resource_id': schedule.resource_id,
                'scheduled_start': schedule.scheduled_start.isoformat() if schedule.scheduled_start else None,
                'scheduled_end': schedule.scheduled_end.isoformat() if schedule.scheduled_end else None,
                'actual_start': schedule.actual_start.isoformat() if schedule.actual_start else None,
                'actual_end': schedule.actual_end.isoformat() if schedule.actual_end else None,
                'status': schedule.status,
                'priority': schedule.priority
            } for schedule in schedules.items],
            'total': schedules.total,
            'pages': schedules.pages,
            'current_page': schedules.page
        })
    except Exception as e:
        logger.error(f"获取生产调度失败: {str(e)}")
        return jsonify({'error': '获取生产调度失败'}), 500

# =============================================================================
# 数据源管理API端点
# =============================================================================

@production_bp.route('/data-source/status')
@login_required
def get_data_source_status():
    """获取数据源状态"""
    try:
        status = scheduling_service.get_data_source_status()
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取数据源状态失败: {str(e)}")
        return jsonify({'error': '获取数据源状态失败'}), 500

@production_bp.route('/data-source/switch', methods=['POST'])
@login_required
def switch_data_source():
    """切换数据源"""
    try:
        data = request.get_json()
        source = data.get('source', 'auto')
        
        result = scheduling_service.switch_data_source(source)
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 400
            
    except Exception as e:
        logger.error(f"切换数据源失败: {str(e)}")
        return jsonify({'error': '切换数据源失败'}), 500

@production_bp.route('/data-source/validate')
@login_required
def validate_data_consistency():
    """验证数据一致性"""
    try:
        result = scheduling_service.validate_data_consistency()
        return jsonify(result)
    except Exception as e:
        logger.error(f"验证数据一致性失败: {str(e)}")
        return jsonify({'error': '验证数据一致性失败'}), 500

@production_bp.route('/data-source/metrics')
@login_required
def get_scheduling_metrics():
    """获取排产指标统计"""
    try:
        metrics = scheduling_service.get_scheduling_metrics()
        return jsonify(metrics)
    except Exception as e:
        logger.error(f"获取排产指标失败: {str(e)}")
        return jsonify({'error': '获取排产指标失败'}), 500

# =============================================================================
# Excel数据导入API端点
# =============================================================================

@production_bp.route('/data-source/wait-lots')
@login_required
def get_wait_lots_data():
    """获取待排产批次数据"""
    try:
        source = request.args.get('source', None)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        wait_lots, data_source = scheduling_service.get_wait_lot_data(source)
        
        # 分页处理
        start = (page - 1) * per_page
        end = start + per_page
        paginated_lots = wait_lots[start:end]
        
        return jsonify({
            'items': paginated_lots,
            'total': len(wait_lots),
            'page': page,
            'per_page': per_page,
            'pages': (len(wait_lots) + per_page - 1) // per_page,
            'data_source': data_source
        })
        
    except Exception as e:
        logger.error(f"获取待排产批次数据失败: {str(e)}")
        return jsonify({'error': '获取待排产批次数据失败'}), 500

@production_bp.route('/data-source/uph-equipment')
@login_required
def get_uph_equipment_data():
    """获取设备UPH数据"""
    try:
        source = request.args.get('source', None)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        uph_data, data_source = scheduling_service.get_uph_equipment_data(source)
        
        # 分页处理
        start = (page - 1) * per_page
        end = start + per_page
        paginated_data = uph_data[start:end]
        
        return jsonify({
            'items': paginated_data,
            'total': len(uph_data),
            'page': page,
            'per_page': per_page,
            'pages': (len(uph_data) + per_page - 1) // per_page,
            'data_source': data_source
        })
        
    except Exception as e:
        logger.error(f"获取设备UPH数据失败: {str(e)}")
        return jsonify({'error': '获取设备UPH数据失败'}), 500

@production_bp.route('/data-source/equipment-status')
@login_required
def get_equipment_status_data():
    """获取设备状态数据"""
    try:
        source = request.args.get('source', None)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        equipment_data, data_source = scheduling_service.get_equipment_status_data(source)
        
        # 分页处理
        start = (page - 1) * per_page
        end = start + per_page
        paginated_data = equipment_data[start:end]
        
        return jsonify({
            'items': paginated_data,
            'total': len(equipment_data),
            'page': page,
            'per_page': per_page,
            'pages': (len(equipment_data) + per_page - 1) // per_page,
            'data_source': data_source
        })
        
    except Exception as e:
        logger.error(f"获取设备状态数据失败: {str(e)}")
        return jsonify({'error': '获取设备状态数据失败'}), 500

# =============================================================================
# 智能排产API端点
# =============================================================================

@production_bp.route('/scheduling/execute', methods=['POST'])
@login_required
def execute_intelligent_scheduling():
    """执行智能排产"""
    try:
        data = request.get_json()
        
        # 验证参数
        algorithm = data.get('algorithm', 'intelligent')
        optimization_target = data.get('optimization_target', 'balanced')
        
        if algorithm not in ['deadline', 'product', 'value', 'intelligent']:
            return jsonify({'error': '无效的排产算法'}), 400
            
        if optimization_target not in ['time', 'balanced', 'efficiency']:
            return jsonify({'error': '无效的优化目标'}), 400
        
        # 执行排产
        result = scheduling_service.execute_intelligent_scheduling({
            'algorithm': algorithm,
            'optimization_target': optimization_target
        })
        
        if result['success']:
            return jsonify(result)
        else:
            return jsonify(result), 500
            
    except Exception as e:
        logger.error(f"执行智能排产失败: {str(e)}")
        return jsonify({'error': '执行智能排产失败'}), 500

# =============================================================================
# 优先级配置API v2端点
# =============================================================================

@production_bp.route('/priority/product', methods=['GET'])
@login_required
def get_product_priority_config_v2():
    """获取产品优先级配置"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # 这里可以添加产品优先级配置查询逻辑
        # 目前返回模拟数据
        return jsonify({
            'items': [],
            'total': 0,
            'page': page,
            'per_page': per_page,
            'pages': 0,
            'message': '产品优先级配置API v2功能将在后续版本中实现'
        })
        
    except Exception as e:
        logger.error(f"获取产品优先级配置失败: {str(e)}")
        return jsonify({'error': '获取产品优先级配置失败'}), 500

@production_bp.route('/priority/product', methods=['POST'])
@login_required
def create_product_priority_config_v2():
    """创建产品优先级配置"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['product_name', 'priority_value']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必需字段: {field}'}), 400
        
        # 这里可以添加创建逻辑
        return jsonify({
            'success': True,
            'message': '产品优先级配置创建功能将在后续版本中实现',
            'data': data
        })
        
    except Exception as e:
        logger.error(f"创建产品优先级配置失败: {str(e)}")
        return jsonify({'error': '创建产品优先级配置失败'}), 500

@production_bp.route('/priority/lot', methods=['GET'])
@login_required
def get_lot_priority_config_v2():
    """获取批次优先级配置 - 包含lot_id字段"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        # 获取查询参数
        lot_id = request.args.get('lot_id', '').strip()
        device = request.args.get('device', '').strip()
        stage = request.args.get('stage', '').strip()
        priority = request.args.get('priority', '').strip()
        
        # 构建查询
        query = LotPriorityConfig.query
        
        # 添加过滤条件
        if lot_id:
            query = query.filter(LotPriorityConfig.lot_id.like(f'%{lot_id}%'))
        if device:
            query = query.filter(LotPriorityConfig.device.like(f'%{device}%'))
        if stage:
            query = query.filter(LotPriorityConfig.stage.like(f'%{stage}%'))
        if priority:
            query = query.filter(LotPriorityConfig.priority == priority)
        
        # 按创建时间排序
        query = query.order_by(LotPriorityConfig.created_at.desc())
        
        # 分页查询
        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        items = pagination.items
        
        # 转换为字典格式
        lot_configs = [item.to_dict() for item in items]
        
        return jsonify({
            'items': lot_configs,
            'total': pagination.total,
            'page': page,
            'per_page': per_page,
            'pages': pagination.pages,
            'has_prev': pagination.has_prev,
            'has_next': pagination.has_next
        })
        
    except Exception as e:
        logger.error(f"获取批次优先级配置失败: {str(e)}")
        return jsonify({'error': f'获取批次优先级配置失败: {str(e)}'}), 500

@production_bp.route('/priority/device', methods=['GET'])
@login_required
def get_device_priority_config_v2():
    """获取设备优先级配置"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 50, type=int)
        
        return jsonify({
            'items': [],
            'total': 0,
            'page': page,
            'per_page': per_page,
            'pages': 0,
            'message': '设备优先级配置API v2功能将在后续版本中实现'
        })
        
    except Exception as e:
        logger.error(f"获取设备优先级配置失败: {str(e)}")
        return jsonify({'error': '获取设备优先级配置失败'}), 500

@production_bp.route('/scheduling-history', methods=['GET'])
@login_required
def get_scheduling_history():
    """获取排产历史记录 - v2 (整合手动和定时任务记录)"""
    try:
        # v2 API暂时不支持分页，一次性获取最近50条
        limit = request.args.get('per_page', 50, type=int)
        
        # 🔧 修复：整合两个数据源的历史记录
        all_history_items = _get_unified_scheduling_history(limit)
        
        return jsonify({
            'success': True,
            'items': all_history_items,
            'total': len(all_history_items),
            'page': 1,
            'per_page': limit,
            'pages': 1,
            'message': f'成功获取最近 {len(all_history_items)} 条历史记录（包含手动和定时任务）'
        })
        
    except Exception as e:
        logger.error(f"获取v2排产历史失败: {str(e)}", exc_info=True)
        return jsonify({'error': f'获取排产历史失败: {str(e)}'}), 500

def _get_unified_scheduling_history(limit: int = 50) -> List[Dict]:
    """统一获取手动执行和定时任务的排产历史记录"""
    try:
        from app.utils.db_helper import get_mysql_connection
        from pymysql.cursors import DictCursor
        
        # 获取新系统的历史记录（手动执行）
        history_manager = get_history_manager()
        manual_history = history_manager.get_recent_history(limit=limit//2, user_id=None)
        
        # 获取旧系统的历史记录（定时任务）
        scheduled_history = _get_scheduled_task_history(limit//2)
        
        # 合并并按时间排序
        all_items = []
        
        # 转换手动执行的记录格式
        for item in manual_history:
            # 🔧 修复：确保duration字段是数字类型
            duration_value = item.get('duration_seconds', 0)
            try:
                # 尝试转换为浮点数
                duration_number = float(duration_value) if duration_value is not None else 0.0
            except (ValueError, TypeError):
                # 如果转换失败，使用默认值0.0
                duration_number = 0.0
            
            all_items.append({
                'history_id': item.get('history_id'),
                'algorithm': item.get('algorithm', 'unknown'),
                'start_time': item.get('start_time'),
                'results_count': item.get('results_count', 0),
                'duration': duration_number,
                'status': 'COMPLETED' if item.get('status') == 'COMPLETED' else 'FAILED',
                'execution_type': 'manual',  # 标识为手动执行
                'user_id': item.get('user_id', 'unknown')
            })
        
        # 添加定时任务的记录
        all_items.extend(scheduled_history)
        
        # 按开始时间降序排序
        all_items.sort(key=lambda x: x.get('start_time', ''), reverse=True)
        
        # 限制返回数量
        return all_items[:limit]
        
    except Exception as e:
        logger.error(f"获取统一历史记录失败: {e}")
        return []

def _get_scheduled_task_history(limit: int = 25) -> List[Dict]:
    """获取定时任务的排产历史记录"""
    try:
        from app.utils.db_helper import get_mysql_connection
        from pymysql.cursors import DictCursor
        import json
        
        conn = get_mysql_connection()
        cursor = conn.cursor(DictCursor)
        
        # 查询schedule_history表（定时任务历史记录）
        cursor.execute("""
            SELECT 
                CONCAT('scheduled_', id) as history_id,
                strategy,
                timestamp,
                batch_count,
                execution_time,
                schedule_results,
                metrics,
                created_at
            FROM schedule_history 
            ORDER BY created_at DESC 
            LIMIT %s
        """, (limit,))
        
        rows = cursor.fetchall()
        cursor.close()
        history_items = []
        for row in rows:
            try:
                # 解析metrics获取更多信息
                metrics = {}
                if row.get('metrics'):
                    try:
                        metrics = json.loads(row['metrics']) if isinstance(row['metrics'], str) else row['metrics']
                    except json.JSONDecodeError:
                        metrics = {}
                
                # 从strategy中提取算法名称
                strategy = row.get('strategy', '')
                if '定时任务' in strategy:
                    # 移除"(定时任务)"后缀，提取算法名称
                    algorithm = strategy.replace(' (定时任务)', '').replace('(定时任务)', '')
                    algorithm_map = {
                        '智能综合': 'intelligent',
                        '交期优先': 'deadline', 
                        '产品优先': 'product',
                        '产值优先': 'value'
                    }
                    algorithm_code = algorithm_map.get(algorithm, 'intelligent')
                else:
                    algorithm_code = metrics.get('strategy', 'intelligent')
                
                # 🔧 修复：确保duration字段是数字类型
                duration_value = row.get('execution_time', 0)
                try:
                    # 尝试转换为浮点数
                    duration_number = float(duration_value) if duration_value is not None else 0.0
                except (ValueError, TypeError):
                    # 如果转换失败，使用默认值0.0
                    duration_number = 0.0
                
                history_items.append({
                    'history_id': row['history_id'],
                    'algorithm': algorithm_code,
                    'start_time': row.get('timestamp', row.get('created_at')).isoformat() if row.get('timestamp') or row.get('created_at') else None,
                    'results_count': row.get('batch_count', 0),
                    'duration': duration_number,
                    'status': 'COMPLETED',  # 能保存到数据库的都是成功的
                    'execution_type': 'scheduled_task',  # 标识为定时任务
                    'user_id': 'system',  # 定时任务统一标识为system
                    'task_name': metrics.get('task_name', '未命名定时任务')
                })
                
            except Exception as e:
                logger.warning(f"解析定时任务历史记录失败: {e}")
                continue
        
        logger.info(f"获取到 {len(history_items)} 条定时任务历史记录")
        return history_items
        
    except Exception as e:
        logger.error(f"获取定时任务历史记录失败: {e}")
        return []

@production_bp.route('/scheduling/export', methods=['POST'])
@login_required
def export_scheduling_result():
    """导出排产结果"""
    try:
        data = request.get_json()
        format_type = data.get('format', 'excel')
        
        if format_type not in ['excel', 'json', 'csv']:
            return jsonify({'error': '不支持的导出格式'}), 400
        
        # 这里可以添加排产结果导出逻辑
        return jsonify({
            'success': True,
            'message': f'排产结果导出功能({format_type})将在后续版本中实现',
            'format': format_type
        })
        
    except Exception as e:
        logger.error(f"导出排产结果失败: {str(e)}")
        return jsonify({'error': '导出排产结果失败'}), 500

@production_bp.route('/orders/export')
@login_required
def export_orders():
    """导出订单数据"""
    try:
        # 获取所有订单数据
        orders = CustomerOrder.query.all()
        
        # 准备导出数据
        export_data = []
        for order in orders:
            for item in order.items:
                export_data.append({
                    '订单编号': order.order_number,
                    '客户名称': order.customer_name,
                    '订单状态': order.status,
                    '产品名称': item.product.name if item.product else '',
                    '产品代码': item.product.code if item.product else '',
                    '数量': item.quantity,
                    '单价': item.unit_price,
                    '总价': item.total_price,
                    '创建时间': order.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })
        
        # 创建Excel文件
        df = pd.DataFrame(export_data)
        output = io.BytesIO()
        
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='订单数据', index=False)
        
        output.seek(0)
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=f'orders_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        )
        
    except Exception as e:
        logger.error(f"导出订单失败: {str(e)}")
        return jsonify({'error': '导出订单失败'}), 500

@production_bp.route('/priority-settings/upload', methods=['POST'])
@login_required
def upload_priority_excel():
    """上传优先级配置Excel文件"""
    try:
        logger.info(f"用户 {current_user.username} 开始上传优先级Excel文件")
        
        if 'files' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'})
        
        files = request.files.getlist('files')
        if not files or all(f.filename == '' for f in files):
            return jsonify({'success': False, 'error': '没有选择文件'})
        
        # 限制只能上传一个文件
        if len(files) > 1:
            return jsonify({'success': False, 'error': '只允许上传单个Excel文件'})
        
        file = files[0]
        if not file or file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'})
        
        filename = secure_filename(file.filename)
        logger.info(f"处理文件: {filename}")
        
        # 严格验证文件类型
        filename_lower = filename.lower()
        if not (filename_lower.endswith('.xlsx') or filename_lower.endswith('.xls')):
            return jsonify({'success': False, 'error': '只支持Excel文件格式(.xlsx或.xls)'})
        
        # 验证文件名包含正确的关键字
        if not ('device' in filename_lower or 'lot' in filename_lower):
            return jsonify({
                'success': False, 
                'error': '文件名必须包含"device"或"lot"关键字，以便系统识别文件类型'
            })
        
        result = process_priority_excel_file(file, filename)
        
        # 构建详细的响应信息
        if result['success']:
            success_message = f"✅ 成功导入 {result.get('imported_count', 0)} 条记录"
            if result.get('warnings'):
                success_message += f"，但有 {len(result['warnings'])} 个警告"
        else:
            success_message = f"❌ 导入失败: {result.get('error', '未知错误')}"
        
        return jsonify({
            'success': result['success'],
            'results': [result],
            'total_processed': result.get('imported_count', 0) if result['success'] else 0,
            'total_files': 1,
            'successful_files': 1 if result['success'] else 0,
            'failed_files': 0 if result['success'] else 1,
            'message': success_message,
            'details': {
                'filename': result.get('filename'),
                'table_type': result.get('table_type'),
                'total_rows': result.get('total_rows', 0),
                'imported_count': result.get('imported_count', 0),
                'error_count': len(result.get('warnings', [])),
                'warnings': result.get('warnings', [])[:5],  # 显示前5个警告
                'database_info': {
                    'table': result.get('table_name'),
                    'previous_count': result.get('previous_count'),
                    'final_count': result.get('final_count')
                }
            }
        })
        
    except Exception as e:
        logger.error(f"优先级Excel上传失败: {str(e)}")
        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'})

def process_priority_excel_file(file, filename):
    """处理单个优先级Excel文件"""
    try:
        logger.info(f"开始处理文件: {filename}")
        
        # 改进的文件名匹配逻辑
        filename_lower = filename.lower()
        if 'devicepriorityconfig' in filename_lower or 'device' in filename_lower:
            table_type = 'device'
            model_class = DevicePriorityConfig
            logger.info(f"识别为产品优先级配置文件: {filename}")
        elif 'lotpriorityconfig' in filename_lower or 'lot' in filename_lower:
            table_type = 'lot'  
            model_class = LotPriorityConfig
            logger.info(f"识别为批次优先级配置文件: {filename}")
        else:
            error_msg = f'无法识别文件类型，文件名应包含 device 或 lot 关键字。当前文件名: {filename}'
            logger.warning(error_msg)
            return {
                'filename': filename,
                'success': False,
                'error': error_msg
            }
        
        # 读取Excel文件
        try:
            df = pd.read_excel(file)
            logger.info(f"成功读取Excel文件，共 {len(df)} 行数据")
            logger.info(f"Excel列名: {list(df.columns)}")
            logger.info(f"前3行数据预览: {df.head(3).to_dict('records')}")
        except Exception as e:
            error_msg = f'读取Excel文件失败: {str(e)}'
            logger.error(error_msg)
            return {
                'filename': filename,
                'success': False,
                'error': error_msg
            }
        
        if df.empty:
            return {
                'filename': filename,
                'success': False,
                'error': 'Excel文件为空'
            }
        
        # 记录现有数据数量和数据库信息
        existing_count = model_class.query.count()
        logger.info(f"表 {model_class.__tablename__} 现有 {existing_count} 条记录")
        logger.info(f"模型绑定数据库: {model_class.__bind_key__}")
        logger.info(f"表名: {model_class.__tablename__}")

        # 验证数据库连接
        try:
            from app import db
            engine = db.get_engine(bind=model_class.__bind_key__)
            logger.info(f"数据库引擎URL: {engine.url}")
        except Exception as e:
            logger.warning(f"获取数据库引擎信息失败: {e}")
        
        # 验证必需字段是否存在
        required_fields = []
        if table_type == 'device':
            required_fields = ['device', 'priority']
        else:  # lot
            required_fields = ['lot_id', 'device', 'priority']
        
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            error_msg = f'Excel文件缺少必需字段: {missing_fields}。实际字段: {list(df.columns)}'
            logger.error(error_msg)
            return {
                'filename': filename,
                'success': False,
                'error': error_msg,
                'table_type': table_type,
                'total_rows': len(df),
                'required_fields': required_fields,
                'actual_fields': list(df.columns),
                'missing_fields': missing_fields
            }
        
        imported_count = 0
        errors = []
        
        for index, row in df.iterrows():
            try:
                logger.info(f"处理第{index+1}行数据: {row.to_dict()}")

                if table_type == 'device':
                    # 检查必需字段（使用小写字段名）
                    device_val = row.get('device')
                    priority_val = row.get('priority')
                    logger.info(f"第{index+1}行 - device: {device_val}, priority: {priority_val}")

                    if pd.isna(device_val) or pd.isna(priority_val):
                        error_msg = f'第{index+2}行: device和priority字段不能为空 (device={device_val}, priority={priority_val})'
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue
                    
                    record = DevicePriorityConfig(
                        device=str(row.get('device', '')),
                        stage=str(row.get('stage', '')) if pd.notna(row.get('stage')) else None,
                        handler_config=str(row.get('handler_config', '')) if pd.notna(row.get('handler_config')) else None,
                        handler_priority=str(row.get('handler_priority', '')) if pd.notna(row.get('handler_priority')) else None,
                        setup_qty=int(row.get('setup_qty', 0)) if pd.notna(row.get('setup_qty')) else None,
                        priority=str(row.get('priority', '5')),
                        price=float(row.get('price', 0)) if pd.notna(row.get('price')) else None,
                        from_time=pd.to_datetime(row.get('from_time')) if pd.notna(row.get('from_time')) else None,
                        end_time=pd.to_datetime(row.get('end_time')) if pd.notna(row.get('end_time')) else None,
                        refresh_time=pd.to_datetime(row.get('refresh_time')) if pd.notna(row.get('refresh_time')) else None,
                        user=str(row.get('user', '')) if pd.notna(row.get('user')) else current_user.username
                    )
                else:  # lot
                    # 检查必需字段（使用小写字段名，包括lot_id）
                    lot_id_val = row.get('lot_id')
                    device_val = row.get('device')
                    priority_val = row.get('priority')
                    logger.info(f"第{index+1}行 - lot_id: {lot_id_val}, device: {device_val}, priority: {priority_val}")

                    if pd.isna(lot_id_val) or pd.isna(device_val) or pd.isna(priority_val):
                        error_msg = f'第{index+2}行: lot_id、device和priority字段不能为空 (lot_id={lot_id_val}, device={device_val}, priority={priority_val})'
                        errors.append(error_msg)
                        logger.warning(error_msg)
                        continue
                    
                    record = LotPriorityConfig(
                        lot_id=str(row.get('lot_id', '')),
                        device=str(row.get('device', '')),
                        stage=str(row.get('stage', '')) if pd.notna(row.get('stage')) else None,
                        priority=str(row.get('priority', '5')),
                        refresh_time=pd.to_datetime(row.get('refresh_time')) if pd.notna(row.get('refresh_time')) else None,
                        user=str(row.get('user', '')) if pd.notna(row.get('user')) else current_user.username
                    )

                db.session.add(record)
                imported_count += 1
                logger.info(f"成功创建记录 {imported_count}: {record}")
                
            except Exception as e:
                error_msg = f'第{index+2}行数据处理失败: {str(e)}'
                errors.append(error_msg)
                logger.warning(error_msg)
                continue
        
        # 提交事务
        try:
            logger.info(f"准备提交 {imported_count} 条记录到数据库...")
            db.session.commit()
            logger.info(f"✅ 成功导入 {imported_count} 条记录到 {model_class.__tablename__} 表")

            # 验证数据是否真的插入了
            final_count = model_class.query.count()
            logger.info(f"📊 提交后表 {model_class.__tablename__} 总记录数: {final_count}")

        except Exception as e:
            db.session.rollback()
            error_msg = f'数据库提交失败: {str(e)}'
            logger.error(error_msg)
            logger.error(f"数据库绑定: {model_class.__bind_key__}")
            logger.error(f"表名: {model_class.__tablename__}")
            return {
                'filename': filename,
                'success': False,
                'error': error_msg,
                'table_type': table_type,
                'total_rows': len(df),
                'imported_count': 0,
                'table_name': model_class.__tablename__,
                'previous_count': existing_count,
                'final_count': existing_count  # 失败时保持原有数量
            }
        
        # 构建详细的成功结果
        result = {
            'filename': filename,
            'success': True,
            'imported_count': imported_count,
            'table_type': table_type,
            'total_rows': len(df),
            'skipped_rows': len(df) - imported_count,
            'table_name': model_class.__tablename__,
            'previous_count': existing_count,
            'final_count': final_count,
            'processing_summary': {
                'total_rows_in_excel': len(df),
                'successfully_imported': imported_count,
                'skipped_due_to_errors': len(errors),
                'validation_errors': len(errors)
            }
        }
        
        if errors:
            result['warnings'] = errors[:10]  # 显示前10个错误
            result['total_warnings'] = len(errors)
            logger.warning(f"文件 {filename} 导入时有 {len(errors)} 个警告")
        else:
            result['warnings'] = []
            result['total_warnings'] = 0
            
        return result
        
    except Exception as e:
        db.session.rollback()
        error_msg = f'处理文件失败: {str(e)}'
        logger.error(error_msg)
        logger.error(f"异常类型: {type(e).__name__}")
        logger.error(f"异常详情: {e}", exc_info=True)
        
        return {
            'filename': filename,
            'success': False,
            'error': error_msg,
            'error_type': type(e).__name__,
            'table_type': table_type if 'table_type' in locals() else 'unknown',
            'total_rows': len(df) if 'df' in locals() else 0,
            'imported_count': 0,
            'processing_stage': 'file_processing',
            'technical_details': {
                'exception_class': type(e).__name__,
                'exception_message': str(e),
                'processing_stage': 'general_file_processing'
            }
        } 

@production_bp.route('/scheduling-history/<string:history_id>', methods=['GET'])
@login_required
def get_scheduling_history_detail(history_id):
    """获取指定排产历史记录的详细信息（支持手动和定时任务）"""
    try:
        logger.info(f"🔍 获取历史记录详情: {history_id}")
        
        # 🔧 修复：判断是手动执行还是定时任务的记录
        if history_id.startswith('scheduled_'):
            # 定时任务记录
            return _get_scheduled_task_detail(history_id)
        else:
            # 手动执行记录
            return _get_manual_task_detail(history_id)
        
    except Exception as e:
        logger.error(f"❌ 获取历史记录详情失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取历史记录详情失败: {str(e)}'
        }), 500

def _get_manual_task_detail(history_id: str):
    """获取手动执行任务的详情"""
    # 使用历史管理器获取详细记录
    history_manager = get_history_manager()
    history_record = history_manager.get_by_id(history_id)
    
    if not history_record:
        return jsonify({
            'success': False,
            'error': '历史记录不存在'
        }), 404
    
    # 🔥 正确提取schedule_results字段
    output_summary = history_record.output_summary or {}
    schedule_results = []
    if isinstance(output_summary, dict):
        schedule_results = output_summary.get('schedule_results', [])
    elif isinstance(output_summary, list):
        # 兼容旧格式
        schedule_results = output_summary
    
    # 构建返回数据
    result_data = {
        'schedule_results': schedule_results,
        'parameters': {
            'strategy': history_record.algorithm,
            'algorithm': history_record.algorithm,
            'optimization_target': history_record.optimization_target,
            'user_id': history_record.user_id,
            'total_batches': history_record.input_summary.get('wait_lots_count', 0) if history_record.input_summary else 0
        },
        'statistics': {
            'total_lots': history_record.results_count or 0,
            'execution_time': history_record._calculate_duration() or 0.0,
            'scheduled_batches': history_record.results_count or 0,
            'status': history_record.status
        },
        'created_at': history_record.start_time.isoformat() if history_record.start_time else None,
        'error_message': history_record.error_message
    }
    
    logger.info(f"✅ 成功获取手动执行历史记录详情: {history_id}")
    return jsonify({
        'success': True,
        'data': result_data
    })

def _get_scheduled_task_detail(history_id: str):
    """获取定时任务的详情"""
    try:
        from app.utils.db_helper import get_mysql_connection
        from pymysql.cursors import DictCursor
        import json
        
        # 提取数据库ID（移除 'scheduled_' 前缀）
        db_id = history_id.replace('scheduled_', '')
        
        conn = get_mysql_connection()
        cursor = conn.cursor(DictCursor)
        
        # 查询定时任务历史记录详情
        cursor.execute("""
            SELECT 
                id,
                strategy,
                timestamp,
                batch_count,
                execution_time,
                schedule_results,
                metrics,
                created_at
            FROM schedule_history 
            WHERE id = %s
        """, (db_id,))
        
        row = cursor.fetchone()
        cursor.close()
        if not row:
            return jsonify({
                'success': False,
                'error': '定时任务历史记录不存在'
            }), 404
        
        # 解析JSON数据
        schedule_results = []
        if row.get('schedule_results'):
            try:
                schedule_results = json.loads(row['schedule_results']) if isinstance(row['schedule_results'], str) else row['schedule_results']
            except json.JSONDecodeError:
                schedule_results = []
        
        metrics = {}
        if row.get('metrics'):
            try:
                metrics = json.loads(row['metrics']) if isinstance(row['metrics'], str) else row['metrics']
            except json.JSONDecodeError:
                metrics = {}
        
        # 从strategy中提取算法名称
        strategy = row.get('strategy', '')
        if '定时任务' in strategy:
            algorithm = strategy.replace(' (定时任务)', '').replace('(定时任务)', '')
            algorithm_map = {
                '智能综合': 'intelligent',
                '交期优先': 'deadline', 
                '产品优先': 'product',
                '产值优先': 'value'
            }
            algorithm_code = algorithm_map.get(algorithm, 'intelligent')
        else:
            algorithm_code = metrics.get('strategy', 'intelligent')
        
        # 构建返回数据
        result_data = {
            'schedule_results': schedule_results,
            'parameters': {
                'strategy': algorithm_code,
                'algorithm': algorithm_code,
                'optimization_target': 'balanced',  # 定时任务默认目标
                'user_id': 'system',
                'total_batches': row.get('batch_count', 0),
                'task_name': metrics.get('task_name', '未命名定时任务'),
                'execution_type': metrics.get('execution_type', 'scheduled_task')
            },
            'statistics': {
                'total_lots': row.get('batch_count', 0),
                'execution_time': row.get('execution_time', 0),
                'scheduled_batches': row.get('batch_count', 0),
                'status': 'COMPLETED'  # 能保存的定时任务都是成功的
            },
            'created_at': row.get('timestamp', row.get('created_at')).isoformat() if row.get('timestamp') or row.get('created_at') else None,
            'error_message': None
        }
        
        logger.info(f"✅ 成功获取定时任务历史记录详情: {history_id}")
        return jsonify({
            'success': True,
            'data': result_data
        })
        
    except Exception as e:
        logger.error(f"❌ 获取定时任务历史记录详情失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取定时任务历史记录详情失败: {str(e)}'
        }), 500

@production_bp.route('/scheduling-history/<string:history_id>', methods=['DELETE'])
@login_required
def delete_scheduling_history_record(history_id):
    """删除指定的排产历史记录（支持手动和定时任务）"""
    try:
        logger.info(f"🗑️ 删除历史记录: {history_id}")
        
        # 🔧 修复：判断是手动执行还是定时任务的记录
        if history_id.startswith('scheduled_'):
            # 定时任务记录 - 从schedule_history表删除
            success = _delete_scheduled_task_record(history_id)
        else:
            # 手动执行记录 - 从scheduling_history表删除
            history_manager = get_history_manager()
            success = history_manager.delete_history(history_id)
        
        if success:
            logger.info(f"✅ 成功删除历史记录: {history_id}")
            return jsonify({
                'success': True,
                'message': '历史记录已删除'
            })
        else:
            return jsonify({
                'success': False,
                'error': '历史记录不存在或删除失败'
            }), 404
        
    except Exception as e:
        logger.error(f"❌ 删除历史记录失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'删除历史记录失败: {str(e)}'
        }), 500

def _delete_scheduled_task_record(history_id: str) -> bool:
    """删除定时任务历史记录"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        # 提取数据库ID（移除 'scheduled_' 前缀）
        db_id = history_id.replace('scheduled_', '')
        
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # 删除定时任务历史记录
        cursor.execute("DELETE FROM schedule_history WHERE id = %s", (db_id,))
        affected_rows = cursor.rowcount
        
        conn.commit()
        cursor.close()
        return affected_rows > 0
        
    except Exception as e:
        logger.error(f"❌ 删除定时任务历史记录失败: {e}")
        return False

@production_bp.route('/scheduling-history/clear-all', methods=['DELETE'])
@login_required
def clear_all_scheduling_history():
    """清空所有排产历史记录（包括手动和定时任务记录）"""
    try:
        logger.info("🗑️ 清空所有历史记录（手动 + 定时任务）")
        
        success_count = 0
        
        # 1. 清空手动执行的历史记录（scheduling_history表）
        try:
            history_manager = get_history_manager()
            if history_manager.clear_all_history():
                success_count += 1
                logger.info("✅ 成功清空手动执行历史记录")
        except Exception as e:
            logger.error(f"❌ 清空手动执行历史记录失败: {e}")
        
        # 2. 清空定时任务的历史记录（schedule_history表）
        try:
            from app.utils.db_helper import get_mysql_connection
            
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 清空定时任务历史记录表
            cursor.execute("DELETE FROM schedule_history")
            deleted_scheduled = cursor.rowcount
            
            conn.commit()
            cursor.close()
            success_count += 1
            logger.info(f"✅ 成功清空定时任务历史记录，删除了 {deleted_scheduled} 条记录")
        except Exception as e:
            logger.error(f"❌ 清空定时任务历史记录失败: {e}")
        
        if success_count >= 1:
            logger.info(f"✅ 成功清空历史记录 - 清空了 {success_count}/2 个数据源")
            return jsonify({
                'success': True,
                'message': f'历史记录已清空 ({success_count}/2 个数据源清空成功)'
            })
        else:
            return jsonify({
                'success': False,
                'error': '清空历史记录失败 - 所有数据源清空失败'
            }), 500
        
    except Exception as e:
        logger.error(f"❌ 清空历史记录失败: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'清空历史记录失败: {str(e)}'
        }), 500 