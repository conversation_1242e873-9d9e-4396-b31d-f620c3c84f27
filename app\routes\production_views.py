from sqlalchemy import text
"""
生产管理视图路由 (精简版)
"""
from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required
import logging
import json
from datetime import datetime
from pymysql.cursors import DictCursor

production_views_bp = Blueprint('production_views', __name__,
                               template_folder='templates')

@production_views_bp.route('/production/auto')
@login_required
def production_auto():
    """自动排产页面"""
    return render_template('production/auto.html')

@production_views_bp.route('/production/semi-auto')
@login_required 
def production_semi_auto():
    """手动排产页面"""
    return render_template('production/semi_auto.html')



@production_views_bp.route('/production/priority-settings')
@login_required
def production_priority_settings():
    """优先级设定页面（旧版，保留兼容性）"""
    return render_template('production/priority_settings.html')

@production_views_bp.route('/production/device-priority')
@login_required
def production_device_priority():
    """产品优先级配置页面"""
    return render_template('production/device_priority.html')

@production_views_bp.route('/production/lot-priority')
@login_required
def production_lot_priority():
    """批次优先级配置页面"""
    return render_template('production/lot_priority.html')

@production_views_bp.route('/production/wait-lots')
@login_required
def wait_lots():
    """待排产批次页面"""
    return render_template('production/wait_lots.html')

@production_views_bp.route('/production/done-lots')
@login_required
def done_lots():
    """已排产批次页面"""
    return render_template('production/done_lots.html', 
                         api_endpoint='/api/v2/production/done-lots',
                         table_name='lotprioritydone',
                         page_title='已排产批次',
                         table_title='已排产批次')

@production_views_bp.route('/production/algorithm')
@login_required
def production_algorithm():
    """算法设置页面"""
    return render_template('production/algorithm.html')

@production_views_bp.route('/production/failed-lots')
@login_required
def failed_lots():
    """排产失败清单页面"""
    return render_template('production/failed_lots.html')

# ==================== 排产历史记录API ====================

@production_views_bp.route('/api/production/save-schedule-history', methods=['POST'])
@login_required
def save_schedule_history():
    """保存排产历史记录到数据库"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的数据格式'})
        
        # 连接数据库
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        # 创建一个基础表，如果它不存在
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS schedule_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            algorithm VARCHAR(50) DEFAULT NULL,
            optimization_target VARCHAR(50) DEFAULT NULL,
            total_batches INT DEFAULT NULL,
            scheduled_batches INT DEFAULT NULL,
            execution_time FLOAT DEFAULT NULL,
            status VARCHAR(20) DEFAULT 'success',
            schedule_data JSON DEFAULT NULL,
            metrics_data JSON DEFAULT NULL,
            timestamp DATETIME DEFAULT NULL,
            created_by VARCHAR(50) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        """
        cursor.execute(create_table_sql)

        # 轻量级数据库迁移：检查并添加缺失的列
        def add_column_if_not_exists(cursor, table_name, column_name, column_definition):
            cursor.execute(f"""
                SELECT COUNT(*)
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = '{table_name}'
                AND COLUMN_NAME = '{column_name}'
            """)
            if cursor.fetchone()[0] == 0:
                cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}")
                logging.info(f"✅ 成功为表 '{table_name}' 添加列 '{column_name}'.")

        # 确保新版本字段存在
        add_column_if_not_exists(cursor, 'schedule_history', 'algorithm', 'VARCHAR(50) DEFAULT NULL')
        add_column_if_not_exists(cursor, 'schedule_history', 'optimization_target', 'VARCHAR(50) DEFAULT NULL')
        add_column_if_not_exists(cursor, 'schedule_history', 'total_batches', 'INT DEFAULT NULL')
        add_column_if_not_exists(cursor, 'schedule_history', 'scheduled_batches', 'INT DEFAULT NULL')
        add_column_if_not_exists(cursor, 'schedule_history', 'execution_time', 'FLOAT DEFAULT NULL')
        add_column_if_not_exists(cursor, 'schedule_history', 'status', 'VARCHAR(20) DEFAULT "success"')
        add_column_if_not_exists(cursor, 'schedule_history', 'schedule_data', 'JSON')
        add_column_if_not_exists(cursor, 'schedule_history', 'metrics_data', 'JSON')
        add_column_if_not_exists(cursor, 'schedule_history', 'timestamp', 'DATETIME DEFAULT NULL')
        add_column_if_not_exists(cursor, 'schedule_history', 'created_by', 'VARCHAR(50) DEFAULT NULL')
        
        # 插入历史记录
        insert_sql = """
        INSERT INTO schedule_history (
            algorithm, optimization_target, total_batches, scheduled_batches, 
            execution_time, status, schedule_data, metrics_data, timestamp, created_by
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # 解析输入数据
        schedule_results = data.get('schedule_results', [])
        parameters = data.get('parameters', {})
        statistics = data.get('statistics', {})
        
        # 获取策略信息
        algorithm = parameters.get('strategy', parameters.get('algorithm', 'intelligent'))
        optimization_target = parameters.get('optimization_target', 'balanced')
        
        # 获取批次信息
        total_batches = len(schedule_results)
        scheduled_batches = statistics.get('total_lots', total_batches)
        execution_time = statistics.get('execution_time', 0.0)
        
        # 获取当前用户
        from flask_login import current_user
        created_by = current_user.username if current_user.is_authenticated else 'system'
        
        cursor.execute(insert_sql, (
            algorithm,
            optimization_target,
            total_batches,
            scheduled_batches,
            execution_time,
            'success',
            json.dumps(schedule_results, ensure_ascii=False),
            json.dumps(parameters, ensure_ascii=False),
            datetime.now(),
            created_by
        ))
        
        conn.commit()
        history_id = cursor.lastrowid
        
        cursor.close()
        logging.info(f"✅ 排产历史记录已保存，ID: {history_id}")
        return jsonify({'success': True, 'id': history_id})
        
    except Exception as e:
        logging.error(f"❌ 保存排产历史记录失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@production_views_bp.route('/api/production/get-schedule-history', methods=['GET'])
@login_required
def get_schedule_history():
    """获取排产历史记录列表"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor(DictCursor)  # 使用DictCursor获取字典形式的结果
        
        # 获取最近50条历史记录，修复字段映射
        cursor.execute("""
            SELECT 
                id,
                algorithm,
                optimization_target,
                total_batches,
                scheduled_batches,
                execution_time,
                status,
                schedule_data,
                metrics_data,
                timestamp,
                created_by,
                created_at
            FROM schedule_history
            ORDER BY created_at DESC
            LIMIT 50
        """)
        
        rows = cursor.fetchall()
        history_list = []
        
        # 策略名称映射
        strategy_names = {
            'intelligent': '智能综合',
            'deadline': '交期优先',
            'product': '产品优先',
            'value': '产值优先'
        }
        
        for row in rows:
            try:
                # 获取基本信息
                algorithm = row.get('algorithm', 'unknown')
                total_batches = row.get('total_batches', 0)
                scheduled_batches = row.get('scheduled_batches', 0) 
                execution_time = row.get('execution_time', 0.0)
                
                # 解析JSON数据
                schedule_data_str = row.get('schedule_data')
                metrics_data_str = row.get('metrics_data')
                
                schedule_data = {}
                metrics_data = {}
                
                if schedule_data_str:
                    try:
                        schedule_data = json.loads(schedule_data_str) if isinstance(schedule_data_str, str) else schedule_data_str
                    except (json.JSONDecodeError, TypeError):
                        pass
                        
                if metrics_data_str:
                    try:
                        metrics_data = json.loads(metrics_data_str) if isinstance(metrics_data_str, str) else metrics_data_str
                    except (json.JSONDecodeError, TypeError):
                        pass
                
                # 从多个来源获取策略信息
                strategy = algorithm or metrics_data.get('strategy', 'unknown')
                if strategy in strategy_names:
                    strategy_display = strategy_names[strategy]
                else:
                    strategy_display = strategy
                
                # 从多个来源获取批次数量
                if total_batches == 0 and scheduled_batches > 0:
                    batch_count = scheduled_batches
                elif total_batches > 0:
                    batch_count = total_batches
                else:
                    batch_count = metrics_data.get('total_batches', 0)
                    
                # 从多个来源获取执行时间
                if execution_time == 0.0:
                    execution_time = metrics_data.get('execution_time', 0.0)
                
                history_list.append({
                    'id': row.get('id'),
                    'parameters': {
                        'strategy': strategy_display,
                        'algorithm': strategy,
                        'optimization_target': row.get('optimization_target', 'balanced')
                    },
                    'statistics': {
                        'total_lots': batch_count,
                        'execution_time': execution_time
                    },
                    'created_at': row.get('created_at').isoformat() if row.get('created_at') else None
                })
                
            except Exception as e:
                logging.warning(f"⚠️ 解析历史记录 {row.get('id')} 失败: {e}")
                # 即使解析失败，也添加基础信息
                history_list.append({
                    'id': row.get('id'),
                    'parameters': {'strategy': '解析失败'},
                    'statistics': {'total_lots': 0, 'execution_time': 0.0},
                    'created_at': row.get('created_at').isoformat() if row.get('created_at') else None,
                    'error': '数据解析失败'
                })
                continue
        
        cursor.close()
        return jsonify({'success': True, 'data': history_list})
        
    except Exception as e:
        logging.error(f"❌ 获取排产历史记录失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@production_views_bp.route('/api/production/get-schedule-history/<int:history_id>', methods=['GET'])
@login_required
def get_schedule_history_detail(history_id):
    """获取指定排产历史记录的详细信息"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor(DictCursor) # 使用DictCursor
        
        cursor.execute("""
            SELECT 
                id,
                algorithm,
                optimization_target,
                total_batches,
                scheduled_batches,
                execution_time,
                status,
                schedule_data,
                metrics_data,
                timestamp,
                created_by,
                created_at
            FROM schedule_history
            WHERE id = %s
        """, (history_id,))
        
        row = cursor.fetchone()
        if not row:
            return jsonify({'success': False, 'error': '历史记录不存在'})
        
        try:
            # 解析JSON数据
            schedule_data_str = row.get('schedule_data')
            metrics_data_str = row.get('metrics_data')
            
            schedule_results = []
            parameters = {}
            statistics = {}
            
            # 解析排产结果数据
            if schedule_data_str:
                try:
                    schedule_data = json.loads(schedule_data_str) if isinstance(schedule_data_str, str) else schedule_data_str
                    # 确保返回正确的排产结果格式
                    if isinstance(schedule_data, list):
                        schedule_results = schedule_data
                    elif isinstance(schedule_data, dict) and 'schedule' in schedule_data:
                        schedule_results = schedule_data['schedule']
                    elif isinstance(schedule_data, dict) and 'results' in schedule_data:
                        schedule_results = schedule_data['results']
                except (json.JSONDecodeError, TypeError):
                    schedule_results = []
            
            # 解析参数数据
            if metrics_data_str:
                try:
                    metrics_data = json.loads(metrics_data_str) if isinstance(metrics_data_str, str) else metrics_data_str
                    if isinstance(metrics_data, dict):
                        parameters = metrics_data
                except (json.JSONDecodeError, TypeError):
                    parameters = {}
            
            # 构建参数信息
            algorithm = row.get('algorithm', 'unknown')
            parameters.update({
                'strategy': algorithm,
                'algorithm': algorithm,
                'optimization_target': row.get('optimization_target', 'balanced')
            })
            
            # 构建统计信息
            total_batches = row.get('total_batches', 0)
            scheduled_batches = row.get('scheduled_batches', 0)
            execution_time = row.get('execution_time', 0.0)
            
            # 选择最合适的批次数量
            batch_count = total_batches if total_batches > 0 else scheduled_batches
            if batch_count == 0 and len(schedule_results) > 0:
                batch_count = len(schedule_results)
            
            statistics = {
                'total_lots': batch_count,
                'execution_time': execution_time,
                'scheduled_batches': scheduled_batches,
                'status': row.get('status', 'completed')
            }
            
            history_detail = {
                'schedule_results': schedule_results,
                'parameters': parameters,
                'statistics': statistics,
                'created_at': row.get('created_at').isoformat() if row.get('created_at') else None
            }
            
            cursor.close()
            return jsonify({'success': True, 'data': history_detail})
            
        except Exception as e:
            logging.error(f"❌ 解析历史记录详情失败: {e}")
            return jsonify({'success': False, 'error': '历史记录数据格式错误'})
        
    except Exception as e:
        logging.error(f"❌ 获取排产历史记录详情失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@production_views_bp.route('/api/production/delete-schedule-history/<int:history_id>', methods=['DELETE'])
@login_required
def delete_schedule_history(history_id):
    """删除指定的排产历史记录"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        # 检查记录是否存在
        cursor.execute("SELECT id FROM schedule_history WHERE id = %s", (history_id,))
        if not cursor.fetchone():
            return jsonify({'success': False, 'error': '历史记录不存在'})
        
        # 删除记录
        cursor.execute("DELETE FROM schedule_history WHERE id = %s", (history_id,))
        conn.commit()
        
        cursor.close()
        logging.info(f"✅ 已删除排产历史记录 ID: {history_id}")
        return jsonify({'success': True})
        
    except Exception as e:
        logging.error(f"❌ 删除排产历史记录失败: {e}")
        return jsonify({'success': False, 'error': str(e)})

@production_views_bp.route('/api/production/clear-all-schedule-history', methods=['DELETE'])
@login_required
def clear_all_schedule_history():
    """清空所有排产历史记录"""
    try:
        from app.utils.db_helper import get_mysql_connection
        
        # 使用Flask-SQLAlchemy替代get_db_connection()
        cursor = conn.cursor()
        
        # 使用 TRUNCATE TABLE 高效清空表
        cursor.execute("TRUNCATE TABLE schedule_history")
        conn.commit()
        
        cursor.close()
        logging.info("✅ 已清空所有排产历史记录")
        return jsonify({'success': True})
        
    except Exception as e:
        logging.error(f"❌ 清空排产历史记录失败: {e}")
        return jsonify({'success': False, 'error': str(e)})
