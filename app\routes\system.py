"""
系统设置路由
"""
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user

# 创建系统蓝图
system_bp = Blueprint('system', __name__, url_prefix='/system')

@system_bp.route('/settings')
@login_required
def settings():
    """显示系统设置页面"""
    # 检查用户是否是管理员
    if current_user.role != 'admin':
        flash('您没有权限访问系统设置', 'danger')
        return redirect(url_for('main.index'))
    
    # 渲染系统设置页面
    return render_template('system/settings.html')

@system_bp.route('/database-config')
@login_required  
def database_config():
    """显示统一数据库配置管理页面"""
    # 检查用户是否是管理员
    if current_user.role != 'admin':
        flash('您没有权限访问数据库配置管理', 'danger')
        return redirect(url_for('main.index'))
    
    # 渲染数据库配置管理页面
    return render_template('system/database_config.html')

# 🔧 修复：性能监控路由已移至 app/main/routes.py，避免路由冲突
# @system_bp.route('/performance')
# @login_required
# def performance():
#     """显示性能监控页面"""
#     # 检查用户是否是管理员
#     if current_user.role != 'admin':
#         flash('您没有权限访问性能监控', 'danger')
#         return redirect(url_for('main.index'))
#     
#     # 渲染性能监控页面
#     return render_template('system/performance_monitor.html')

@system_bp.route('/save_settings', methods=['POST'])
@login_required
def save_settings():
    """保存系统设置"""
    # 检查用户是否是管理员
    if current_user.role != 'admin':
        return jsonify({'success': False, 'message': '您没有权限修改系统设置'}), 403
    
    # 获取表单数据
    enable_chatbot = request.form.get('enable_chatbot') == 'on'
    chatbot_token = request.form.get('chatbot_token', '')
    chatbot_server = request.form.get('chatbot_server', '')
    chatbot_color = request.form.get('chatbot_color', '#b72424')
    integration_type = request.form.get('integration_type', 'script')
    
    # 保存设置（这里简化为仅返回成功消息，实际应用中可能需要保存到数据库）
    # TODO: 实现实际的设置保存逻辑
    
    flash('系统设置已保存', 'success')
    return redirect(url_for('main.index')) 