#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
后端定时任务服务 - 替代前端定时任务
基于APScheduler实现的智能排产定时任务管理
"""

import logging
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from flask import current_app
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.executors.pool import ThreadPoolExecutor

logger = logging.getLogger(__name__)

def safe_int(value, default=0):
    """安全的整数转换，避免int + str错误"""
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default

class BackgroundSchedulerService:
    """后端定时任务服务"""
    
    def __init__(self, app=None):
        self.scheduler = None
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化应用"""
        self.app = app
        
        # 配置APScheduler
        jobstores = {
            'default': SQLAlchemyJobStore(url=app.config.get('SQLALCHEMY_DATABASE_URI'))
        }
        
        executors = {
            'default': ThreadPoolExecutor(20),
        }
        
        job_defaults = {
            'coalesce': False,
            'max_instances': 3
        }
        
        # 创建调度器
        self.scheduler = BackgroundScheduler(
            jobstores=jobstores,
            executors=executors,
            job_defaults=job_defaults,
            timezone='Asia/Shanghai'
        )
        
        # 注册应用上下文
        app.extensions['background_scheduler'] = self
    
    def start(self):
        """启动调度器"""
        if self.scheduler and not self.scheduler.running:
            try:
                self.scheduler.start()
                logger.info("✅ 后端定时任务调度器启动成功")
                
                # 添加每日日志邮件发送任务
                self._add_daily_log_email_task()
                
                return True
            except Exception as e:
                logger.error(f"❌ 后端定时任务调度器启动失败: {e}")
                return False
        elif self.scheduler and self.scheduler.running:
            logger.info("✅ 后端定时任务调度器已在运行")
            return True
        return False
    
    def _add_daily_log_email_task(self):
        """添加每日日志邮件发送任务"""
        try:
            from apscheduler.triggers.cron import CronTrigger
            from app.utils.daily_log_mailer import send_daily_logs
            
            # 检查任务是否已存在
            existing_job = self.scheduler.get_job('daily_log_email')
            if existing_job:
                logger.info("📧 每日日志邮件任务已存在")
                return
            
            # 添加每日晚上8点的日志发送任务
            self.scheduler.add_job(
                func=send_daily_logs,
                trigger=CronTrigger(hour=20, minute=0),  # 每天晚上8点
                id='daily_log_email',
                name='每日日志邮件发送',
                replace_existing=True,
                misfire_grace_time=300  # 5分钟容错时间
            )
            
            logger.info("✅ 每日日志邮件任务已添加 - 每天晚上20:00执行")
            
        except Exception as e:
            logger.error(f"❌ 添加每日日志邮件任务失败: {e}")
    
    def shutdown(self):
        """关闭调度器"""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown()
            logger.info("🔄 后端定时任务调度器已关闭")
    
    def create_scheduled_task(self, task_data: Dict) -> Dict:
        """
        创建定时任务
        
        Args:
            task_data: 任务配置数据
            {
                "name": "任务名称",
                "type": "once|daily|weekly|interval",
                "date": "2025-01-01",
                "hour": 9,
                "minute": 0,
                "strategy": "intelligent|deadline|product|value",
                "target": "balanced|makespan|efficiency",
                "autoImport": true,
                "emailNotification": true,
                "weekdays": ["monday", "tuesday"],  # weekly类型
                "intervalValue": 30,  # interval类型
                "intervalUnit": "minutes|hours|days"  # interval类型
            }
        
        Returns:
            Dict: 创建结果
        """
        try:
            # 确保调度器已启动
            if not self.scheduler:
                return {'success': False, 'message': '调度器未初始化'}
            
            if not self.scheduler.running:
                self.start()
            
            task_id = f"scheduled_task_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{task_data.get('name', 'task')}"
            
            # 根据任务类型创建触发器
            trigger = self._create_trigger(task_data)
            if not trigger:
                return {'success': False, 'message': '无效的任务配置'}
            
            # 添加任务到调度器 - 使用静态函数避免序列化问题
            job = self.scheduler.add_job(
                func=execute_scheduled_task_static,
                trigger=trigger,
                args=[task_data],
                id=task_id,
                name=task_data.get('name', '未命名任务'),
                replace_existing=True
            )
            
            # 保存任务配置到数据库
            self._save_task_config(task_id, task_data)
            
            logger.info(f"✅ 定时任务创建成功: {task_data.get('name')} (ID: {task_id})")
            
            # 安全地获取next_run_time
            next_run_time = None
            try:
                if hasattr(job, 'next_run_time') and job.next_run_time:
                    next_run_time = job.next_run_time.isoformat()
            except Exception as e:
                logger.warning(f"⚠️ 获取任务下次运行时间失败: {e}")
            
            return {
                'success': True,
                'message': '任务创建成功',
                'task_id': task_id,
                'next_run_time': next_run_time
            }
            
        except Exception as e:
            logger.error(f"❌ 创建定时任务失败: {e}")
            return {'success': False, 'message': f'创建失败: {str(e)}'}
    
    def _create_trigger(self, task_data: Dict):
        """根据任务类型创建触发器"""
        task_type = task_data.get('type')
        
        if task_type == 'once':
            # 一次性任务
            run_date = datetime.strptime(
                f"{task_data.get('date')} {task_data.get('hour', 9):02d}:{task_data.get('minute', 0):02d}",
                '%Y-%m-%d %H:%M'
            )
            return DateTrigger(run_date=run_date)
            
        elif task_type == 'daily':
            # 每日重复
            return CronTrigger(
                hour=task_data.get('hour', 9),
                minute=task_data.get('minute', 0)
            )
            
        elif task_type == 'weekly':
            # 每周重复
            weekdays = task_data.get('weekdays', [])
            if not weekdays:
                return None
            
            # 转换星期名称为cron格式
            weekday_map = {
                'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
                'friday': 4, 'saturday': 5, 'sunday': 6
            }
            cron_weekdays = [str(weekday_map[day]) for day in weekdays if day in weekday_map]
            
            return CronTrigger(
                day_of_week=','.join(cron_weekdays),
                hour=task_data.get('hour', 9),
                minute=task_data.get('minute', 0)
            )
            
        elif task_type == 'interval':
            # 间隔重复
            interval_value = task_data.get('intervalValue', 1)
            interval_unit = task_data.get('intervalUnit', 'hours')
            
            kwargs = {}
            if interval_unit == 'minutes':
                kwargs['minutes'] = interval_value
            elif interval_unit == 'hours':
                kwargs['hours'] = interval_value
            elif interval_unit == 'days':
                kwargs['days'] = interval_value
            
            return IntervalTrigger(**kwargs)
        
        return None
    
    def _execute_scheduled_task(self, task_data: Dict):
        """执行定时任务"""
        task_name = task_data.get('name', '未命名任务')
        
        try:
            with self.app.app_context():
                logger.info(f"🚀 开始执行定时任务: {task_name}")
                
                # 记录任务开始执行
                execution_id = self._log_task_execution(task_data, 'started')
                
                # 如果启用自动导入，先导入数据
                if task_data.get('autoImport', False):
                    logger.info("📥 执行自动数据导入...")
                    # TODO: 实现自动导入逻辑
                
                # 执行排产
                result = self._execute_scheduling(task_data)
                
                if result.get('success'):
                    logger.info(f"✅ 定时任务执行成功: {task_name}")
                    self._log_task_execution(task_data, 'completed', execution_id, result)
                    
                    # 更新任务的最后执行时间
                    self._update_last_executed(task_data.get('id'), datetime.now())
                    
                    # 发送邮件通知（如果启用）
                    if task_data.get('emailNotification', False):
                        self._send_notification(task_data, result)
                        
                    # 🔧 移除重复调用：Excel自动保存已在静态方法中处理
                    # Excel自动保存在execute_scheduled_task_static中统一处理，此处不再重复调用
                    
                    return result  # 🔧 修复：返回结果
                else:
                    logger.error(f"❌ 定时任务执行失败: {task_name} - {result.get('message')}")
                    self._log_task_execution(task_data, 'failed', execution_id, result)
                    return result  # 🔧 修复：返回结果
                
        except Exception as e:
            logger.error(f"💥 定时任务执行异常: {task_name} - {e}")
            error_result = {'success': False, 'error': str(e)}
            self._log_task_execution(task_data, 'error', None, error_result)
            return error_result  # 🔧 修复：返回错误结果
    
    def _execute_scheduling(self, task_data: Dict) -> Dict:
        """
        执行排产算法 - 已废弃，请使用静态方法
        
        🔧 重要修复：此实例方法已废弃，避免与静态方法重复执行
        APScheduler只调用execute_scheduled_task_static静态方法
        
        Args:
            task_data: 任务数据字典，包含排产参数
            
        Returns:
            Dict: 排产结果（返回失败标记，不执行实际排产）
        """
        # 解析任务参数（仅用于日志）
        strategy = task_data.get('strategy', 'intelligent')
        optimization_target = task_data.get('optimization_target', 'balanced')
        user_id = task_data.get('created_by', 'system')
        
        # 🔧 重要修复：移除重复的排产算法调用
        # 排产算法已在execute_scheduled_task_static → _execute_scheduling_static中执行
        # 此处不再重复调用，避免双重执行和重复保存
        logger.warning("⚠️ 实例方法_execute_scheduling已废弃，排产逻辑在静态方法中统一处理")
        
        # 返回失败结果，不执行实际排产
        return {
            'success': False,
            'message': '实例方法已废弃，请使用静态方法执行',
            'schedule': [],
            'metrics': {
                'total_batches': 0,
                'scheduled_batches': 0,
                'failed_batches': 0,
                'success_rate': '0%',
                'execution_time': 0.0,
                'algorithm': strategy,
                'optimization_target': optimization_target
            },
            'deprecated': True
        }
    
    def _save_task_config(self, task_id: str, task_data: Dict):
        """保存任务配置到数据库"""
        try:
            from app import db
            from sqlalchemy import text
            
            # 插入任务配置记录
            insert_sql = text("""
                INSERT INTO scheduled_tasks (
                    task_id, name, type, config_data, status, created_at, updated_at
                ) VALUES (
                    :task_id, :name, :type, :config_data, :status, :created_at, :updated_at
                )
            """)
            
            db.session.execute(insert_sql, {
                'task_id': task_id,
                'name': task_data.get('name', '未命名任务'),
                'type': task_data.get('type', 'once'),
                'config_data': json.dumps(task_data, ensure_ascii=False),
                'status': 'active',
                'created_at': datetime.now(),
                'updated_at': datetime.now()
            })
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"❌ 保存任务配置失败: {e}")
    
    def _save_schedule_results_to_database(self, scheduled_lots: List[Dict], strategy: str, user_id: str):
        """统一保存排产结果到数据库 - 与手动调度使用相同的逻辑"""
        try:
            from app import db
            from sqlalchemy import text
            
            logger.info(f"💾 定时任务保存 {len(scheduled_lots)} 条排产记录到数据库...")
            
            # 使用与手动调度相同的保存逻辑：保存到lotprioritydone表
            # 清空现有记录
            db.session.execute(text("DELETE FROM lotprioritydone"))
            
            # 插入新记录 - 包含所有字段，特别是排产计算结果字段
            for lot in scheduled_lots:
                insert_sql = text("""
                    INSERT INTO lotprioritydone (
                        PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, 
                        DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE, WIP_STATE, 
                        PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER, 
                        RELEASE_TIME, FAC_ID, CREATE_TIME,
                        match_type, comprehensive_score, processing_time, 
                        changeover_time, algorithm_version, priority_score, 
                        estimated_hours, equipment_status, STEP
                    ) VALUES (
                        :PRIORITY, :HANDLER_ID, :LOT_ID, :LOT_TYPE, :GOOD_QTY, :PROD_ID,
                        :DEVICE, :CHIP_ID, :PKG_PN, :PO_ID, :STAGE, :WIP_STATE,
                        :PROC_STATE, :HOLD_STATE, :FLOW_ID, :FLOW_VER,
                        :RELEASE_TIME, :FAC_ID, :CREATE_TIME,
                        :match_type, :comprehensive_score, :processing_time,
                        :changeover_time, :algorithm_version, :priority_score,
                        :estimated_hours, :equipment_status, :STEP
                    )
                """)
                
                # 使用与手动调度相同的数据映射逻辑
                def _convert_priority_to_int(priority_value) -> int:
                    if priority_value is None:
                        return 1
                    if isinstance(priority_value, (int, float)):
                        return int(priority_value)
                    if isinstance(priority_value, str):
                        priority_str = priority_value.strip()
                        if not priority_str or priority_str.lower() in ['n', 'none', '']:
                            return 1
                        try:
                            return int(float(priority_str))
                        except (ValueError, TypeError):
                            return 1
                    return 1
                
                insert_data = {
                    'HANDLER_ID': lot.get('HANDLER_ID', ''),
                    'LOT_ID': lot.get('LOT_ID', ''),
                    'LOT_TYPE': lot.get('LOT_TYPE', 'lot_wip'),
                    'GOOD_QTY': lot.get('GOOD_QTY', 0),
                    'PROD_ID': lot.get('PROD_ID', ''),
                    'DEVICE': lot.get('DEVICE', ''),
                    'CHIP_ID': lot.get('CHIP_ID', ''),
                    'PKG_PN': lot.get('PKG_PN', ''),
                    'PO_ID': lot.get('PO_ID', ''),
                    'STAGE': lot.get('STAGE', ''),
                    'STEP': lot.get('STEP', ''),
                    'WIP_STATE': lot.get('WIP_STATE', 'QUEUE'),
                    'PROC_STATE': lot.get('PROC_STATE', 'WAIT'),
                    'HOLD_STATE': lot.get('HOLD_STATE', 'N'),
                    'FLOW_ID': lot.get('FLOW_ID', ''),
                    'FLOW_VER': lot.get('FLOW_VER', ''),
                    'RELEASE_TIME': lot.get('RELEASE_TIME'),
                    'FAC_ID': lot.get('FAC_ID', 'FAC1'),
                    'CREATE_TIME': lot.get('CREATE_TIME'),
                    'PRIORITY': _convert_priority_to_int(lot.get('PRIORITY', 1)),
                    'match_type': lot.get('match_type', ''),
                    'comprehensive_score': lot.get('comprehensive_score', 0),
                    'processing_time': lot.get('processing_time', 0),
                    'changeover_time': lot.get('changeover_time', 0),
                    'algorithm_version': f'v2.1-{strategy}-scheduled',  # 标识为定时任务
                    'priority_score': lot.get('priority_score', lot.get('comprehensive_score', 0)),
                    'estimated_hours': lot.get('estimated_hours', lot.get('processing_time', 0)),
                    'equipment_status': lot.get('equipment_status', 'AVAILABLE')
                }
                
                db.session.execute(insert_sql, insert_data)
            
            db.session.commit()
            logger.info(f"✅ 定时任务排产记录保存成功 - 策略: {strategy}, 用户: {user_id}")
            
        except Exception as e:
            logger.error(f"❌ 定时任务保存排产记录失败: {e}")
            db.session.rollback()
            raise e
    
    def _save_schedule_history_unified(self, task_data: Dict, scheduled_lots: List[Dict], result_metrics: Dict, execution_time: float):
        """统一保存排产历史记录 - 与手动调度使用相同的历史记录系统"""
        try:
            from app.models.production.scheduling_history import get_history_manager, SchedulingHistory
            
            # 使用与手动调度相同的历史记录管理器
            history_manager = get_history_manager()
            
            # 创建历史记录
            history_record = SchedulingHistory.create_new_record(
                algorithm=task_data.get('strategy', 'intelligent'),
                optimization_target=task_data.get('optimization_target', 'balanced'),
                user_id=task_data.get('created_by', 'system'),
                parameters={
                    'task_name': task_data.get('name', '定时任务'),
                    'task_type': task_data.get('type', 'scheduled'),
                    'auto_import': task_data.get('autoImport', False),
                    'email_notification': task_data.get('emailNotification', False),
                    'execution_type': 'scheduled_task'  # 标识为定时任务
                }
            )
            
            # 设置输入摘要
            history_record.set_input_summary(
                wait_lots_count=result_metrics.get('total_batches', 0),
                equipment_count=0,
                uph_data_count=0,
                test_specs_count=0
            )
            
            # 保存完整的排产结果数据
            history_record.output_summary = {
                'scheduled_lots_count': result_metrics.get('scheduled_batches', 0),
                'total_good_qty': sum(safe_int(lot.get('GOOD_QTY', 0)) for lot in scheduled_lots),
                'equipment_utilization': {},
                'timestamp': datetime.now().isoformat(),
                'schedule_results': scheduled_lots  # 关键：保存完整的排产结果
            }
            history_record.results_count = result_metrics.get('scheduled_batches', 0)
            
            # 🔧 修复：使用实际的算法执行时间而不是API总时间
            history_record.end_time = datetime.now()
            history_record.status = 'COMPLETED'
            
            # 🔧 修复：手动设置算法执行时间，确保duration_seconds正确
            # 注意：这里使用核心算法执行时间而不是API总时间
            
            # 🔧 确保execution_time是数字类型
            try:
                algorithm_duration = float(execution_time) if execution_time else 1.0
            except (ValueError, TypeError):
                algorithm_duration = 1.0
            
            # 如果算法时间太小（<0.01秒），使用默认时间
            if algorithm_duration < 0.01:
                algorithm_duration = 1.0
            
            # 通过调整start_time来确保duration正确
            history_record.start_time = history_record.end_time - timedelta(seconds=algorithm_duration)
            
            logger.info(f"📝 定时任务历史记录完成设置 - 算法耗时: {algorithm_duration:.2f}s")
            
            # 保存到数据库
            if history_manager.save_history(history_record):
                logger.info(f"✅ 定时任务排产历史记录保存成功: {history_record.history_id}")
            else:
                logger.error(f"❌ 定时任务排产历史记录保存失败: {history_record.history_id}")
            
        except Exception as e:
            logger.error(f"❌ 定时任务保存排产历史记录失败: {e}")
            # 不抛出异常，避免影响主流程
    
    def _log_task_execution(self, task_data: Dict, status: str, execution_id: str = None, result: Dict = None) -> str:
        """记录任务执行日志"""
        try:
            from app import db
            from sqlalchemy import text
            import uuid
            
            if not execution_id:
                execution_id = str(uuid.uuid4())
            
            if status == 'started':
                # 记录开始执行
                insert_sql = text("""
                    INSERT INTO task_execution_logs (
                        execution_id, task_name, status, started_at, config_data
                    ) VALUES (
                        :execution_id, :task_name, :status, :started_at, :config_data
                    )
                """)
                
                db.session.execute(insert_sql, {
                    'execution_id': execution_id,
                    'task_name': task_data.get('name', '未命名任务'),
                    'status': status,
                    'started_at': datetime.now(),
                    'config_data': json.dumps(task_data, ensure_ascii=False)
                })
            else:
                # 更新执行结果
                update_sql = text("""
                    UPDATE task_execution_logs 
                    SET status = :status, finished_at = :finished_at, result_data = :result_data
                    WHERE execution_id = :execution_id
                """)
                
                db.session.execute(update_sql, {
                    'execution_id': execution_id,
                    'status': status,
                    'finished_at': datetime.now(),
                    'result_data': json.dumps(result, ensure_ascii=False) if result else None
                })
            
            db.session.commit()
            return execution_id
            
        except Exception as e:
            logger.error(f"❌ 记录任务执行日志失败: {e}")
            return execution_id or str(uuid.uuid4())
    
    def _save_schedule_history(self, task_data: Dict, scheduled_lots: List[Dict], strategy: str, execution_time: float):
        """已弃用：排产历史记录保存（统一使用新的历史记录系统）"""
        # 🚫 此函数已弃用，避免重复记录
        # 现在使用 _save_schedule_history_unified 统一保存历史记录
        logger.warning("⚠️ _save_schedule_history已弃用，使用_save_schedule_history_unified统一保存历史记录")
        pass

    def _send_notification(self, task_data: Dict, result: Dict):
        """发送邮件通知"""
        try:
            # TODO: 实现邮件通知逻辑
            logger.info(f"📧 发送邮件通知: {task_data.get('name')} 执行完成")
        except Exception as e:
            logger.error(f"❌ 发送邮件通知失败: {e}")
    
    def get_all_tasks(self) -> List[Dict]:
        """获取所有定时任务"""
        try:
            tasks = []
            
            # 从调度器获取任务
            if self.scheduler:
                for job in self.scheduler.get_jobs():
                    try:
                        # 安全地获取next_run_time属性
                        next_run_time = None
                        if hasattr(job, 'next_run_time') and job.next_run_time:
                            next_run_time = job.next_run_time.isoformat()
                        
                        # 🔧 修复：正确获取APScheduler任务的真实状态
                        job_status = 'active'  # 默认状态
                        
                        # 检查任务是否在APScheduler中被暂停
                        try:
                            # 方法1: 检查next_run_time是否为None（暂停的任务通常next_run_time为None）
                            if hasattr(job, 'next_run_time') and job.next_run_time is None:
                                # 进一步验证：尝试从调度器重新获取任务
                                fresh_job = self.scheduler.get_job(job.id)
                                if fresh_job:
                                    # 如果任务存在但next_run_time为None，很可能是被暂停了
                                    # 但也可能是触发器已结束，需要进一步检查
                                    
                                    # 方法2: 尝试检查任务是否有有效的触发器
                                    if hasattr(fresh_job, 'trigger') and fresh_job.trigger:
                                        # 有触发器但next_run_time为None，通常表示被暂停
                                        job_status = 'paused'
                                        logger.debug(f"🔍 任务 {job.id} 检测为暂停状态: 有触发器但next_run_time为None")
                                    else:
                                        # 没有触发器或触发器无效，可能是任务配置错误
                                        job_status = 'error'
                                        logger.debug(f"🔍 任务 {job.id} 检测为错误状态: 触发器无效")
                                else:
                                    # 任务不存在，标记为错误
                                    job_status = 'error'
                            else:
                                # next_run_time存在，任务应该是活跃的
                                job_status = 'active'
                                logger.debug(f"🔍 任务 {job.id} 检测为活跃状态: next_run_time={next_run_time}")
                                
                        except Exception as e:
                            # 如果状态检查失败，记录错误但保持默认状态
                            logger.warning(f"⚠️ 检查任务 {job.id} 状态失败: {e}")
                            job_status = 'error'
                        
                        task_info = {
                            'id': job.id,
                            'name': getattr(job, 'name', job.id),
                            'next_run_time': next_run_time,
                            'trigger': str(job.trigger) if hasattr(job, 'trigger') else 'unknown',
                            'status': job_status  # 🔧 修复：使用真实状态而非硬编码
                        }
                        tasks.append(task_info)
                    except Exception as job_error:
                        logger.warning(f"⚠️ 获取任务信息失败: {job.id} - {job_error}")
                        # 即使单个任务有问题，也添加基本信息
                        tasks.append({
                            'id': job.id,
                            'name': getattr(job, 'name', job.id),
                            'next_run_time': None,
                            'trigger': 'unknown',
                            'status': 'error'
                        })
            
            # 从数据库获取更详细的配置信息
            from app import db
            from sqlalchemy import text
            
            config_sql = text("SELECT task_id, name, type, config_data, status, last_executed FROM scheduled_tasks")
            config_results = db.session.execute(config_sql).fetchall()
            
            config_map = {row[0]: {
                'name': row[1],
                'type': row[2], 
                'config': json.loads(row[3]) if row[3] else {},
                'db_status': row[4],  # 🔧 修复：重命名为db_status以区分数据库状态
                'lastExecuted': row[5].isoformat() if row[5] else None
            } for row in config_results}
            
            # 🔧 修复：合并信息时优先使用APScheduler的真实状态
            for task in tasks:
                if task['id'] in config_map:
                    db_config = config_map[task['id']]
                    # 优先使用APScheduler的状态，数据库状态作为参考
                    apscheduler_status = task['status']
                    db_status = db_config.get('db_status', 'active')
                    
                    # 状态优先级：APScheduler实际状态 > 数据库状态
                    if apscheduler_status in ['paused', 'error']:
                        # APScheduler明确显示暂停或错误，使用APScheduler状态
                        final_status = apscheduler_status
                    elif db_status == 'paused':
                        # 数据库显示暂停，但APScheduler可能还没同步，使用数据库状态
                        final_status = 'paused'
                    else:
                        # 其他情况使用APScheduler状态
                        final_status = apscheduler_status
                    
                    # 更新任务信息
                    task.update({
                        'name': db_config['name'],
                        'type': db_config['type'],
                        'config': db_config['config'],
                        'status': final_status,  # 🔧 修复：使用计算出的最终状态
                        'lastExecuted': db_config['lastExecuted']
                    })
            
            # 🔧 修复：同时检查数据库中存在但APScheduler中不存在的任务（可能是已删除或出错的任务）
            apscheduler_task_ids = {task['id'] for task in tasks}
            for task_id, db_config in config_map.items():
                if task_id not in apscheduler_task_ids:
                    # 数据库中存在但APScheduler中不存在的任务，标记为错误状态
                    tasks.append({
                        'id': task_id,
                        'name': db_config['name'],
                        'type': db_config['type'],
                        'config': db_config['config'],
                        'status': 'error',  # APScheduler中不存在，标记为错误
                        'next_run_time': None,
                        'trigger': 'orphaned',  # 标记为孤立任务
                        'lastExecuted': db_config['lastExecuted'],
                        'error_message': '任务在调度器中不存在，可能需要删除'  # 添加错误信息
                    })
            
            logger.info(f"✅ 获取任务列表成功: 共 {len(tasks)} 个任务")
            return tasks
            
        except Exception as e:
            logger.error(f"❌ 获取任务列表失败: {e}")
            return []
    
    def pause_task(self, task_id: str) -> Dict:
        """暂停任务"""
        try:
            self.scheduler.pause_job(task_id)
            self._update_task_status(task_id, 'paused')
            return {'success': True, 'message': '任务已暂停'}
        except Exception as e:
            return {'success': False, 'message': f'暂停失败: {str(e)}'}
    
    def resume_task(self, task_id: str) -> Dict:
        """恢复任务"""
        try:
            self.scheduler.resume_job(task_id)
            self._update_task_status(task_id, 'active')
            return {'success': True, 'message': '任务已恢复'}
        except Exception as e:
            return {'success': False, 'message': f'恢复失败: {str(e)}'}
    
    def delete_task(self, task_id: str) -> Dict:
        """删除任务"""
        try:
            # 🔧 修复：先尝试从APScheduler中删除任务
            try:
                if self.scheduler and self.scheduler.running:
                    job = self.scheduler.get_job(task_id)
                    if job:
                        self.scheduler.remove_job(task_id)
                        logger.info(f"✅ 从APScheduler中删除任务: {task_id}")
                    else:
                        logger.warning(f"⚠️ 任务在APScheduler中不存在: {task_id}")
                else:
                    logger.warning(f"⚠️ APScheduler未运行，跳过调度器删除: {task_id}")
            except Exception as scheduler_error:
                logger.warning(f"⚠️ 从APScheduler删除任务失败: {task_id} - {scheduler_error}")
            
            # 🔧 修复：无论APScheduler删除是否成功，都要删除数据库配置
            self._delete_task_config(task_id)
            
            return {'success': True, 'message': '任务已删除'}
        except Exception as e:
            logger.error(f"❌ 删除任务失败: {task_id} - {e}")
            return {'success': False, 'message': f'删除失败: {str(e)}'}
    
    def _update_task_status(self, task_id: str, status: str):
        """更新任务状态"""
        try:
            from app import db
            from sqlalchemy import text
            
            update_sql = text("""
                UPDATE scheduled_tasks 
                SET status = :status, updated_at = :updated_at 
                WHERE task_id = :task_id
            """)
            
            db.session.execute(update_sql, {
                'task_id': task_id,
                'status': status,
                'updated_at': datetime.now()
            })
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"❌ 更新任务状态失败: {e}")
    
    def _delete_task_config(self, task_id: str):
        """删除任务配置"""
        try:
            from app import db
            from sqlalchemy import text
            
            delete_sql = text("DELETE FROM scheduled_tasks WHERE task_id = :task_id")
            result = db.session.execute(delete_sql, {'task_id': task_id})
            db.session.commit()
            
            if result.rowcount > 0:
                logger.info(f"✅ 删除任务配置成功: {task_id}")
            else:
                logger.warning(f"⚠️ 任务配置不存在: {task_id}")
            
        except Exception as e:
            logger.error(f"❌ 删除任务配置失败: {e}")
            db.session.rollback()
    
    def cleanup_orphaned_tasks(self) -> Dict:
        """清理孤立任务（数据库中存在但APScheduler中不存在的任务）"""
        try:
            from app import db
            from sqlalchemy import text
            
            # 获取所有任务
            all_tasks = self.get_all_tasks()
            
            # 找出孤立任务
            orphaned_tasks = [task for task in all_tasks if task.get('status') == 'error' and task.get('trigger') == 'orphaned']
            
            if not orphaned_tasks:
                return {'success': True, 'message': '没有发现孤立任务', 'cleaned_count': 0}
            
            # 删除孤立任务
            cleaned_count = 0
            for task in orphaned_tasks:
                try:
                    self._delete_task_config(task['id'])
                    cleaned_count += 1
                except Exception as e:
                    logger.error(f"❌ 清理孤立任务失败: {task['id']} - {e}")
            
            logger.info(f"✅ 清理完成，共清理 {cleaned_count} 个孤立任务")
            
            return {
                'success': True,
                'message': f'清理完成，共清理 {cleaned_count} 个孤立任务',
                'cleaned_count': cleaned_count,
                'total_found': len(orphaned_tasks)
            }
            
        except Exception as e:
            logger.error(f"❌ 清理孤立任务失败: {e}")
            return {'success': False, 'message': f'清理失败: {str(e)}'}
    
    def _update_last_executed(self, task_id: str, execution_time: datetime):
        """更新任务的最后执行时间"""
        try:
            from app import db
            from sqlalchemy import text
            
            # 更新数据库中的最后执行时间
            update_sql = text("""
                UPDATE scheduled_tasks 
                SET last_executed = :last_executed, updated_at = :updated_at
                WHERE task_id = :task_id
            """)
            
            db.session.execute(update_sql, {
                'task_id': task_id,
                'last_executed': execution_time,
                'updated_at': datetime.now()
            })
            db.session.commit()
            
            logger.info(f"✅ 更新任务 {task_id} 最后执行时间: {execution_time}")
            
        except Exception as e:
            logger.error(f"❌ 更新任务最后执行时间失败: {e}")
    
    def _auto_save_excel(self, result: Dict, task_data: Dict, source: str = 'scheduled'):
        """自动保存排产结果为Excel - 统一使用全局Excel自动保存服务"""
        try:
            from app.services.excel_auto_save_service import get_excel_auto_save_service
            
            excel_service = get_excel_auto_save_service()
            
            # 🔧 关键修复：统一使用全局Excel自动保存配置，而不是任务配置
            # 这样即使定时任务未启用，手动排产的Excel自动保存仍然有效
            if not excel_service.is_auto_save_enabled():
                logger.info("ℹ️ 全局Excel自动保存未启用，跳过定时任务Excel保存")
                return
            
            # 获取排产结果数据
            schedule_data = []
            if 'schedule' in result:
                schedule_data = result['schedule']
            elif 'data' in result:
                schedule_data = result['data']
            
            if not schedule_data:
                logger.warning("⚠️ 没有排产结果数据可保存为Excel")
                return
            
            # 构建指标信息
            metrics = result.get('metrics', {})
            metrics.update({
                'execution_source': source,
                'task_name': task_data.get('name', '未命名任务'),
                'task_strategy': task_data.get('strategy', 'intelligent'),
                'execution_time': datetime.now().isoformat(),
                'execution_type': 'scheduled_task'
            })
            
            # 自动保存Excel
            save_result = excel_service.auto_save_schedule_result(
                schedule_data=schedule_data,
                source=source,
                metrics=metrics
            )
            
            if save_result.get('success'):
                logger.info(f"✅ 定时任务排产结果已自动保存为Excel: {save_result.get('filename')} (共{save_result.get('records_count')}条记录)")
            else:
                logger.error(f"❌ 定时任务排产结果Excel自动保存失败: {save_result.get('message')}")
                
        except Exception as e:
            logger.error(f"❌ 排产结果Excel自动保存异常: {e}")

def execute_scheduled_task_static(task_data: Dict):
    """
    静态执行函数 - 避免调度器序列化问题
    这个函数不依赖类实例，可以被APScheduler安全地序列化
    """
    task_name = task_data.get('name', '未命名任务')
    
    # 🔧 修复：获取Flask应用实例
    try:
        # 尝试从全局导入获取应用实例
        from app import app as flask_app
        app = flask_app
    except ImportError:
        try:
            # 如果上面失败，尝试创建新的应用实例
            from app import create_app
            app = create_app()
        except Exception as e:
            logger.error(f"❌ 无法获取Flask应用实例，定时任务 {task_name} 执行失败: {e}")
            return
    
    # 在应用上下文中执行任务
    with app.app_context():
        try:
            logger.info(f"🚀 开始执行定时任务: {task_name}")
            
            # 记录任务开始执行
            execution_id = _log_task_execution_static(task_data, 'started')
            
            # 如果启用自动导入，先导入数据
            if task_data.get('autoImport', False):
                logger.info("📥 执行自动数据导入...")
                # TODO: 实现自动导入逻辑
            
            # 执行排产
            result = _execute_scheduling_static(task_data)
            
            if result.get('success'):
                logger.info(f"✅ 定时任务执行成功: {task_name}")
                _log_task_execution_static(task_data, 'completed', execution_id, result)
                
                # 发送邮件通知（如果启用）
                if task_data.get('emailNotification', False):
                    _send_notification_static(task_data, result)
                
                # 🔧 关键修复：添加Excel自动保存逻辑
                try:
                    _auto_save_excel_static(result, task_data, 'scheduled')
                except Exception as excel_error:
                    logger.error(f"❌ 定时任务Excel自动保存异常: {excel_error}")
                
                return result  # 🔧 修复：返回结果而不是None
            else:
                logger.error(f"❌ 定时任务执行失败: {task_name} - {result.get('message')}")
                _log_task_execution_static(task_data, 'failed', execution_id, result)
                return result  # 🔧 修复：返回结果
            
        except Exception as e:
            logger.error(f"💥 定时任务执行异常: {task_name} - {e}")
            error_result = {'success': False, 'error': str(e)}
            _log_task_execution_static(task_data, 'error', None, error_result)
            return error_result  # 🔧 修复：返回错误结果

def _execute_scheduling_static(task_data: Dict) -> Dict:
    """静态排产执行函数 - 统一与手动调度使用相同的逻辑"""
    try:
        import time
        start_time = time.time()
        
        from app.services.real_scheduling_service import RealSchedulingService
        
        # 创建排产服务实例
        rs = RealSchedulingService()
        
        # 🔧 统一修复：使用与手动调度相同的参数和调用方式
        strategy = task_data.get('strategy', 'intelligent')
        optimization_target = task_data.get('optimization_target', 'balanced')
        user_id = task_data.get('created_by')  # 获取创建任务的用户ID
        
        logger.info(f"🚀 定时任务静态执行排产 - 策略: {strategy}, 优化目标: {optimization_target}, 用户: {user_id}")
        
        # 🔧 关键修复：使用与手动调度完全相同的调用方式
        scheduling_result = rs.execute_real_scheduling(
            algorithm=strategy,  # 使用algorithm参数而不是位置参数
            user_id=user_id,
            optimization_target=optimization_target
        )
        
        # 🔧 兼容性处理：支持新旧两种返回格式（与手动调度保持一致）
        if isinstance(scheduling_result, dict) and 'schedule' in scheduling_result:
            # 新格式：包含完整统计信息
            scheduled_lots = scheduling_result['schedule']
            result_metrics = scheduling_result['metrics']
        else:
            # 旧格式：直接返回列表，需要构造统计信息
            scheduled_lots = scheduling_result if scheduling_result else []
            result_metrics = {
                'total_batches': len(scheduled_lots),
                'scheduled_batches': len(scheduled_lots),
                'failed_batches': 0,
                'success_rate': '100%' if scheduled_lots else '0%',
                'execution_time': 0.0,
                'algorithm': strategy,
                'optimization_target': optimization_target
            }
        
        execution_time = time.time() - start_time
        
        # 🔧 修复：使用与手动调度相同的数据保存逻辑
        if scheduled_lots:
            _save_schedule_results_to_database_static(scheduled_lots, strategy, user_id)
            _save_schedule_history_unified_static(task_data, scheduled_lots, result_metrics, execution_time)
        
        return {
            'success': True,
            'message': f'定时任务排产完成 - 策略: {strategy}, 优化目标: {optimization_target}, 成功: {result_metrics["scheduled_batches"]}/{result_metrics["total_batches"]}',
            'schedule': scheduled_lots,
            'metrics': result_metrics,
            'execution_time': execution_time
        }
        
    except Exception as e:
        logger.error(f"❌ 定时任务静态排产执行失败: {e}", exc_info=True)
        return {
            'success': False,
            'message': f'排产执行失败: {str(e)}',
            'error': str(e)
        }

def _auto_save_excel_static(result: Dict, task_data: Dict, source: str = 'scheduled'):
    """静态Excel自动保存函数 - 统一使用全局Excel自动保存服务"""
    try:
        from app.services.excel_auto_save_service import get_excel_auto_save_service
        
        excel_service = get_excel_auto_save_service()
        
        # 🔧 关键修复：统一使用全局Excel自动保存配置，而不是任务配置
        # 这样即使定时任务未启用，手动排产的Excel自动保存仍然有效
        if not excel_service.is_auto_save_enabled():
            logger.info("ℹ️ 全局Excel自动保存未启用，跳过定时任务Excel保存")
            return
        
        # 获取排产结果数据
        schedule_data = []
        if 'schedule' in result:
            schedule_data = result['schedule']
        elif 'data' in result:
            schedule_data = result['data']
        
        if not schedule_data:
            logger.warning("⚠️ 没有排产结果数据可保存为Excel")
            return
        
        # 构建指标信息
        metrics = result.get('metrics', {})
        metrics.update({
            'execution_source': source,
            'task_name': task_data.get('name', '未命名任务'),
            'task_strategy': task_data.get('strategy', 'intelligent'),
            'execution_time': datetime.now().isoformat(),
            'execution_type': 'scheduled_task'
        })
        
        # 自动保存Excel
        save_result = excel_service.auto_save_schedule_result(
            schedule_data=schedule_data,
            source=source,
            metrics=metrics
        )
        
        if save_result.get('success'):
            logger.info(f"✅ 定时任务排产结果已自动保存为Excel: {save_result.get('filename')} (共{save_result.get('records_count')}条记录)")
        else:
            logger.error(f"❌ 定时任务排产结果Excel自动保存失败: {save_result.get('message')}")
            
    except Exception as e:
        logger.error(f"❌ 定时任务排产结果Excel自动保存异常: {e}")

def _log_task_execution_static(task_data: Dict, status: str, execution_id: str = None, result: Dict = None) -> str:
    """静态任务执行日志记录函数 - 修复应用上下文问题"""
    try:
        # 🔧 修复：完全移除日志记录到数据库的逻辑，避免Flask应用上下文问题
        # 定时任务的日志记录已经通过其他方式处理，这里只需要返回execution_id
        import uuid
        
        if not execution_id:
            execution_id = str(uuid.uuid4())
        
        # 🔧 修复：仅记录到日志文件，不再操作数据库
        if status == 'started':
            logger.info(f"🚀 定时任务开始执行: {task_data.get('name', '未命名任务')} (ID: {execution_id})")
        elif status == 'completed':
            logger.info(f"✅ 定时任务执行完成: {task_data.get('name', '未命名任务')} (ID: {execution_id})")
        elif status == 'failed':
            logger.error(f"❌ 定时任务执行失败: {task_data.get('name', '未命名任务')} (ID: {execution_id})")
        elif status == 'error':
            logger.error(f"💥 定时任务执行异常: {task_data.get('name', '未命名任务')} (ID: {execution_id})")
        
        return execution_id
        
    except Exception as e:
        logger.error(f"❌ 记录任务执行日志失败: {e}")
        return execution_id or str(uuid.uuid4())

def _save_schedule_results_to_database_static(scheduled_lots: List[Dict], strategy: str, user_id: str):
    """静态版本：统一保存排产结果到数据库 - 与手动调度使用相同的逻辑"""
    try:
        from app import db
        from sqlalchemy import text
        
        logger.info(f"💾 定时任务静态保存 {len(scheduled_lots)} 条排产记录到数据库...")
        
        # 使用与手动调度相同的保存逻辑：保存到lotprioritydone表
        # 清空现有记录
        db.session.execute(text("DELETE FROM lotprioritydone"))
        
        # 插入新记录 - 包含所有字段，特别是排产计算结果字段
        for lot in scheduled_lots:
            insert_sql = text("""
                INSERT INTO lotprioritydone (
                    HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, 
                    DEVICE, CHIP_ID, PKG_PN, PO_ID, STAGE, STEP, WIP_STATE, 
                    PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER, 
                    RELEASE_TIME, FAC_ID, CREATE_TIME, PRIORITY,
                    match_type, comprehensive_score, processing_time, 
                    changeover_time, algorithm_version, priority_score, 
                    estimated_hours, equipment_status
                ) VALUES (
                    :HANDLER_ID, :LOT_ID, :LOT_TYPE, :GOOD_QTY, :PROD_ID,
                    :DEVICE, :CHIP_ID, :PKG_PN, :PO_ID, :STAGE, :STEP, :WIP_STATE,
                    :PROC_STATE, :HOLD_STATE, :FLOW_ID, :FLOW_VER,
                    :RELEASE_TIME, :FAC_ID, :CREATE_TIME, :PRIORITY,
                    :match_type, :comprehensive_score, :processing_time,
                    :changeover_time, :algorithm_version, :priority_score,
                    :estimated_hours, :equipment_status
                )
            """)
            
            # 使用与手动调度相同的数据映射逻辑
            def _convert_priority_to_int(priority_value) -> int:
                if priority_value is None:
                    return 1
                if isinstance(priority_value, (int, float)):
                    return int(priority_value)
                if isinstance(priority_value, str):
                    priority_str = priority_value.strip()
                    if not priority_str or priority_str.lower() in ['n', 'none', '']:
                        return 1
                    try:
                        return int(float(priority_str))
                    except (ValueError, TypeError):
                        return 1
                return 1
            
            insert_data = {
                'HANDLER_ID': lot.get('HANDLER_ID', ''),
                'LOT_ID': lot.get('LOT_ID', ''),
                'LOT_TYPE': lot.get('LOT_TYPE', 'lot_wip'),
                'GOOD_QTY': lot.get('GOOD_QTY', 0),
                'PROD_ID': lot.get('PROD_ID', ''),
                'DEVICE': lot.get('DEVICE', ''),
                'CHIP_ID': lot.get('CHIP_ID', ''),
                'PKG_PN': lot.get('PKG_PN', ''),
                'PO_ID': lot.get('PO_ID', ''),
                'STAGE': lot.get('STAGE', ''),
                'STEP': lot.get('STEP', ''),
                'WIP_STATE': lot.get('WIP_STATE', 'QUEUE'),
                'PROC_STATE': lot.get('PROC_STATE', 'WAIT'),
                'HOLD_STATE': lot.get('HOLD_STATE', 'N'),
                'FLOW_ID': lot.get('FLOW_ID', ''),
                'FLOW_VER': lot.get('FLOW_VER', ''),
                'RELEASE_TIME': lot.get('RELEASE_TIME'),
                'FAC_ID': lot.get('FAC_ID', 'FAC1'),
                'CREATE_TIME': lot.get('CREATE_TIME'),
                'PRIORITY': _convert_priority_to_int(lot.get('PRIORITY', 1)),
                'match_type': lot.get('match_type', ''),
                'comprehensive_score': lot.get('comprehensive_score', 0),
                'processing_time': lot.get('processing_time', 0),
                'changeover_time': lot.get('changeover_time', 0),
                'algorithm_version': f'v2.1-{strategy}-scheduled',  # 标识为定时任务
                'priority_score': lot.get('priority_score', lot.get('comprehensive_score', 0)),
                'estimated_hours': lot.get('estimated_hours', lot.get('processing_time', 0)),
                'equipment_status': lot.get('equipment_status', 'AVAILABLE')
            }
            
            db.session.execute(insert_sql, insert_data)
        
        db.session.commit()
        logger.info(f"✅ 定时任务静态排产记录保存成功 - 策略: {strategy}, 用户: {user_id}")
        
    except Exception as e:
        logger.error(f"❌ 定时任务静态保存排产记录失败: {e}")
        db.session.rollback()
        raise e

def _save_schedule_history_unified_static(task_data: Dict, scheduled_lots: List[Dict], result_metrics: Dict, execution_time: float):
    """静态版本：统一保存排产历史记录 - 与手动调度使用相同的历史记录系统"""
    try:
        from app.models.production.scheduling_history import get_history_manager, SchedulingHistory
        
        # 使用与手动调度相同的历史记录管理器
        history_manager = get_history_manager()
        
        # 创建历史记录
        history_record = SchedulingHistory.create_new_record(
            algorithm=task_data.get('strategy', 'intelligent'),
            optimization_target=task_data.get('optimization_target', 'balanced'),
            user_id=task_data.get('created_by', 'system'),
            parameters={
                'task_name': task_data.get('name', '定时任务'),
                'task_type': task_data.get('type', 'scheduled'),
                'auto_import': task_data.get('autoImport', False),
                'email_notification': task_data.get('emailNotification', False),
                'execution_type': 'scheduled_task'  # 标识为定时任务
            }
        )
        
        # 设置输入摘要
        history_record.set_input_summary(
            wait_lots_count=result_metrics.get('total_batches', 0),
            equipment_count=0,
            uph_data_count=0,
            test_specs_count=0
        )
        
        # 保存完整的排产结果数据
        history_record.output_summary = {
            'scheduled_lots_count': result_metrics.get('scheduled_batches', 0),
            'total_good_qty': sum(safe_int(lot.get('GOOD_QTY', 0)) for lot in scheduled_lots),
            'equipment_utilization': {},
            'timestamp': datetime.now().isoformat(),
            'schedule_results': scheduled_lots  # 关键：保存完整的排产结果
        }
        history_record.results_count = result_metrics.get('scheduled_batches', 0)
        
        # 🔧 修复：使用实际的算法执行时间而不是API总时间
        history_record.end_time = datetime.now()
        history_record.status = 'COMPLETED'
        
        # 🔧 修复：手动设置算法执行时间，确保duration_seconds正确
        # 注意：这里使用核心算法执行时间而不是API总时间
        try:
            algorithm_duration = float(execution_time) if execution_time else 0.0
        except (ValueError, TypeError):
            # 如果转换失败，使用默认值
            algorithm_duration = 1.0
        
        # 如果算法时间太小（<0.01秒），使用默认时间
        if algorithm_duration < 0.01:
            algorithm_duration = 1.0
        
        # 通过调整start_time来确保duration正确
        history_record.start_time = history_record.end_time - timedelta(seconds=algorithm_duration)
        
        logger.info(f"📝 定时任务历史记录完成设置 - 算法耗时: {algorithm_duration:.2f}s")
        
        # 保存到数据库
        if history_manager.save_history(history_record):
            logger.info(f"✅ 定时任务静态排产历史记录保存成功: {history_record.history_id}")
        else:
            logger.error(f"❌ 定时任务静态排产历史记录保存失败: {history_record.history_id}")
        
    except Exception as e:
        logger.error(f"❌ 定时任务静态保存排产历史记录失败: {e}")
        # 不抛出异常，避免影响主流程

def _save_schedule_history_static(task_data: Dict, scheduled_lots: List[Dict], strategy: str, execution_time: float):
    """静态保存排产历史记录函数（已弃用 - 使用统一的历史记录系统）"""
    # 此函数已替换为_save_schedule_history_unified_static
    logger.warning("⚠️ _save_schedule_history_static已弃用，请使用_save_schedule_history_unified_static")

def _send_notification_static(task_data: Dict, result: Dict):
    """静态邮件通知函数"""
    try:
        # TODO: 实现邮件通知逻辑
        logger.info(f"📧 发送邮件通知: {task_data.get('name')} 执行完成")
    except Exception as e:
        logger.error(f"❌ 发送邮件通知失败: {e}")

# 全局实例
background_scheduler = BackgroundSchedulerService() 