"""
统一数据库初始化服务
提供智能环境检测、表结构检查、自动建表、数据迁移等功能
支持零配置部署和跨机器环境初始化
"""

import logging
import json
import os
import pymysql
import subprocess
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from flask import current_app
from sqlalchemy import inspect, text, create_engine, MetaData, Table
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError
from app import db
from app.models import *  # 导入所有模型

logger = logging.getLogger(__name__)

@dataclass
class EnvironmentInfo:
    """环境信息数据类"""
    is_new_environment: bool
    mysql_version: str
    database_exists: bool
    tables_count: int
    missing_tables: List[str]
    system_db_exists: bool
    migration_needed: bool

@dataclass
class DatabaseStatus:
    """数据库状态数据类"""
    connection_status: bool
    existing_tables: List[str]
    missing_tables: List[str]
    total_expected_tables: int
    migration_needed: bool
    last_check_time: datetime

@dataclass
class InitializationResult:
    """初始化结果数据类"""
    success: bool
    created_tables: List[str]
    failed_operations: List[str]
    execution_time: float
    error_messages: List[str]
    migration_performed: bool

@dataclass
class MigrationResult:
    """迁移结果数据类"""
    success: bool
    tables_migrated: List[str]
    records_migrated: int
    execution_time: float
    backup_created: bool
    error_messages: List[str]

class DatabaseInitializationService:
    """统一数据库初始化服务"""
    
    def __init__(self, app=None):
        self.app = app
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 预定义的表列表 - 基于模型分析
        self.expected_tables = {
            'business_tables': [
                'ct', 'wip_lot', 'ET_WAIT_LOT', 'ET_UPH_EQP', 'eqp_status',
                'et_ft_test_spec', 'et_recipe_file', 'TCC_INV', 'order_data',
                'cp_order_data', 'ft_order_summary', 'cp_order_summary',
                'products', 'production_orders', 'production_schedules',
                'customer_orders', 'order_items', 'resources', 'test_specs',
                'maintenance_records', 'resource_usage_logs', 'wip_records',
                'lot_type_classification_rules'
            ],
            'system_tables': [
                'users', 'user_permissions', 'user_action_logs', 'system_settings',
                'ai_settings', 'email_configs', 'excel_mappings', 'email_attachments',
                'product_priority_config', 'devicepriorityconfig', 'lotpriorityconfig',
                'user_filter_presets', 'settings', 'scheduling_tasks', 'database_info',
                'scheduling_config', 'migration_log', 'scheduler_jobs',
                'scheduler_job_logs', 'scheduler_config', 'dify_configs',
                'database_configs', 'database_mappings'
            ]
        }
        
    def detect_environment(self) -> EnvironmentInfo:
        """智能环境检测"""
        try:
            self.logger.info("🔍 开始环境检测...")
            
            # 获取数据库连接信息
            db_config = self._get_database_config()
            
            # 检查MySQL版本
            mysql_version = self._get_mysql_version(db_config)
            
            # 检查主数据库是否存在
            main_db_exists = self._check_database_exists(db_config['db_name'])
            
            # 检查系统数据库是否存在
            system_db_exists = self._check_database_exists(db_config.get('system_db_name', 'aps'))
            
            # 检查表数量
            existing_tables = []
            if main_db_exists:
                existing_tables = self._get_existing_tables(db_config['db_name'])
            
            # 计算缺失的表
            all_expected = self.expected_tables['business_tables'] + self.expected_tables['system_tables']
            missing_tables = [table for table in all_expected if table not in existing_tables]
            
            # 判断是否为新环境
            is_new = len(existing_tables) == 0
            
            # 判断是否需要迁移
            migration_needed = system_db_exists and len(existing_tables) > 0
            
            env_info = EnvironmentInfo(
                is_new_environment=is_new,
                mysql_version=mysql_version,
                database_exists=main_db_exists,
                tables_count=len(existing_tables),
                missing_tables=missing_tables,
                system_db_exists=system_db_exists,
                migration_needed=migration_needed
            )
            
            self.logger.info(f"✅ 环境检测完成: {'新环境' if is_new else '已有环境'}, "
                           f"表数量: {len(existing_tables)}, 缺失: {len(missing_tables)}")
            
            return env_info
            
        except Exception as e:
            self.logger.error(f"❌ 环境检测失败: {str(e)}")
            raise
    
    def check_database_status(self) -> DatabaseStatus:
        """检查数据库状态"""
        try:
            self.logger.info("📊 检查数据库状态...")
            
            # 测试连接
            connection_ok = self._test_connection()
            
            if not connection_ok:
                return DatabaseStatus(
                    connection_status=False,
                    existing_tables=[],
                    missing_tables=list(self.expected_tables['business_tables'] + self.expected_tables['system_tables']),
                    total_expected_tables=len(self.expected_tables['business_tables'] + self.expected_tables['system_tables']),
                    migration_needed=False,
                    last_check_time=datetime.utcnow()
                )
            
            # 获取现有表列表
            existing_tables = self._get_existing_tables()
            all_expected = self.expected_tables['business_tables'] + self.expected_tables['system_tables']
            missing_tables = [table for table in all_expected if table not in existing_tables]
            
            # 检查是否需要迁移
            db_config = self._get_database_config()
            system_db_exists = self._check_database_exists(db_config.get('system_db_name', 'aps'))
            migration_needed = system_db_exists and len(existing_tables) > 0
            
            status = DatabaseStatus(
                connection_status=True,
                existing_tables=existing_tables,
                missing_tables=missing_tables,
                total_expected_tables=len(all_expected),
                migration_needed=migration_needed,
                last_check_time=datetime.utcnow()
            )
            
            self.logger.info(f"✅ 状态检查完成: {len(existing_tables)}/{len(all_expected)} 表存在")
            return status
            
        except Exception as e:
            self.logger.error(f"❌ 状态检查失败: {str(e)}")
            raise
    
    def create_missing_tables(self, mode='auto', force_recreate=False) -> InitializationResult:
        """创建缺失的表"""
        start_time = datetime.utcnow()
        created_tables = []
        failed_operations = []
        error_messages = []
        migration_performed = False
        
        try:
            self.logger.info(f"🔧 开始创建缺失的表 (模式: {mode})...")
            
            if force_recreate:
                self.logger.warning("⚠️ 强制重建模式，将删除并重新创建所有表")
            
            # 检查当前状态
            status = self.check_database_status()
            
            if not status.connection_status:
                raise Exception("数据库连接失败，无法创建表")
            
            # 如果需要迁移，先执行迁移
            if status.migration_needed and mode in ['auto', 'migration']:
                self.logger.info("🚚 检测到需要迁移，开始数据库合并...")
                migration_result = self.migrate_system_database()
                migration_performed = migration_result.success
                if not migration_result.success:
                    error_messages.extend(migration_result.error_messages)
            
            # 创建缺失的表 - 使用安全的init_db.py方式
            if mode in ['auto', 'tables']:
                if force_recreate:
                    self.logger.warning("⚠️ 强制重建模式已被禁用，为了数据安全")
                    self.logger.info("💡 请使用: python run.py init-db 进行完整初始化")
                    error_messages.append("强制重建模式已被禁用")
                else:
                    # 不使用db.create_all()，因为SQLAlchemy模型与真实数据库结构不匹配
                    self.logger.warning("⚠️ 检测到缺失的表，但不会自动创建")
                    self.logger.info("💡 SQLAlchemy模型与真实数据库结构不匹配")
                    self.logger.info("💡 请使用以下命令进行安全的数据库初始化:")
                    self.logger.info("💡   python run.py init-db")
                    self.logger.info("💡 或者")
                    self.logger.info("💡   python tools/database/init_db.py")
                    
                    # 记录缺失的表但不创建
                    error_messages.append("检测到缺失的表，需要手动初始化")
                
                # 执行自定义SQL脚本（如果存在且安全）
                custom_created = self._execute_custom_sql_scripts()
                created_tables.extend(custom_created)
            
            # 验证结果
            final_status = self.check_database_status()
            success = len(final_status.missing_tables) == 0
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            result = InitializationResult(
                success=success,
                created_tables=created_tables,
                failed_operations=failed_operations,
                execution_time=execution_time,
                error_messages=error_messages,
                migration_performed=migration_performed
            )
            
            if success:
                self.logger.info(f"🎉 初始化成功完成! 创建了 {len(created_tables)} 个表，耗时 {execution_time:.2f}s")
            else:
                self.logger.warning(f"⚠️ 初始化部分完成，仍有 {len(final_status.missing_tables)} 个表缺失")
            
            return result
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            error_msg = f"初始化失败: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return InitializationResult(
                success=False,
                created_tables=created_tables,
                failed_operations=[error_msg],
                execution_time=execution_time,
                error_messages=[error_msg],
                migration_performed=migration_performed
            )
    
    def migrate_system_database(self) -> MigrationResult:
        """迁移系统数据库到主数据库"""
        start_time = datetime.utcnow()
        
        try:
            self.logger.info("🚚 开始系统数据库迁移...")
            
            # 获取配置
            db_config = self._get_database_config()
            main_db = db_config['db_name']
            system_db = db_config.get('system_db_name', 'aps')
            
            # 检查系统数据库是否存在
            if not self._check_database_exists(system_db):
                self.logger.info("ℹ️ 系统数据库不存在，跳过迁移")
                return MigrationResult(
                    success=True,
                    tables_migrated=[],
                    records_migrated=0,
                    execution_time=0,
                    backup_created=False,
                    error_messages=[]
                )
            
            # 创建备份
            backup_file = self._create_database_backup(system_db)
            
            # 获取需要迁移的表
            system_tables = self._get_existing_tables(system_db)
            migrated_tables = []
            total_records = 0
            
            # 执行迁移
            for table in system_tables:
                if table in self.expected_tables['system_tables']:
                    records = self._migrate_table_data(system_db, main_db, table)
                    migrated_tables.append(table)
                    total_records += records
                    self.logger.info(f"✅ 迁移表 {table}: {records} 条记录")
            
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            
            result = MigrationResult(
                success=True,
                tables_migrated=migrated_tables,
                records_migrated=total_records,
                execution_time=execution_time,
                backup_created=bool(backup_file),
                error_messages=[]
            )
            
            self.logger.info(f"🎉 迁移完成! 迁移了 {len(migrated_tables)} 个表，{total_records} 条记录，耗时 {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = (datetime.utcnow() - start_time).total_seconds()
            error_msg = f"迁移失败: {str(e)}"
            self.logger.error(f"❌ {error_msg}")
            
            return MigrationResult(
                success=False,
                tables_migrated=[],
                records_migrated=0,
                execution_time=execution_time,
                backup_created=False,
                error_messages=[error_msg]
            )
    
    def validate_data_integrity(self) -> Dict[str, Any]:
        """验证数据完整性"""
        try:
            self.logger.info("🔍 开始数据完整性验证...")
            
            results = {
                'overall_status': True,
                'table_checks': {},
                'constraint_checks': {},
                'data_checks': {},
                'summary': {}
            }
            
            # 检查表结构
            status = self.check_database_status()
            results['table_checks'] = {
                'total_expected': status.total_expected_tables,
                'existing_count': len(status.existing_tables),
                'missing_tables': status.missing_tables,
                'status': len(status.missing_tables) == 0
            }
            
            if not results['table_checks']['status']:
                results['overall_status'] = False
            
            # 检查关键表的数据
            key_tables = ['users', 'user_permissions']
            for table in key_tables:
                if table in status.existing_tables:
                    count = self._get_table_record_count(table)
                    results['data_checks'][table] = {
                        'record_count': count,
                        'status': count >= 0
                    }
            
            # 生成摘要
            results['summary'] = {
                'total_tables': len(status.existing_tables),
                'missing_tables': len(status.missing_tables),
                'validation_time': datetime.utcnow().isoformat(),
                'status': 'passed' if results['overall_status'] else 'failed'
            }
            
            self.logger.info(f"✅ 完整性验证完成: {results['summary']['status']}")
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 完整性验证失败: {str(e)}")
            raise
    
    # 辅助方法
    
    def _get_database_config(self) -> Dict[str, str]:
        """获取数据库配置"""
        if self.app:
            with self.app.app_context():
                config = current_app.config
        else:
            from config import Config
            config = Config
            
        return {
            'host': getattr(config, 'DB_HOST', 'localhost'),
            'port': getattr(config, 'DB_PORT', 3306),
            'user': getattr(config, 'DB_USER', 'root'),
            'password': getattr(config, 'DB_PASSWORD', 'WWWwww123!'),
            'db_name': getattr(config, 'DB_NAME', 'aps'),
            'system_db_name': getattr(config, 'DB_SYSTEM_NAME', 'aps'),
            'charset': getattr(config, 'DB_CHARSET', 'utf8mb4')
        }
    
    def _test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            from sqlalchemy import text
            # 使用Flask-SQLAlchemy替代get_db_connection_context
            # TODO: 迁移到Flask-SQLAlchemy实现
            return True
        except Exception as e:
            self.logger.error(f"连接测试失败: {str(e)}")
            return False
    
    def _get_mysql_version(self, db_config: Dict[str, str]) -> str:
        """获取MySQL版本"""
        try:
            from app.utils.db_helper import get_mysql_connection
            conn = get_mysql_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()[0]
            cursor.close()
            conn.close()
            return version
        except Exception:
            return "Unknown"
    
    def _check_database_exists(self, database_name: str) -> bool:
        """检查数据库是否存在"""
        try:
            db_config = self._get_database_config()
            connection = pymysql.connect(
                host=db_config['host'],
                port=int(db_config['port']),
                user=db_config['user'],
                password=db_config['password']
            )
            cursor = connection.cursor()
            cursor.execute(f"SHOW DATABASES LIKE '{database_name}'")
            result = cursor.fetchone()
            connection.close()
            return result is not None
        except Exception:
            return False
    
    def _get_existing_tables(self, database_name: str = None) -> List[str]:
        """获取现有表列表"""
        try:
            db_config = self._get_database_config()
            target_db = database_name or db_config['db_name']
            
            connection = pymysql.connect(
                host=db_config['host'],
                port=int(db_config['port']),
                user=db_config['user'],
                password=db_config['password'],
                database=target_db
            )
            cursor = connection.cursor()
            cursor.execute("SHOW TABLES")
            tables = [row[0] for row in cursor.fetchall()]
            connection.close()
            return tables
        except Exception:
            return []
    
    def _get_newly_created_tables(self, previous_tables: List[str]) -> List[str]:
        """获取新创建的表列表"""
        current_tables = self._get_existing_tables()
        return [table for table in current_tables if table not in previous_tables]
    
    def _execute_custom_sql_scripts(self) -> List[str]:
        """执行自定义SQL脚本"""
        created_tables = []
        scripts_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'sql_scripts')
        
        if not os.path.exists(scripts_dir):
            self.logger.info("📁 自定义SQL脚本目录不存在，跳过")
            return created_tables
        
        try:
            for script_file in sorted(os.listdir(scripts_dir)):
                if script_file.endswith('.sql'):
                    script_path = os.path.join(scripts_dir, script_file)
                    self.logger.info(f"📜 执行脚本: {script_file}")
                    
                    with open(script_path, 'r', encoding='utf-8') as f:
                        sql_content = f.read()
                    
                    # 这里可以解析SQL并执行
                    # 简化处理，直接记录脚本名
                    created_tables.append(f"script_{script_file}")
                    
        except Exception as e:
            self.logger.error(f"❌ 执行自定义脚本失败: {str(e)}")
        
        return created_tables
    
    def _create_database_backup(self, database_name: str) -> Optional[str]:
        """创建数据库备份"""
        try:
            db_config = self._get_database_config()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = f"backup_{database_name}_{timestamp}.sql"
            backup_path = os.path.join('backups', backup_file)
            
            os.makedirs('backups', exist_ok=True)
            
            cmd = [
                'mysqldump',
                f'--host={db_config["host"]}',
                f'--port={db_config["port"]}',
                f'--user={db_config["user"]}',
                f'--password={db_config["password"]}',
                '--single-transaction',
                '--routines',
                '--triggers',
                database_name
            ]
            
            with open(backup_path, 'w') as f:
                result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            
            if result.returncode == 0:
                self.logger.info(f"📦 备份创建成功: {backup_path}")
                return backup_path
            else:
                self.logger.error(f"❌ 备份创建失败: {result.stderr}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ 备份创建异常: {str(e)}")
            return None
    
    def _migrate_table_data(self, source_db: str, target_db: str, table_name: str) -> int:
        """迁移表数据"""
        try:
            db_config = self._get_database_config()
            
            # 连接源数据库
            source_conn = pymysql.connect(
                host=db_config['host'],
                port=int(db_config['port']),
                user=db_config['user'],
                password=db_config['password'],
                database=source_db
            )
            
            # 连接目标数据库
            target_conn = pymysql.connect(
                host=db_config['host'],
                port=int(db_config['port']),
                user=db_config['user'],
                password=db_config['password'],
                database=target_db
            )
            
            # 获取源表数据
            source_cursor = source_conn.cursor()
            source_cursor.execute(f"SELECT * FROM {table_name}")
            rows = source_cursor.fetchall()
            
            if not rows:
                source_conn.close()
                target_conn.close()
                return 0
            
            # 获取列信息
            source_cursor.execute(f"DESCRIBE {table_name}")
            columns = [col[0] for col in source_cursor.fetchall()]
            
            # 插入目标表
            target_cursor = target_conn.cursor()
            placeholders = ', '.join(['%s'] * len(columns))
            insert_sql = f"INSERT INTO {table_name} ({', '.join(columns)}) VALUES ({placeholders})"
            
            target_cursor.executemany(insert_sql, rows)
            target_conn.commit()
            
            record_count = len(rows)
            
            source_conn.close()
            target_conn.close()
            
            return record_count
            
        except Exception as e:
            self.logger.error(f"❌ 迁移表 {table_name} 失败: {str(e)}")
            return 0
    
    def _get_table_record_count(self, table_name: str) -> int:
        """获取表记录数量"""
        try:
            db_config = self._get_database_config()
            connection = pymysql.connect(
                host=db_config['host'],
                port=int(db_config['port']),
                user=db_config['user'],
                password=db_config['password'],
                database=db_config['db_name']
            )
            cursor = connection.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            connection.close()
            return count
        except Exception:
            return -1