#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强数据源管理器 - 简化版本
支持动态字段发现、多数据源整合等功能
"""

import logging
from typing import Dict, List, Optional, Any
from app.services.dynamic_field_manager import get_field_manager

logger = logging.getLogger(__name__)

class EnhancedDataSourceManager:
    """增强数据源管理器"""
    
    def __init__(self):
        """初始化增强数据源管理器"""
        self.field_manager = get_field_manager()
        self.current_source = 'mysql'
        self._cache = {}
        self._cache_timeout = 300  # 5分钟缓存
        self._db_config_cache = None  # 缓存数据库配置
    
    def get_table_data(self, table_name: str, page: int = 1, per_page: int = 50, 
                      filters: Optional[List] = None, sort_by: str = '', 
                      sort_order: str = 'asc', load_all: bool = False) -> Dict:
        """获取表格数据 - 动态字段支持，支持按需加载"""
        try:
            # 获取表信息（自动发现字段）
            table_info = self.field_manager.get_table_info(table_name)
            if not table_info:
                return {
                    'success': False,
                    'error': f'无法获取表信息: {table_name}'
                }
            
            # 根据load_all参数决定是否加载全量数据
            if load_all:
                # 导出等操作需要全量数据
                data, total = self._fetch_table_data_all(table_name, table_info, filters, sort_by, sort_order)
                paged_data = data
            else:
                # 页面显示使用分页查询
                data, total = self._fetch_table_data_paged(table_name, table_info, page, per_page, filters, sort_by, sort_order)
                paged_data = data

            # 处理字段显示规则
            paged_data = self._apply_display_rules(paged_data, table_info)
            
            # 获取显示字段（排除隐藏字段） - 修复类型错误
            display_fields = []
            for field in table_info.get('fields', []):
                if isinstance(field, dict):
                    # 字典格式的字段定义
                    if not field.get('hidden', False):
                        display_fields.append(field)
                else:
                    # 字符串格式的字段名
                    display_fields.append({'name': str(field), 'field': str(field)})
            
            return {
                'success': True,
                'data': paged_data,
                'total': total,
                'page': page,
                'per_page': per_page,
                'fields': display_fields,
                'table_info': table_info
            }
        except Exception as e:
            logger.error(f"❌ 获取表数据失败 ({table_name}): {e}")
            return {
                'success': False,
                'error': str(e),
                'data': [],
                'total': 0
            }
    
    def _fetch_table_data_all(self, table_name: str, table_info: Dict, 
                             filters: Optional[List] = None, sort_by: str = '', 
                             sort_order: str = 'asc') -> tuple:
        """获取全量表数据（不分页）"""
        try:
            # 使用连接池上下文管理器 - 修复连接泄漏
            from app.utils.db_helper import get_mysql_connection_context
            with get_mysql_connection_context() as conn:
                cursor = conn.cursor()

                # 构建基础SQL
                primary_key = table_info.get('primary_key', 'id')
                base_where = f"WHERE {primary_key} IS NOT NULL"
            
            # 构建筛选条件（与分页查询相同逻辑）
            filter_conditions = []
            filter_params = []
            if filters:
                for filter_item in filters:
                    field = filter_item.get('field')
                    operator = filter_item.get('operator', 'contains')
                    value = filter_item.get('value')
                    
                    if field and value is not None:
                        if operator == 'contains':
                            filter_conditions.append(f"{field} LIKE %s")
                            filter_params.append(f"%{value}%")
                        elif operator == 'equals':
                            filter_conditions.append(f"{field} = %s")
                            filter_params.append(value)
                        elif operator == 'starts_with':
                            filter_conditions.append(f"{field} LIKE %s")
                            filter_params.append(f"{value}%")
                        elif operator == 'ends_with':
                            filter_conditions.append(f"{field} LIKE %s")
                            filter_params.append(f"%{value}")
            
            # 组合筛选条件
            if filter_conditions:
                where_clause = f"{base_where} AND " + " AND ".join(filter_conditions)
            else:
                where_clause = base_where
            
            # 排序条件
            order_clause = ""
            if sort_by and sort_by in [field.get('name', '') if isinstance(field, dict) else str(field) for field in table_info.get('fields', [])]:
                order_direction = 'DESC' if sort_order.lower() == 'desc' else 'ASC'
                order_clause = f"ORDER BY {sort_by} {order_direction}"
            
            # 执行查询
            query = f"SELECT * FROM {table_name} {where_clause} {order_clause}"
            cursor.execute(query, filter_params)
            result = cursor.fetchall()
            
            # 处理数据
            data = []
            for row in result:
                if isinstance(row, dict):
                    data.append(row)
                else:
                    # 转换为字典格式
                    field_names = [field.get('name', '') if isinstance(field, dict) else str(field) for field in table_info.get('fields', [])]
                    row_dict = {}
                    for i, value in enumerate(row):
                        if i < len(field_names):
                            row_dict[field_names[i]] = value
                    data.append(row_dict)
            
            cursor.close()
            conn.close()
            
            return data, len(data)
            
        except Exception as e:
            logger.error(f"❌ 获取全量数据失败 ({table_name}): {e}")
            # TODO: 临时返回空列表，避免语法错误
            return [], 0
    
    def _fetch_table_data_paged(self, table_name: str, table_info: Dict, page: int, 
                               per_page: int, filters: Optional[List] = None, 
                               sort_by: str = '', sort_order: str = 'asc') -> tuple:
        """获取分页表数据"""
        try:
            # 使用连接池上下文管理器 - 修复连接泄漏
            from app.utils.db_helper import get_mysql_connection_context
            with get_mysql_connection_context() as conn:
                cursor = conn.cursor()

                # 计算OFFSET
                offset = (page - 1) * per_page

                # 构建基础WHERE条件
                primary_key = table_info.get('primary_key', 'id')
                base_where = f"WHERE {primary_key} IS NOT NULL"
            
            # 构建筛选条件
            filter_conditions = []
            filter_params = []
            if filters:
                for filter_item in filters:
                    field = filter_item.get('field')
                    operator = filter_item.get('operator', 'contains')
                    value = filter_item.get('value')
                    
                    if field and value is not None:
                        if operator == 'contains':
                            filter_conditions.append(f"{field} LIKE %s")
                            filter_params.append(f"%{value}%")
                        elif operator == 'equals':
                            filter_conditions.append(f"{field} = %s")
                            filter_params.append(value)
            
            # 组合筛选条件
            if filter_conditions:
                where_clause = f"{base_where} AND " + " AND ".join(filter_conditions)
            else:
                where_clause = base_where
            
            # 排序条件
            order_clause = ""
            if sort_by and sort_by in [field.get('name', '') if isinstance(field, dict) else str(field) for field in table_info.get('fields', [])]:
                order_direction = 'DESC' if sort_order.lower() == 'desc' else 'ASC'
                order_clause = f"ORDER BY {sort_by} {order_direction}"
            
            # 获取总数
            count_query = f"SELECT COUNT(*) FROM {table_name} {where_clause}"
            cursor.execute(count_query, filter_params)
            total = cursor.fetchone()[0]
            
            # 获取分页数据
            data_query = f"SELECT * FROM {table_name} {where_clause} {order_clause} LIMIT {per_page} OFFSET {offset}"
            cursor.execute(data_query, filter_params)
            result = cursor.fetchall()
            
            # 处理数据
            data = []
            for row in result:
                if isinstance(row, dict):
                    data.append(row)
                else:
                    # 转换为字典格式
                    field_names = [field.get('name', '') if isinstance(field, dict) else str(field) for field in table_info.get('fields', [])]
                    row_dict = {}
                    for i, value in enumerate(row):
                        if i < len(field_names):
                            row_dict[field_names[i]] = value
                    data.append(row_dict)
            
            cursor.close()
            conn.close()
            
            return data, total
            
        except Exception as e:
            logger.error(f"❌ 获取分页数据失败 ({table_name}): {e}")
            # TODO: 临时返回空列表，避免语法错误
            return [], 0
    
    def _apply_display_rules(self, data: List[Dict], table_info: Dict) -> List[Dict]:
        """应用字段显示规则"""
        # 简化实现：直接返回数据
        return data
    
    def get_table_columns(self, table_name: str) -> Dict:
        """获取表列信息"""
        try:
            table_info = self.field_manager.get_table_info(table_name)
            if table_info:
                return {
                    'success': True,
                    'columns': table_info.get('fields', []),
                    'table_name': table_name
                }
            else:
                return {
                    'success': False,
                    'error': f'无法获取表信息: {table_name}',
                    'columns': []
                }
        except Exception as e:
            logger.error(f"❌ 获取表列信息失败 ({table_name}): {e}")
            return {
                'success': False,
                'error': str(e),
                'columns': []
            }

# 全局实例
enhanced_manager = EnhancedDataSourceManager()

def get_enhanced_manager():
    """获取增强数据源管理器实例"""
    return enhanced_manager
