#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实业务逻辑的智能排产服务 - 优化版
基于业务规则的专业智能排产算法

核心特性：
1. 基于数据库匹配规则的三级设备匹配策略
2. 智能评分系统与综合排产算法
3. 完善的缓存机制与性能优化
4. 详细的日志记录与错误处理
5. 🔥 集成失败跟踪系统，实时记录排产失败批次
"""

import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from collections import defaultdict

logger = logging.getLogger(__name__)

class RealSchedulingService:
    """基于真实业务逻辑的智能排产服务 - 优化版"""
    
    def __init__(self):
        from app.services.data_source_manager import DataSourceManager
        from app.services.multilevel_cache_manager import multilevel_cache
        from app.services.intelligent_cache_adapter import IntelligentCacheAdapter
        from scheduling_failure_fix import SchedulingFailureTracker
        
        self.data_manager = DataSourceManager()
        self.equipment_workload = {}  # 设备工作负载跟踪
        
        # 🚀 Task 2.2：集成多级缓存系统
        self.multilevel_cache = multilevel_cache
        self.cache_adapter = IntelligentCacheAdapter(multilevel_cache)
        
        # 🔥 集成失败跟踪系统
        self.failure_tracker = SchedulingFailureTracker()
        logger.info("🎯 失败跟踪系统已集成到排产服务")
        
        # 🚀 优化：计算结果缓存
        self._computation_cache = {
            'lot_requirements': {},
            'equipment_matches': {},
            'score_calculations': {}
        }
        
        # 🚀 优化：性能统计
        self._performance_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'db_queries': 0,
            'computation_time': 0
        }
        
        # 设备匹配规则配置 (严格按照SQL规则)
        self.match_rules = {
            'same_setup': {
                'name': '同设置匹配',
                'score': 100,
                'changeover_time': 0,
                'description': '同设置：KIT_PN + TB_PN + HB_PN相同'
            },
            'small_change': {
                'name': '小改机匹配', 
                'score': 80,
                'changeover_time': 45,
                'description': '小改机：KIT_PN相同'
            },
            'big_change': {
                'name': '大改机匹配',
                'score': 60,
                'changeover_time': 120,
                'description': '大改机：HANDLER_CONFIG相同'
            }
        }
        
        # 🎯 权重配置：由前端用户策略系统动态提供，这里仅作为最后的后备配置
        self.default_weights = {
            'tech_match_weight': 25.0,
            'load_balance_weight': 20.0,
            'deadline_weight': 25.0,
            'value_efficiency_weight': 20.0,
            'business_priority_weight': 10.0
        }

        
        # 优先级配置缓存
        self._device_priority_cache = None
        self._lot_priority_cache = None
        self._uph_cache = None
        self._cache_timestamp = None
        self._uph_cache_timestamp = None
        self._uph_cache_timeout = 300
        
        # 🚀 新增：智能懒加载机制
        self._lazy_cache = {}
        self._preload_timestamp = None
        self._preload_context = None
        self._data_access_patterns = {}  # 跟踪数据访问模式
        self._cache_ttl = {  # 🚀 优化：统一快速刷新策略
            'config_data': 10,        # 配置数据10秒 - 快速响应配置变化
            'business_data': 10,      # 业务数据10秒 - 快速响应业务变化
            'static_data': 10         # 静态数据10秒 - 统一刷新策略
        }

    def _preload_all_data_with_multilevel_cache(self) -> Dict:
        """
        🚀 Task 2.2：使用多级缓存的数据预加载
        优先从缓存获取，缓存未命中时加载并缓存
        """
        logger.info("🚀 开始多级缓存数据预加载...")
        start_time = time.time()
        
        # 定义所有需要的数据源及其缓存配置
        data_sources = {
            'device_priority': {
                'fetch_func': self._get_device_priority_data,
                'data_type': 'CONFIG_DATA',
                'cache_key': 'scheduling_device_priority'
            },
            'lot_priority': {
                'fetch_func': self._get_lot_priority_data,
                'data_type': 'CONFIG_DATA', 
                'cache_key': 'scheduling_lot_priority'
            },
            'test_specs': {
                'fetch_func': self._get_test_specs_data,
                'data_type': 'BUSINESS_DATA',
                'cache_key': 'scheduling_test_specs'
            },
            'equipment_status': {
                'fetch_func': self._get_equipment_status_data,
                'data_type': 'BUSINESS_DATA',
                'cache_key': 'scheduling_equipment_status'
            },
            'uph_data': {
                'fetch_func': self._get_uph_data,
                'data_type': 'BUSINESS_DATA',
                'cache_key': 'scheduling_uph_data'
            },
            'recipe_files': {
                'fetch_func': self._get_recipe_files_data,
                'data_type': 'BUSINESS_DATA',
                'cache_key': 'scheduling_recipe_files'
            },
            'stage_mapping_config': {
                'fetch_func': self._get_stage_mapping_data,
                'data_type': 'CONFIG_DATA',
                'cache_key': 'scheduling_stage_mappings'
            },
            'wait_lots': {
                'fetch_func': self._get_wait_lots_data,
                'data_type': 'BUSINESS_DATA',
                'cache_key': 'scheduling_wait_lots'
            }
        }
        
        preloaded_data = {}
        cache_hits = 0
        cache_misses = 0
        
        from app.services.multilevel_cache_manager import DataType
        
        for key, config in data_sources.items():
            try:
                # 构建缓存键
                cache_key = config['cache_key']
                data_type = getattr(DataType, config['data_type'])
                
                # 使用多级缓存的get_or_compute方法
                data = self.multilevel_cache.get_or_compute(
                    key=cache_key,
                    compute_func=config['fetch_func'],
                    data_type=data_type
                )
                
                preloaded_data[key] = data
                
                # 统计缓存命中情况
                if self.multilevel_cache.get(cache_key, data_type) is not None:
                    cache_hits += 1
                    logger.debug(f"🎯 缓存命中: {key}")
                else:
                    cache_misses += 1
                    logger.debug(f"🔄 缓存未命中，重新加载: {key}")
                    
                data_count = len(data) if isinstance(data, list) else ('dict' if isinstance(data, dict) else 'data')
                logger.debug(f"✅ 预加载 {key}: {data_count}")
                
            except Exception as e:
                logger.warning(f"⚠️ 预加载 {key} 失败: {e}")
                preloaded_data[key] = [] if 'data' in key else {}
                cache_misses += 1
        
        load_time = time.time() - start_time
        cache_hit_rate = (cache_hits / (cache_hits + cache_misses)) * 100 if (cache_hits + cache_misses) > 0 else 0
        
        logger.info(f"✅ 多级缓存数据预加载完成，耗时: {load_time:.2f}s, 缓存命中率: {cache_hit_rate:.1f}%")
        
        # 🔧 修复：添加数据别名映射，解决键名不匹配问题
        # 修复STAGE映射配置的键名不匹配：stage_mapping_config -> stage_mappings
        if 'stage_mapping_config' in preloaded_data:
            preloaded_data['stage_mappings'] = preloaded_data['stage_mapping_config']
            logger.debug(f"✅ 添加STAGE映射别名: stage_mappings -> {len(preloaded_data['stage_mappings'])}条记录")
        
        return preloaded_data

    def _get_data_on_demand(self, data_type: str, context: Dict = None) -> Dict:
        """
        🚀 智能懒加载机制：按需获取数据，只在需要时加载
        
        Args:
            data_type: 数据类型 ('test_specs', 'equipment_status', 'uph_data', 等)
            context: 上下文信息，用于优化加载策略
        
        Returns:
            Dict: 数据字典
        """
        current_time = time.time()
        
        # 1. 构建缓存键
        context_hash = hash(str(sorted((context or {}).items()))) if context else 0
        cache_key = f"{data_type}_{context_hash}"
        
        # 2. 检查缓存有效性
        if cache_key in self._lazy_cache:
            cache_data = self._lazy_cache[cache_key]
            cache_time = cache_data.get('timestamp', 0)
            ttl = self._get_data_ttl(data_type)
            
            if current_time - cache_time < ttl:
                # 更新访问模式统计
                self._update_access_pattern(data_type, 'cache_hit')
                logger.debug(f"🎯 懒加载缓存命中: {data_type}")
                return cache_data['data']
        
        # 3. 缓存未命中或过期，重新加载
        logger.info(f"🔄 懒加载：重新加载 {data_type}")
        
        # 根据数据类型调用对应的获取函数
        fetch_func = self._get_fetch_function(data_type)
        if not fetch_func:
            logger.error(f"❌ 未知的数据类型: {data_type}")
            return {}
        
        try:
            # 加载数据
            start_time = time.time()
            data = fetch_func()
            load_time = time.time() - start_time
            
            # 缓存数据
            self._lazy_cache[cache_key] = {
                'data': data,
                'timestamp': current_time,
                'load_time': load_time,
                'context': context
            }
            
            # 更新访问模式统计
            self._update_access_pattern(data_type, 'cache_miss', load_time)
            
            logger.info(f"✅ 懒加载完成: {data_type}, 耗时: {load_time:.2f}s, 数据量: {len(data) if isinstance(data, (list, dict)) else 'N/A'}")
            return data
            
        except Exception as e:
            logger.error(f"❌ 懒加载失败: {data_type}, 错误: {e}")
            return {}

    def _get_data_ttl(self, data_type: str) -> int:
        """获取数据类型对应的缓存生命周期"""
        if data_type in ['device_priority', 'lot_priority', 'stage_mapping_config']:
            return self._cache_ttl['config_data']
        elif data_type in ['test_specs', 'recipe_files']:
            return self._cache_ttl['static_data']
        else:
            return self._cache_ttl['business_data']

    def _get_fetch_function(self, data_type: str):
        """获取数据类型对应的加载函数"""
        fetch_functions = {
            'device_priority': self._get_device_priority_data,
            'lot_priority': self._get_lot_priority_data,
            'test_specs': self._get_test_specs_data,
            'equipment_status': self._get_equipment_status_data,
            'uph_data': self._get_uph_data,
            'recipe_files': self._get_recipe_files_data,
            'stage_mapping_config': self._get_stage_mapping_data,
            'wait_lots': self._get_wait_lots_data
        }
        return fetch_functions.get(data_type)

    def _update_access_pattern(self, data_type: str, access_type: str, load_time: float = 0):
        """更新数据访问模式统计"""
        if data_type not in self._data_access_patterns:
            self._data_access_patterns[data_type] = {
                'cache_hits': 0,
                'cache_misses': 0,
                'total_load_time': 0,
                'avg_load_time': 0,
                'last_access': time.time()
            }
        
        pattern = self._data_access_patterns[data_type]
        pattern['last_access'] = time.time()
        
        if access_type == 'cache_hit':
            pattern['cache_hits'] += 1
        elif access_type == 'cache_miss':
            pattern['cache_misses'] += 1
            pattern['total_load_time'] += load_time
            total_misses = pattern['cache_misses']
            pattern['avg_load_time'] = pattern['total_load_time'] / total_misses if total_misses > 0 else 0

    def _smart_preload_strategy(self, context: Dict) -> Dict:
        """
        🚀 智能预加载策略：根据上下文决定预加载哪些数据
        
        Args:
            context: 上下文信息 {'lot_count': int, 'algorithm': str, 'optimization_target': str}
        
        Returns:
            Dict: 预加载的数据
        """
        lot_count = context.get('lot_count', 0)
        algorithm = context.get('algorithm', 'intelligent')
        
        logger.info(f"🧠 智能预加载策略启动: {lot_count}个批次, 算法: {algorithm}")
        
        # 根据批次数量确定预加载策略
        if lot_count <= 10:
            # 小规模排产：最小预加载
            essential_data = ['test_specs', 'stage_mapping_config']
            logger.info("📦 小规模排产：最小预加载策略")
        elif lot_count <= 50:
            # 中等规模排产：选择性预加载
            essential_data = ['test_specs', 'equipment_status', 'uph_data', 'stage_mapping_config']
            logger.info("📦 中等规模排产：选择性预加载策略")
        else:
            # 大规模排产：全量预加载
            essential_data = ['device_priority', 'lot_priority', 'test_specs', 
                            'equipment_status', 'uph_data', 'recipe_files', 
                            'stage_mapping_config', 'wait_lots']
            logger.info("📦 大规模排产：全量预加载策略")
        
        # 执行预加载
        preloaded_data = {}
        for data_type in essential_data:
            preloaded_data[data_type] = self._get_data_on_demand(data_type, context)
        
        # 添加数据别名映射
        if 'stage_mapping_config' in preloaded_data:
            preloaded_data['stage_mappings'] = preloaded_data['stage_mapping_config']
        
        # 记录预加载上下文
        self._preload_context = context
        self._preload_timestamp = time.time()
        
        logger.info(f"✅ 智能预加载完成: {len(preloaded_data)}种数据类型")
        return preloaded_data

    def get_data_with_fallback(self, preloaded_data: Dict, data_type: str, context: Dict = None) -> Dict:
        """
        🚀 带回退机制的数据获取：优先使用预加载数据，失败时按需加载
        
        Args:
            preloaded_data: 预加载的数据字典
            data_type: 需要的数据类型
            context: 上下文信息
            
        Returns:
            Dict: 数据字典
        """
        # 1. 优先使用预加载数据
        if data_type in preloaded_data:
            data = preloaded_data[data_type]
            if data:  # 确保数据不为空
                self._update_access_pattern(data_type, 'cache_hit')
                return data
        
        # 2. 预加载数据不可用，按需加载
        logger.debug(f"🔄 预加载数据不可用，按需加载: {data_type}")
        return self._get_data_on_demand(data_type, context)

    def _get_device_priority_data(self) -> List[Dict]:
        """获取设备优先级配置数据 - 移除限制获取完整数据"""
        result = self.data_manager.get_table_data('devicepriorityconfig')
        return result.get('data', []) if result.get('success') else []

    def _get_lot_priority_data(self) -> List[Dict]:
        """获取批次优先级配置数据 - 移除限制获取完整数据"""
        result = self.data_manager.get_table_data('lotpriorityconfig')
        return result.get('data', []) if result.get('success') else []

    def _get_test_specs_data(self) -> List[Dict]:
        """获取测试规范数据 - 移除限制获取完整数据用于排产算法"""
        result = self.data_manager.get_table_data('ET_FT_TEST_SPEC')
        return result.get('data', []) if result.get('success') else []

    def _get_equipment_status_data(self) -> List[Dict]:
        """获取设备状态数据 - 移除限制获取完整数据"""
        result = self.data_manager.get_table_data('EQP_STATUS')
        return result.get('data', []) if result.get('success') else []

    def _get_uph_data(self) -> Dict:
        """
        🚀 优化：获取UPH数据（用户需求：通过DEVICE+STAGE匹配）
        用户需求：UPH的获取是在aps数据库里的et_uph_eqp，通过DEVICE+STAGE的方式来匹配对应工序的UPH
        """
        try:
            # 获取原始UPH数据
            uph_data, _ = self.data_manager.get_uph_data()
            
            # 🚀 用户需求：通过DEVICE+STAGE方式重新组织UPH数据，便于快速匹配
            organized_uph = {}
            
            if isinstance(uph_data, dict):
                for key, value in uph_data.items():
                    if isinstance(value, dict):
                        device = value.get('DEVICE', '').strip()
                        stage = value.get('STAGE', '').strip()
                        uph_value = value.get('UPH', 0)
                        
                        if device and stage:
                            # 使用DEVICE+STAGE作为组合键
                            device_stage_key = f"{device}_{stage}"
                            organized_uph[device_stage_key] = {
                                'DEVICE': device,
                                'STAGE': stage,
                                'UPH': uph_value,
                                'original_data': value
                            }
                            
                            # 同时保持原有的键值对，确保向后兼容
                            organized_uph[key] = value
            
            logger.debug(f"✅ UPH数据重新组织完成，共{len(organized_uph)}条记录，支持DEVICE+STAGE匹配")
            return organized_uph
            
        except Exception as e:
            logger.error(f"获取UPH数据失败: {e}")
            return {}
    
    def _get_uph_for_device_stage(self, device: str, stage: str, preloaded_data: Dict) -> float:
        """
        🚀 新增：根据DEVICE+STAGE获取对应工序的UPH
        用户需求：通过DEVICE+STAGE的方式来匹配对应工序的UPH
        """
        try:
            uph_data = preloaded_data.get('uph_data', {})
            
            if not device or not stage:
                logger.warning(f"DEVICE或STAGE为空，无法获取UPH: device={device}, stage={stage}")
                return 100.0  # 默认UPH
            
            # 1. 精确匹配：DEVICE|STAGE (修复键格式)
            device_stage_key = f"{device.strip()}|{stage.strip()}"
            if device_stage_key in uph_data:
                uph_record = uph_data[device_stage_key]
                uph_value = uph_record.get('UPH', 100.0)
                logger.debug(f"✅ UPH精确匹配: {device}+{stage} -> UPH={uph_value}")
                return float(uph_value)
            
            # 2. STAGE模糊匹配：使用优化的STAGE匹配规则
            for key, value in uph_data.items():
                if isinstance(value, dict) and 'DEVICE' in value and 'STAGE' in value:
                    record_device = value.get('DEVICE', '').strip()
                    record_stage = value.get('STAGE', '').strip()
                    
                    # DEVICE必须精确匹配，STAGE可以模糊匹配
                    if record_device == device.strip():
                        if self._is_sql_stage_match(stage, record_stage, preloaded_data):
                            uph_value = value.get('UPH', 100.0)
                            logger.debug(f"✅ UPH模糊匹配: {device}+{stage} ≈ {record_device}+{record_stage} -> UPH={uph_value}")
                            return float(uph_value)
            
            # 3. 默认UPH（无匹配时）
            logger.warning(f"❌ 未找到匹配的UPH记录: {device}+{stage}，使用默认UPH=100")
            return 100.0
            
        except Exception as e:
            logger.error(f"获取UPH失败: device={device}, stage={stage}, error={e}")
            return 100.0

    def _get_recipe_files_data(self) -> List[Dict]:
        """获取配方文件数据 - 移除限制获取完整数据"""
        result = self.data_manager.get_table_data('et_recipe_file')
        return result.get('data', []) if result.get('success') else []

    def _get_stage_mapping_data(self) -> List[Dict]:
        """🔗 新增：获取STAGE映射配置数据 - 移除限制获取完整数据"""
        try:
            result = self.data_manager.get_table_data('stage_mapping_config')
            if result.get('success'):
                # 只返回激活的映射配置
                active_mappings = [
                    mapping for mapping in result.get('data', []) 
                    if mapping.get('is_active', False)
                ]
                logger.debug(f"✅ 加载STAGE映射配置: {len(active_mappings)}条记录")
                return active_mappings
            else:
                logger.warning("❌ 获取STAGE映射配置失败")
                return []
        except Exception as e:
            logger.error(f"获取STAGE映射配置数据失败: {e}")
            return []
    
    def _get_wait_lots_data(self) -> List[Dict]:
        """获取待测产品数据 - 移除限制获取完整数据"""
        try:
            result = self.data_manager.get_table_data('ET_WAIT_LOT')
            return result.get('data', []) if result.get('success') else []
        except Exception as e:
            logger.error(f"获取待测产品数据失败: {e}")
            return []

    def calculate_equipment_match_score_optimized(self, lot_requirements: Dict, equipment: Dict, preloaded_data: Dict) -> Tuple[int, str, int]:
        """
        🚀 优化版：基于新设备匹配规则的智能设备匹配评分系统
        
        新的匹配规则：
        1. 同设置匹配: KIT_PN + TB_PN + HB_PN 完全匹配
        2. 小改机匹配: KIT_PN 相同 
        3. 大改机匹配: HANDLER_CONFIG 相同
        
        Args:
            lot_requirements: 批次配置需求 
            equipment: 设备当前配置
            preloaded_data: 预加载的数据（优化性能）
            
        Returns:
            Tuple[int, str, int]: (匹配分数, 匹配类型, 改机时间分钟)
        """
        try:
            handler_id = equipment.get('HANDLER_ID', '')
            lot_device = lot_requirements.get('DEVICE', '')
            lot_stage = lot_requirements.get('STAGE', '')
            
            # 🚀 优化：缓存检查
            cache_key = f"{hash(str(lot_requirements))}_{handler_id}"
            if cache_key in self._computation_cache['score_calculations']:
                self._performance_stats['cache_hits'] += 1
                result = self._computation_cache['score_calculations'][cache_key]
                logger.debug(f"🎯 缓存命中 - 设备{handler_id} vs 批次{lot_device}-{lot_stage}: {result[1]}({result[0]}分)")
                return result

            logger.debug(f"🔍 开始评估设备匹配 - 设备{handler_id} vs 批次{lot_device}-{lot_stage}")

            # 提取批次需求配置
            req_kit = lot_requirements.get('KIT_PN', '')
            req_hb = lot_requirements.get('HB_PN', '')
            req_tb = lot_requirements.get('TB_PN', '') or 'NA'
            req_handler_config = lot_requirements.get('HANDLER_CONFIG', '')
            req_eqp_class = lot_requirements.get('EQP_CLASS', '')  # 来自ET_FT_TEST_SPEC.HANDLER字段
            req_device = lot_requirements.get('DEVICE', '')
            req_stage = lot_requirements.get('STAGE', '')

            # 提取设备当前配置
            eqp_kit = equipment.get('KIT_PN', '')
            eqp_hb = equipment.get('HB_PN', '')
            eqp_tb = equipment.get('TB_PN', '') or 'NA'
            eqp_handler_config = equipment.get('HANDLER_CONFIG', '')
            eqp_class = equipment.get('EQP_CLASS', '')
            eqp_device = equipment.get('DEVICE', '')  # 设备当前处理的DEVICE
            eqp_stage = equipment.get('STAGE', '')    # 设备当前处理的STAGE

            logger.debug(f"📋 需求: KIT:{req_kit}, HB:{req_hb}, TB:{req_tb}, EQP_CLASS:{req_eqp_class}, CONFIG:{req_handler_config}")
            logger.debug(f"🔧 设备: KIT:{eqp_kit}, HB:{eqp_hb}, TB:{eqp_tb}, EQP_CLASS:{eqp_class}, CONFIG:{eqp_handler_config}")

            # 🎯 按照SQL规则严格实现三级匹配策略
            
            # 1️⃣ 同设置匹配 (samesetup.sql) - 最高优先级100分，无改机时间
            # 条件：KIT_PN + TB_PN + HB_PN相同
            if self._check_sql_same_setup_match(req_kit, req_hb, req_tb, req_device, req_stage, 
                                               eqp_kit, eqp_hb, eqp_tb, eqp_device, eqp_stage, preloaded_data):
                rule = self.match_rules['same_setup']
                score = rule['score']
                changeover_time = rule['changeover_time']
                match_type = rule['name']
                logger.info(f"✨ {match_type} - 评分:{score}, 改机时间:{changeover_time}分钟")
                
            # 2️⃣ 小改机匹配 (smallchange.sql) - 中等优先级80分，45分钟改机
            # 条件：KIT_PN相同
            elif self._check_sql_small_change_match(req_kit, req_hb, req_tb, req_device, req_stage,
                                                   eqp_kit, eqp_hb, eqp_tb, eqp_device, eqp_stage, preloaded_data):
                rule = self.match_rules['small_change']
                score = rule['score']
                changeover_time = rule['changeover_time']
                match_type = rule['name']
                logger.info(f"🔧 {match_type} - 评分:{score}, 改机时间:{changeover_time}分钟")
                
            # 3️⃣ 大改机匹配 (bigchange.sql) - 基础兼容60分，120分钟改机
            # 条件：HANDLER_CONFIG相同
            elif self._check_sql_big_change_match(req_handler_config, req_device, req_stage, 
                                                 eqp_handler_config, eqp_device, eqp_stage, preloaded_data):
                rule = self.match_rules['big_change']
                score = rule['score']
                changeover_time = rule['changeover_time']
                match_type = rule['name']
                logger.info(f"🛠️ {match_type} - 评分:{score}, 改机时间:{changeover_time}分钟")
                
            else:
                # 不匹配 - 按SQL规则，没有满足任何匹配条件的设备
                score = 0
                changeover_time = 9999
                match_type = "不兼容"
                logger.debug(f"❌ {match_type} - 设备无法处理该批次")

            # 🚀 智能评分调整（保持原有逻辑）
            if score > 0:
                # 🔧 修复：计算精确的改机时间
                changeover_time = self._calculate_intelligent_changeover_time(
                    hardware_score=score, 
                    config_score=score, 
                    status=equipment.get('STATUS', 'IDLE'),
                    match_type=match_type,
                    req_kit=req_kit, eqp_kit=eqp_kit,
                    req_hb=req_hb, eqp_hb=eqp_hb,
                    req_tb=req_tb, eqp_tb=eqp_tb
                )
                
                # 设备性能调整
                performance_bonus = self._get_equipment_performance_score(handler_id)
                
                # 负载调整
                load_penalty = self._get_current_load_factor(handler_id)
                
                # 最终评分
                final_score = min(100, max(0, score + performance_bonus - load_penalty))
                
                result = (int(final_score), match_type, changeover_time)
                logger.debug(f"🎯 最终评分 - {handler_id}: {match_type}({final_score}分, {changeover_time}分钟)")
            else:
                result = (0, match_type, changeover_time)

            # 缓存计算结果
            self._computation_cache['score_calculations'][cache_key] = result
            return result

        except Exception as e:
            logger.error(f"❌ 计算设备匹配评分失败 - 设备{equipment.get('HANDLER_ID', 'Unknown')}: {e}")
            return (0, "评分异常", 9999)

    def _check_sql_same_setup_match(self, req_kit: str, req_hb: str, req_tb: str, req_device: str, req_stage: str,
                                   eqp_kit: str, eqp_hb: str, eqp_tb: str, eqp_device: str, eqp_stage: str, preloaded_data: Dict) -> bool:
        """
        🔧 更新：检查同设置匹配 - 新规则：KIT_PN + TB_PN + HB_PN相同
        
        新的匹配条件：
        1. KIT_PN完全匹配（必须有值且相等）
        2. HB_PN完全匹配（必须有值且相等）
        3. TB_PN完全匹配（包括都为'NA'的情况）
        
        移除了DEVICE和STAGE的检查，只关注硬件配置匹配
        """
        try:
            # 🚨 严格验证：KIT信息必须存在且匹配
            if not req_kit or not eqp_kit:
                logger.debug(f"❌ 同设置匹配失败 - KIT信息缺失: req_kit='{req_kit}', eqp_kit='{eqp_kit}'")
                return False
            
            if not req_hb or not eqp_hb:
                logger.debug(f"❌ 同设置匹配失败 - HB信息缺失: req_hb='{req_hb}', eqp_hb='{eqp_hb}'")
                return False
            
            # 🔧 新规则：只检查KIT_PN + TB_PN + HB_PN匹配
            kit_match = req_kit.strip() == eqp_kit.strip()
            hb_match = req_hb.strip() == eqp_hb.strip()
            tb_match = (req_tb or 'NA').strip() == (eqp_tb or 'NA').strip()
            
            # 🔧 新需求：所有硬件配置条件都必须满足
            result = kit_match and hb_match and tb_match
            
            if result:
                logger.info(f"✅ 同设置匹配成功 - 硬件配置完全匹配")
                logger.info(f"   KIT:{req_kit}={eqp_kit}, HB:{req_hb}={eqp_hb}, TB:{req_tb}={eqp_tb}")
            else:
                logger.debug(f"❌ 同设置不匹配 - KIT:{kit_match}, HB:{hb_match}, TB:{tb_match}")
                if not kit_match:
                    logger.debug(f"   KIT不匹配: '{req_kit}' != '{eqp_kit}'")
                if not hb_match:
                    logger.debug(f"   HB不匹配: '{req_hb}' != '{eqp_hb}'")
                if not tb_match:
                    logger.debug(f"   TB不匹配: '{req_tb}' != '{eqp_tb}'")
                
            return result
            
        except Exception as e:
            logger.error(f"检查同设置匹配失败: {e}")
            return False

    def _check_sql_small_change_match(self, req_kit: str, req_hb: str, req_tb: str, req_device: str, req_stage: str,
                                     eqp_kit: str, eqp_hb: str, eqp_tb: str, eqp_device: str, eqp_stage: str, preloaded_data: Dict) -> bool:
        """
        🔧 更新：小改机匹配逻辑 - 新规则：KIT_PN相同
        
        新的匹配条件：
        1. KIT_PN完全匹配（必须有值且相等）
        
        移除了EQP_CLASS、DEVICE、STAGE的检查，只关注KIT_PN匹配
        可以通过更换HB_PN/TB_PN来适配不同的批次需求
        """
        try:
            # 🚨 严格验证：KIT信息必须存在且匹配
            if not req_kit or not eqp_kit:
                logger.debug(f"❌ 小改机匹配失败 - KIT信息缺失: req_kit='{req_kit}', eqp_kit='{eqp_kit}'")
                return False
            
            # 🔧 新规则：只检查KIT_PN匹配
            kit_match = req_kit.strip() == eqp_kit.strip()
            
            if kit_match:
                logger.info(f"✅ 小改机匹配成功 - KIT_PN匹配: {req_kit}")
                logger.info(f"   可通过更换HB/TB适配不同批次")
            else:
                logger.debug(f"❌ 小改机不匹配 - KIT_PN不匹配: '{req_kit}' != '{eqp_kit}'")
                
            return kit_match
            
        except Exception as e:
            logger.error(f"检查小改机匹配失败: {e}")
            return False
    
    def _check_kit_changeability_for_small_change(self, req_eqp_class: str, eqp_class: str, preloaded_data: Dict) -> bool:
        """
        🚀 新增：小改机KIT兼容性检查
        用户需求：判断机台是否可以通过更换HB/TB来适配批次需求
        """
        try:
            # 相同EQP_CLASS的机台通常可以通过更换HB/TB适配
            if req_eqp_class.strip().upper() == eqp_class.strip().upper():
                return True
            
            # 检查EQP_CLASS系列兼容性（同系列机台可以通过KIT更换适配）
            eqp_class_families = {
                'TEST': ['TEST', 'TESTER', 'FT', 'FINAL_TEST'],
                'HANDLER': ['HANDLER', 'HANDLE', 'HDL', 'SORT'],
                'PROGRAMMER': ['PROG', 'PROGRAM', 'PGM', 'TRIM'],
                'SORTER': ['SORT', 'SORTER', 'SRT', 'HANDLER'],
                'ATE': ['ATE', 'TESTER', 'TEST'],
                'PROBER': ['PROBE', 'PROBER', 'WAF']
            }
            
            req_upper = req_eqp_class.upper().strip()
            eqp_upper = eqp_class.upper().strip()
            
            # 检查是否属于同一设备系列
            for family, variants in eqp_class_families.items():
                req_in_family = any(variant in req_upper for variant in variants)
                eqp_in_family = any(variant in eqp_upper for variant in variants)
                
                if req_in_family and eqp_in_family:
                    logger.debug(f"✅ KIT兼容性检查通过: {req_eqp_class} <-> {eqp_class} (系列: {family})")
                    return True
            
            # 如果EQP_CLASS信息不完整，默认允许（容错处理）
            if not req_eqp_class or not eqp_class:
                return True
            
            logger.debug(f"❌ KIT兼容性检查失败: {req_eqp_class} <-> {eqp_class} (不同系列)")
            return False
            
        except Exception as e:
            logger.warning(f"KIT兼容性检查失败: {e}")
            return True  # 默认允许，确保系统稳定性

    def _check_sql_big_change_match(self, req_handler_config: str, req_device: str, req_stage: str, 
                                   eqp_handler_config: str, eqp_device: str, eqp_stage: str, preloaded_data: Dict) -> bool:
        """
        🔧 更新：大改机匹配逻辑 - 新规则：HANDLER_CONFIG相同
        
        新的匹配条件：
        1. HANDLER_CONFIG完全匹配或兼容匹配
        
        移除了DEVICE和STAGE的检查，只关注HANDLER_CONFIG匹配
        可以通过较大改机处理该批次
        """
        try:
            # 🔧 修复BUG：严格的HANDLER_CONFIG匹配规则
            config_match = False
            if req_handler_config and eqp_handler_config:
                req_config = req_handler_config.strip().upper()
                eqp_config = eqp_handler_config.strip().upper()
                
                # 完全匹配
                if req_config == eqp_config:
                    config_match = True
                    logger.debug(f"✅ HANDLER_CONFIG完全匹配: {req_handler_config}")
                # 兼容匹配（同系列配置）
                
            elif not req_handler_config and not eqp_handler_config:  # 都无配置要求时，认为兼容
                config_match = True
                logger.debug(f"✅ HANDLER_CONFIG匹配: 都无特殊配置要求")
            
            if config_match:
                logger.info(f"✅ 大改机匹配成功 - HANDLER_CONFIG匹配: '{req_handler_config}' ≈ '{eqp_handler_config}'")
            else:
                logger.info(f"❌ 大改机不匹配 - HANDLER_CONFIG不兼容: '{req_handler_config}' ≠ '{eqp_handler_config}'")
                
            return config_match
            
        except Exception as e:
            logger.error(f"检查大改机匹配失败: {e}")
            return False

    def _smart_stage_match(self, source_stage: str, target_stage: str, preloaded_data: Dict) -> bool:
        """
        🔗 新增：基于数据库配置的智能STAGE匹配
        使用 stage_mapping_config 表的动态映射规则
        
        Args:
            source_stage: 源STAGE（如批次的STAGE）
            target_stage: 目标STAGE（如测试规范的STAGE）
            preloaded_data: 预加载数据，包含映射配置
            
        Returns:
            bool: 是否匹配
        """
        try:
            if not source_stage or not target_stage:
                return False
            
            source_stage_clean = source_stage.upper().strip()
            target_stage_clean = target_stage.upper().strip()
            
            # 1. 精确匹配
            if source_stage_clean == target_stage_clean:
                return True
            
            # 2. 从预加载数据获取映射配置
            stage_mappings = preloaded_data.get('stage_mappings', [])
            if not stage_mappings:
                logger.debug("⚠️ 无STAGE映射配置，使用传统匹配方法")
                return self._legacy_stage_match(source_stage, target_stage)
            
            # 3. 按优先级排序的映射匹配
            # 精确匹配 -> 模糊匹配 -> 别名匹配
            mapping_priority = {'exact': 1, 'fuzzy': 2, 'alias': 3}
            sorted_mappings = sorted(stage_mappings, key=lambda x: mapping_priority.get(x.get('mapping_type', 'fuzzy'), 2))
            
            for mapping in sorted_mappings:
                mapping_source = mapping.get('source_stage', '').upper().strip()
                mapping_target = mapping.get('target_stage', '').upper().strip()
                mapping_type = mapping.get('mapping_type', 'fuzzy')
                
                # 检查是否匹配
                if self._check_mapping_match(
                    source_stage_clean, target_stage_clean,
                    mapping_source, mapping_target, mapping_type
                ):
                    logger.debug(f"✅ STAGE映射匹配: {source_stage} → {target_stage} (类型: {mapping_type})")
                    return True
            
            # 4. 如果没有找到映射，使用传统规则作为兜底
            return self._legacy_stage_match(source_stage, target_stage)
            
        except Exception as e:
            logger.error(f"智能STAGE匹配失败: {e}")
            return self._legacy_stage_match(source_stage, target_stage)
    
    def _check_mapping_match(self, source_stage: str, target_stage: str, 
                           mapping_source: str, mapping_target: str, mapping_type: str) -> bool:
        """检查特定映射规则是否匹配"""
        try:
            if mapping_type == 'exact':
                # 精确匹配：完全相同
                return ((source_stage == mapping_source and target_stage == mapping_target) or
                        (source_stage == mapping_target and target_stage == mapping_source))
                        
            elif mapping_type == 'fuzzy':
                # 模糊匹配：包含关系
                return ((mapping_source in source_stage and mapping_target in target_stage) or
                        (mapping_source in target_stage and mapping_target in source_stage) or
                        (source_stage in mapping_source and target_stage in mapping_target) or
                        (target_stage in mapping_source and source_stage in mapping_target))
                        
            elif mapping_type == 'alias':
                # 别名匹配：任意方向的等价关系
                return ((source_stage in [mapping_source, mapping_target] and 
                        target_stage in [mapping_source, mapping_target]) and
                        source_stage != target_stage)
                        
            return False
            
        except Exception as e:
            logger.error(f"映射规则检查失败: {e}")
            return False
    
    def _legacy_stage_match(self, lot_stage: str, spec_stage: str) -> bool:
        """🔗 传统的STAGE匹配逻辑（作为兜底方案）- 包含历史硬编码规则"""
        try:
            if not lot_stage or not spec_stage:
                return False
            
            lot_stage_upper = lot_stage.upper().strip()
            spec_stage_upper = spec_stage.upper().strip()
            
            # 1. 精确匹配
            if lot_stage_upper == spec_stage_upper:
                return True
            
            # 2. 按SQL规则：取批次STAGE的前4个字符与测试规范STAGE匹配
            lot_stage_prefix = lot_stage_upper[:4]
            if lot_stage_prefix == spec_stage_upper:
                return True
            
            # 3. 基础的模糊匹配规则
            if lot_stage_upper.endswith('-FT'):
                base_lot_stage = lot_stage_upper[:-3]
                if base_lot_stage == spec_stage_upper:
                    return True
            
            # 4. 历史硬编码映射规则（兜底保障）
            legacy_mappings = {
                # 温度测试系列
                'HOT-FT': ['Hot', 'HOT', 'hot', 'Hot2', 'Hot3'],
                'COLD-FT': ['Cold', 'COLD', 'cold'],
                'ROOM-TTR-FT': ['ROOM-TTR', 'Room-TTR', 'room-ttr', 'ROOM-TEST'],
                'ROOM-TEST-FT': ['ROOM-TEST', 'Room-Test', 'room-test', 'ROOM-TTR'], 
                'TRIM-FT': ['TRIM', 'Trim', 'trim'],
                'BAKING2': ['BAKING', 'Baking', 'baking'],
                'LSTR': ['LSTR', 'Lstr', 'lstr'],
                
                # 反向映射
                'Hot': ['HOT-FT', 'hot-ft'],
                'Cold': ['COLD-FT', 'cold-ft'],
                'ROOM-TEST': ['ROOM-TEST-FT', 'ROOM-TTR-FT'],
                'ROOM-TTR': ['ROOM-TTR-FT', 'ROOM-TEST-FT'],
                'Trim': ['TRIM-FT', 'trim-ft']
            }
            
            # 检查历史映射
            if lot_stage_upper in legacy_mappings:
                for target in legacy_mappings[lot_stage_upper]:
                    if target.upper() == spec_stage_upper:
                        logger.debug(f"✅ 历史映射匹配: {lot_stage} → {spec_stage}")
                        return True
            
            # 反向检查
            if spec_stage_upper in legacy_mappings:
                for target in legacy_mappings[spec_stage_upper]:
                    if target.upper() == lot_stage_upper:
                        logger.debug(f"✅ 历史反向映射匹配: {lot_stage} ← {spec_stage}")
                        return True
            
            # 5. 数字后缀处理
            import re
            lot_base = re.sub(r'\d+$', '', lot_stage_upper)
            spec_base = re.sub(r'\d+$', '', spec_stage_upper)
            if lot_base == spec_base and len(lot_base) >= 3:
                logger.debug(f"✅ 数字后缀匹配: {lot_stage} → {spec_stage}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"传统STAGE匹配失败: {e}")
            return False

    def _is_sql_stage_match(self, lot_stage: str, spec_stage: str, preloaded_data: Dict = None) -> bool:
        """
        🔗 增强版STAGE匹配逻辑（支持数据库配置的智能匹配）
        优先使用数据库配置的映射规则，如无配置则使用传统规则
        """
        try:
            # 如果有预加载数据，使用智能匹配
            if preloaded_data:
                return self._smart_stage_match(lot_stage, spec_stage, preloaded_data)
            
            # 否则使用传统匹配逻辑
            return self._legacy_stage_match(lot_stage, spec_stage)
            
        except Exception as e:
            logger.error(f"STAGE匹配失败: {e}")
            return False

    def _get_lot_type_classification(self, lot: Dict) -> str:
        """
        🚀 增强：LOT_TYPE分类识别
        区分工程批次和量产批次
        """
        try:
            lot_type = lot.get('LOT_TYPE', '').strip()
            lot_id = lot.get('LOT_ID', '').strip()
            
            # 直接从字段判断
            if lot_type in ['工程批', 'Engineering', 'ENG']:
                return '工程批次'
            elif lot_type in ['量产批', 'Production', 'PROD']:
                return '量产批次'
            
            # 从LOT_ID模式判断
            if any(pattern in lot_id.upper() for pattern in ['ENG', 'TEST', 'TRIAL', 'PILOT']):
                return '工程批次'
            elif any(pattern in lot_id.upper() for pattern in ['PROD', 'MP', 'MASS']):
                return '量产批次'
            
            # 默认为工程批次（当前数据主要是工程批）
            return '工程批次'
            
        except Exception as e:
            logger.warning(f"LOT_TYPE分类失败: {e}")
            return '工程批次'

    def _check_lstr_pkg_compatibility(self, lot_pkg_pn: str, equipment_pkg_pn: str) -> bool:
        """
        🚀 新增：LSTR阶段基于封装类型PKG_PN的兼容性检查
        LSTR批次可以基于相同封装类型上机
        """
        try:
            if not lot_pkg_pn or not equipment_pkg_pn:
                return True  # 如果信息不完整，允许匹配
            
            lot_pkg = lot_pkg_pn.upper().strip()
            eqp_pkg = equipment_pkg_pn.upper().strip()
            
            # 精确匹配
            if lot_pkg == eqp_pkg:
                return True
            
            # 封装系列匹配（如QFN48和QFN64都是QFN系列）
            pkg_families = {
                'QFN': ['QFN', 'MLF'],
                'BGA': ['BGA', 'FBGA', 'PBGA'],
                'QFP': ['QFP', 'LQFP', 'TQFP'],
                'SOP': ['SOP', 'SOIC', 'SSOP', 'TSOP'],
                'DFN': ['DFN', 'SON'],
                'CSP': ['CSP', 'WLCSP']
            }
            
            for family, variants in pkg_families.items():
                lot_in_family = any(variant in lot_pkg for variant in variants)
                eqp_in_family = any(variant in eqp_pkg for variant in variants)
                if lot_in_family and eqp_in_family:
                    logger.debug(f"✅ LSTR封装系列匹配: {lot_pkg} <-> {eqp_pkg} (系列: {family})")
                    return True
            
            return False
            
        except Exception as e:
            logger.warning(f"LSTR封装兼容性检查失败: {e}")
            return True  # 默认允许

    def _calculate_btt_baking_priority_score(self, lot: Dict, preloaded_data: Dict) -> float:
        """
        🚀 新增：BTT和烘烤批次的优先级评分
        基于产品优先级和同产品批次数量
        """
        try:
            device = lot.get('DEVICE', '')
            lot_type = self._get_lot_type_classification(lot)
            
            # 基础分数
            base_score = 50.0
            
            # 1. 产品优先级评分（从设备优先级配置获取）
            device_priority_score = 0.0
            device_priorities = preloaded_data.get('device_priority', [])
            for priority_config in device_priorities:
                if priority_config.get('DEVICE', '') == device:
                    device_priority_score = float(priority_config.get('PRIORITY', 0))
                    break
            
            # 2. 批次类型优先级
            lot_type_bonus = 20.0 if lot_type == '量产批次' else 10.0
            
            # 3. 同产品批次数量评分（批次越多优先级越高）
            same_device_count = 0
            wait_lots = preloaded_data.get('wait_lots', [])
            for other_lot in wait_lots:
                if other_lot.get('DEVICE', '') == device:
                    same_device_count += 1
            
            batch_count_score = min(same_device_count * 5.0, 30.0)  # 最高30分
            
            # 4. FIFO评分（早进先出）
            fifo_score = self._calculate_intelligent_fifo_score(lot)
            
            total_score = (base_score + 
                          device_priority_score * 0.4 + 
                          lot_type_bonus * 0.3 + 
                          batch_count_score * 0.2 + 
                          fifo_score * 0.1)
            
            logger.debug(f"BTT/烘烤优先级 {lot.get('LOT_ID')}: 设备{device_priority_score} + 类型{lot_type_bonus} + 数量{batch_count_score} + FIFO{fifo_score} = {total_score}")
            
            return total_score
            
        except Exception as e:
            logger.warning(f"BTT/烘烤优先级计算失败: {e}")
            return 50.0

    def _check_special_stage_equipment_availability(self, special_stage: str, config: Dict, preloaded_data: Dict) -> List[Dict]:
        """
        🔧 新增：检查特殊阶段设备可用性
        
        Args:
            special_stage: 特殊阶段名称 (BAKING/LSTR/BTT)
            config: 阶段配置信息
            preloaded_data: 预加载数据
            
        Returns:
            List[Dict]: 兼容的设备列表
        """
        try:
            equipment_status = preloaded_data.get('equipment_status', [])
            compatible_equipment = []
            
            required_eqp_class = config.get('EQP_CLASS', '')
            required_config = config.get('REQUIRED_CONFIG', '')
            
            for equipment in equipment_status:
                eqp_class = equipment.get('EQP_CLASS', '').strip()
                handler_config = equipment.get('HANDLER_CONFIG', '').strip()
                status = equipment.get('STATUS', '').strip()
                
                # 🔧 严格匹配条件
                class_match = False
                config_match = False
                
                # 1. EQP_CLASS匹配检查
                if special_stage == 'BAKING':
                    # 烘箱设备：EQP_CLASS必须包含Oven或BAKING
                    class_match = ('OVEN' in eqp_class.upper() or 
                                 'BAKING' in eqp_class.upper() or
                                 eqp_class.upper() == 'OVEN')
                elif special_stage == 'LSTR':
                    # 编带设备：EQP_CLASS必须包含LSTR或TAPE
                    class_match = ('LSTR' in eqp_class.upper() or 
                                 'TAPE' in eqp_class.upper() or
                                 'REEL' in eqp_class.upper())
                elif special_stage == 'BTT':
                    # 老化测试设备：EQP_CLASS必须包含TEST或BURN
                    class_match = ('TEST' in eqp_class.upper() or 
                                 'BURN' in eqp_class.upper() or
                                 'BTT' in eqp_class.upper())
                
                # 2. HANDLER_CONFIG匹配检查（可选）
                if required_config:
                    config_match = (required_config.upper() in handler_config.upper() or
                                  handler_config.upper() in required_config.upper())
                else:
                    config_match = True  # 如果没有特定配置要求，则通过
                
                # 3. 设备状态检查
                status_ok = status.upper() in ['IDLE', 'AVAILABLE', 'READY']
                
                if class_match and config_match and status_ok:
                    compatible_equipment.append(equipment)
                    logger.debug(f"✅ 兼容设备: {equipment.get('HANDLER_ID')} - "
                               f"EQP_CLASS:{eqp_class}, CONFIG:{handler_config}, STATUS:{status}")
                else:
                    logger.debug(f"❌ 不兼容设备: {equipment.get('HANDLER_ID')} - "
                               f"类别匹配:{class_match}, 配置匹配:{config_match}, 状态OK:{status_ok}")
            
            logger.info(f"🔍 {special_stage}阶段设备检查: 找到{len(compatible_equipment)}/{len(equipment_status)}台兼容设备")
            return compatible_equipment
            
        except Exception as e:
            logger.error(f"检查特殊阶段设备可用性失败: {e}")
            return []


    def _is_special_stage_lot(self, lot: Dict) -> str:
        """
        🚀 新增：识别特殊阶段批次
        返回特殊阶段类型：LSTR、BTT、BAKING或空字符串
        """
        stage = lot.get('STAGE', '').upper().strip()
        
        if 'LSTR' in stage or 'TAPE' in stage or 'REEL' in stage:
            return 'LSTR'
        elif 'BTT' in stage or 'BURN' in stage or 'AGING' in stage:
            return 'BTT'
        elif 'BAKING' in stage or 'BAKE' in stage or 'OVEN' in stage:
            return 'BAKING'
        
        return ''

    def _calculate_processing_time(self, lot: Dict, lot_requirements: Dict, preloaded_data: Dict = None) -> float:
        """
        🔧 修复：计算批次处理时间（严格通过DEVICE+STAGE获取UPH）
        用户需求：UPH的获取是在aps数据库里的et_uph_eqp，通过DEVICE+STAGE的方式来匹配对应工序的UPH
        """
        try:
            # 安全地转换数据类型
            good_qty_raw = lot.get('GOOD_QTY', 0)
            
            # 处理字符串类型的数值
            try:
                good_qty = float(str(good_qty_raw)) if good_qty_raw else 0
            except (ValueError, TypeError):
                good_qty = 0
                
            if good_qty <= 0:
                return 1.0  # 默认1小时
            
            # 🔧 修复：通过DEVICE+STAGE方式获取UPH
            device = lot_requirements.get('DEVICE', '') or lot.get('DEVICE', '')
            stage = lot_requirements.get('STAGE', '') or lot.get('STAGE', '')
            
            uph = 100.0  # 默认UPH
            
            if preloaded_data:
                # 使用新的UPH获取方法，基于DEVICE+STAGE匹配
                uph = self._get_uph_for_device_stage(device, stage, preloaded_data)
                logger.debug(f"✅ 通过DEVICE+STAGE获取UPH: {device}+{stage} -> UPH={uph}")
            else:
                # 回退到传统方式
                uph_raw = lot_requirements.get('UPH', 100)
                try:
                    uph = float(str(uph_raw)) if uph_raw else 100
                except (ValueError, TypeError):
                    uph = 100
                logger.debug(f"⚠️ 使用传统UPH获取方式: UPH={uph}")
            
            if uph <= 0:
                logger.warning(f"❌ 未找到匹配的UPH记录: {device}+{stage}，使用默认UPH=100")
                uph = 100.0  # 默认UPH
            
            # 计算处理时间（小时）
            processing_time = good_qty / uph
            
            # 最小处理时间0.1小时，最大24小时
            final_time = max(0.1, min(processing_time, 24.0))
            
            logger.debug(f"📊 处理时间计算: 批次{lot.get('LOT_ID', 'Unknown')} - "
                        f"数量={good_qty}, UPH={uph}, 时间={final_time:.2f}小时")
            
            return final_time
            
        except Exception as e:
            logger.warning(f"计算处理时间失败: {e}")
            return 1.0  # 默认1小时



    def execute_optimized_scheduling(self, algorithm: str = 'intelligent', user_id: str = None, optimization_target: str = 'balanced') -> List[Dict]:
        """
        🚨 DEPRECATED - 此方法已废弃，因为不保存数据到数据库！
        ❌ 问题：此方法只返回排产结果，不保存到数据库，导致用户看不到排产数据
        ✅ 解决：请使用 execute_real_scheduling() 方法，该方法会正确保存数据并包含STEP字段
        
        🚀 执行优化版智能排产 - 统一算法架构 + 动态权重配置
        
        设计理念：
        🎯 所有前端策略（intelligent/deadline/product/value）都使用同一个增强启发式算法
        🎯 策略差异仅体现在权重配置的不同，实现简洁而强大的架构
        
        核心优化：
        1. 🏗️ 统一算法架构：一个算法处理所有策略
        2. ⚖️ 动态权重配置：根据策略调整评分权重
        3. 💾 多级缓存预加载：显著提升性能
        4. 🚀 并行计算加速：大规模数据优化
        5. 🔧 特殊阶段处理：LSTR/BTT/BAKING业务规则
        6. 🎭 STAGE模糊匹配：解决匹配覆盖率问题
        7. 📊 性能监控与反馈：持续优化
        
        Args:
            algorithm: 前端策略选择 (intelligent/deadline/product/value)
            user_id: 用户ID，用于获取个性化权重配置
            optimization_target: 优化目标 (balanced/speed/accuracy)
            
        Returns:
            List[Dict]: 排产结果列表
        """
        start_time = time.time()
        logger.info("🚀 开始执行多级缓存+并行计算+策略权重优化智能排产...")
        
        # 🎯 策略权重系统集成：动态获取用户策略权重配置
        dynamic_weights = self._get_strategy_weights(algorithm, user_id, optimization_target)
        original_weights = None
        if dynamic_weights:
            # 临时覆盖default_weights，确保后续算法使用动态权重
            original_weights = self.default_weights.copy()
            self.default_weights.update(dynamic_weights)
            logger.info(f"✅ 策略权重动态加载成功 - 策略: {algorithm}, 用户: {user_id or 'system'}, 优化目标: {optimization_target}")
            logger.debug(f"🔧 当前权重配置: {self.default_weights}")
        else:
            logger.warning(f"⚠️ 策略权重获取失败，使用默认配置 - 策略: {algorithm}, 优化目标: {optimization_target}")
        
        # 重置性能统计
        self._performance_stats = {
            'cache_hits': 0,
            'cache_misses': 0,
            'db_queries': 0,
            'computation_time': 0,
            'parallel_computation_time': 0,  # Task 2.3新增
            'parallel_speedup': 1.0          # Task 2.3新增
        }
        
        try:
            # 获取待排产批次
            wait_lots, wait_source = self.data_manager.get_wait_lot_data()
            logger.info(f"📋 从{wait_source}获取到 {len(wait_lots)} 个待排产批次")
            
            # 🚀 智能预加载策略：根据批次数量和算法类型决定预加载策略
            context = {
                'lot_count': len(wait_lots),
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'user_id': user_id
            }
            preloaded_data = self._smart_preload_strategy(context)
            
            if not wait_lots:
                logger.warning("⚠️ 没有待排产批次")
                return []
            
            # 🔧 为确保结果一致性，禁用并行计算
            parallel_engine = None  # 强制禁用并行计算
            logger.info("⚠️ 并行计算已禁用以确保排产结果一致性")
            
            # 🚀 Task 2.2优化2：智能算法选择 (使用多维度分析)
            available_equipment = preloaded_data.get('equipment_status', [])
            
            # 🚀 Task 2.3优化2：使用并行计算加速算法选择过程
            parallel_start = time.time()
            if parallel_engine and len(wait_lots) > 50:  # 大规模数据使用并行计算
                logger.info("🚀 使用并行计算加速算法选择过程...")
                selected_algorithm, decision_log = self._parallel_algorithm_selection(
                    wait_lots, available_equipment, parallel_engine
                )
            else:
                selected_algorithm, decision_log = self.algorithm_selector.select_optimal_algorithm(
                    wait_lots=wait_lots,
                    available_equipment=available_equipment,
                    constraints={'time_limit': 600},  # 10分钟时间限制
                    requirements={'accuracy_requirement': 0.8}  # 80%精度要求
                )
            parallel_time = time.time() - parallel_start
            self._performance_stats['parallel_computation_time'] += parallel_time
            
            logger.info(f"🧠 智能算法选择结果: {selected_algorithm.value}")
            logger.debug(f"🔍 决策因子: {decision_log['decision_factors']}")
            
            # 🚀 Task 2.2优化3：为选定算法准备智能缓存环境
            cache_preparation = self.cache_adapter.prepare_for_algorithm(
                algorithm_type=selected_algorithm,
                data_context=preloaded_data,
                performance_requirements={'time_limit': 600, 'accuracy_requirement': 0.8}
            )
            logger.info(f"🧠 缓存环境准备: {cache_preparation}")
            
            # 🚀 Task 2.2优化4：记录数据访问模式，用于缓存学习
            data_accessed = list(preloaded_data.keys())
            
            # 🚀 Task 2.3优化3：根据前端策略选择执行对应的增强算法
            from app.services.algorithm_selector import AlgorithmType
            parallel_start = time.time()
            
            # 🎯 统一算法架构：所有前端策略都使用增强启发式算法，仅权重配置不同
            logger.info(f"🎯 执行增强启发式算法（{algorithm.upper()}策略 - 权重已动态配置）")
            logger.info(f"📊 当前权重配置: 技术匹配={self.default_weights.get('tech_match_weight', 0)}%, "
                       f"负载均衡={self.default_weights.get('load_balance_weight', 0)}%, "
                       f"交期紧迫={self.default_weights.get('deadline_weight', 0)}%, "
                       f"产值效率={self.default_weights.get('value_efficiency_weight', 0)}%, "
                       f"业务优先级={self.default_weights.get('business_priority_weight', 0)}%")
            
            # 🚀 所有策略统一使用增强启发式算法
            # 差异化体现在前面已动态配置的权重参数中
            schedule_results = self._execute_heuristic_scheduling_optimized(wait_lots, preloaded_data)
            
            # 📝 记录策略使用情况
            logger.info(f"✅ {algorithm.upper()}策略执行完成，权重配置已生效")
            
            parallel_time = time.time() - parallel_start
            self._performance_stats['parallel_computation_time'] += parallel_time
            
            execution_time = time.time() - start_time
            self._performance_stats['computation_time'] = execution_time
            
            # 🚀 Task 2.2优化5：记录算法性能数据和缓存表现
            success_rate = len(schedule_results) / len(wait_lots) if wait_lots else 0
            cache_stats = self.multilevel_cache.get_cache_stats()
            overall_hit_rate = cache_stats['overall_hit_rate'] / 100.0  # 转为比例
            
            # 记录到智能算法选择器
            self.algorithm_selector.record_algorithm_performance(
                algorithm=selected_algorithm,
                lot_count=len(wait_lots),
                equipment_count=len(available_equipment),
                execution_time=execution_time,
                success_rate=success_rate
            )
            
            # 记录到智能缓存适配器
            self.cache_adapter.record_algorithm_execution(
                algorithm_type=selected_algorithm,
                execution_time=execution_time,
                data_accessed=data_accessed,
                cache_hit_rate=overall_hit_rate
            )
            
            # 🚀 Task 2.2 + 2.3性能统计报告
            legacy_hit_rate = (self._performance_stats['cache_hits'] / 
                            max(self._performance_stats['cache_hits'] + self._performance_stats['cache_misses'], 1)) * 100
            
            # 计算并行加速比
            serial_time = execution_time - self._performance_stats['parallel_computation_time']
            if serial_time > 0 and self._performance_stats['parallel_computation_time'] > 0:
                self._performance_stats['parallel_speedup'] = serial_time / self._performance_stats['parallel_computation_time']
            
            logger.info(f"🎉 Task 2.2+2.3优化排产完成 - 算法: {selected_algorithm.value}, 成功: {len(schedule_results)}/{len(wait_lots)}, "
                       f"总耗时: {execution_time:.2f}s")
            logger.info(f"📊 Task 2.2缓存性能摘要:")
            logger.info(f"   • 多级缓存命中率: {cache_stats['overall_hit_rate']:.1f}%")
            logger.info(f"   • L1缓存大小: {cache_stats['l1_cache_size']}")
            logger.info(f"   • 热点数据: {cache_stats['hot_keys_count']}个")
            logger.info(f"   • 传统缓存命中率: {legacy_hit_rate:.1f}%")
            logger.info(f"   • 数据库查询: {self._performance_stats['db_queries']} 次")
            logger.info(f"📊 Task 2.3并行计算性能摘要:")
            logger.info(f"   • 并行计算时间: {self._performance_stats['parallel_computation_time']:.2f}s")
            logger.info(f"   • 并行加速比: {self._performance_stats['parallel_speedup']:.2f}x")
            logger.info(f"   • 总体成功率: {success_rate:.2%}")
            
            # 🎯 策略权重系统：恢复原始权重配置，避免影响后续调用
            if original_weights:
                self.default_weights = original_weights
                logger.debug("🔄 权重配置已恢复为默认值")
            
            return schedule_results
            
        except Exception as e:
            logger.error(f"❌ 优化版排产执行失败: {e}")
            
            # 🎯 策略权重系统：异常情况下也要恢复原始权重
            if 'original_weights' in locals() and original_weights:
                self.default_weights = original_weights
                logger.debug("🔄 异常情况下权重配置已恢复")
            
            return []
            
    def _get_parallel_engine(self):
        """获取并行计算引擎实例"""
        try:
            from app.services.parallel_scheduling_engine import ParallelSchedulingEngine
            
            # 集成多级缓存管理器
            if hasattr(self, 'multilevel_cache') and self.multilevel_cache:
                parallel_engine = ParallelSchedulingEngine(multilevel_cache=self.multilevel_cache)
            else:
                parallel_engine = ParallelSchedulingEngine()
            
            # 启动引擎
            parallel_engine.start()
            logger.info("✅ 并行计算引擎已启动")
            return parallel_engine
            
        except Exception as e:
            logger.warning(f"并行计算引擎初始化失败: {e}")
            return None
    
    def _parallel_algorithm_selection(self, wait_lots: List[Dict], available_equipment: List[Dict], 
                                    parallel_engine) -> Tuple:
        """使用并行计算加速算法选择过程"""
        try:
            # 使用并行引擎进行多维度分析
            # 这里可以并行计算不同的决策因子
            selected_algorithm, decision_log = self.algorithm_selector.select_optimal_algorithm(
                wait_lots=wait_lots,
                available_equipment=available_equipment,
                constraints={'time_limit': 600},
                requirements={'accuracy_requirement': 0.8}
            )
            return selected_algorithm, decision_log
            
        except Exception as e:
            logger.warning(f"并行算法选择失败，回退到串行: {e}")
            return self.algorithm_selector.select_optimal_algorithm(
                wait_lots=wait_lots,
                available_equipment=available_equipment,
                constraints={'time_limit': 600},
                requirements={'accuracy_requirement': 0.8}
            )
    
    def _execute_heuristic_scheduling_with_parallel(self, wait_lots: List[Dict], 
                                                  preloaded_data: Dict, parallel_engine) -> List[Dict]:
        """🚀 使用并行计算的启发式排产算法"""
        logger.info("🚀 执行并行优化启发式排产算法...")
        
        if not parallel_engine or len(wait_lots) < 20:
            # 小规模数据或并行引擎不可用时使用串行算法
            return self._execute_heuristic_scheduling_optimized(wait_lots, preloaded_data)
        
        try:
            # 使用并行引擎进行设备匹配评分
            available_equipment = preloaded_data.get('equipment_status', [])
            available_statuses = ['Run', 'IDLE', 'Wait', 'READY', 'ONLINE', 'SetupRun', '']
            filtered_equipment = [eqp for eqp in available_equipment 
                                if eqp.get('STATUS', '').strip() in available_statuses]
            
            # 并行计算设备匹配评分
            parallel_results = parallel_engine.parallel_equipment_scoring(
                lots=wait_lots,
                equipment=filtered_equipment,
                algorithm_type='heuristic'
            )
            
            if not parallel_results:
                logger.warning("并行计算失败，回退到串行算法")
                return self._execute_heuristic_scheduling_optimized(wait_lots, preloaded_data)
            
            # 根据并行计算结果生成排产方案
            scheduled_lots = []
            equipment_priorities = {}
            
            for result in parallel_results:
                lot_id = result.get('LOT_ID', '')
                handler_id = result.get('HANDLER_ID', '')
                
                if handler_id not in equipment_priorities:
                    equipment_priorities[handler_id] = 0
                equipment_priorities[handler_id] += 1
                
                # 构建完整的排产记录
                scheduled_lot = {
                    'LOT_ID': lot_id,
                    'DEVICE': result.get('DEVICE', ''),
                    'STAGE': result.get('STAGE', ''),
                    'GOOD_QTY': result.get('GOOD_QTY', 0),
                    'HANDLER_ID': handler_id,
                    'TESTER_ID': result.get('TESTER_ID', ''),
                    'COMPREHENSIVE_SCORE': result.get('SCORE', 0),
                    'MATCH_TYPE': result.get('MATCH_TYPE', '并行计算'),
                    'EXECUTION_PRIORITY': equipment_priorities[handler_id],
                    'ALGORITHM': 'heuristic_parallel',
                    'SCHEDULED_TIME': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                scheduled_lots.append(scheduled_lot)
            
            # 保存到数据库
            if scheduled_lots:
                self._save_to_database(scheduled_lots)
            
            logger.info(f"🎉 并行启发式排产完成，生成 {len(scheduled_lots)} 个排产结果")
            return scheduled_lots
            
        except Exception as e:
            logger.error(f"并行启发式排产失败: {e}")
            return self._execute_heuristic_scheduling_optimized(wait_lots, preloaded_data)
    
    def _execute_hybrid_scheduling_with_parallel(self, wait_lots: List[Dict], 
                                               preloaded_data: Dict, parallel_engine) -> List[Dict]:
        """🚀 使用并行计算的混合算法"""
        logger.info("🎯 执行并行优化混合算法...")
        
        if not parallel_engine or len(wait_lots) < 50:
            # 小规模数据或并行引擎不可用时使用普通混合算法
            return self._execute_hybrid_scheduling(wait_lots, preloaded_data)
        
        try:
            # 第一阶段：并行启发式算法生成初始解
            logger.debug("🔄 第一阶段：并行启发式算法生成初始解")
            initial_solution = self._execute_heuristic_scheduling_with_parallel(
                wait_lots, preloaded_data, parallel_engine
            )
            
            if not initial_solution:
                return []
            
            # 第二阶段：OR-Tools优化（保持串行，因为OR-Tools内部已有优化）
            logger.debug("🔄 第二阶段：OR-Tools局部优化")
            # 选择评分较低的批次进行重新优化
            low_score_lots = [lot for lot in initial_solution 
                            if lot.get('COMPREHENSIVE_SCORE', 0) < 80]
            
            if low_score_lots and len(low_score_lots) <= 20:
                # 对低分批次使用OR-Tools重新计算
                available_equipment = preloaded_data.get('equipment_status', [])
                available_statuses = ['Run', 'IDLE', 'Wait', 'READY', 'ONLINE', 'SetupRun', '']
                filtered_equipment = [eqp for eqp in available_equipment 
                                    if eqp.get('STATUS', '').strip() in available_statuses]
                
                # 重新构造待排产批次格式
                reoptimize_lots = []
                for scheduled_lot in low_score_lots:
                    original_lot = next((lot for lot in wait_lots 
                                       if lot.get('LOT_ID') == scheduled_lot.get('LOT_ID')), None)
                    if original_lot:
                        reoptimize_lots.append(original_lot)
                
                if reoptimize_lots:
                    optimized_results = self._execute_ortools_scheduling(reoptimize_lots, filtered_equipment)
                    
                    # 替换优化后的结果
                    for optimized in optimized_results:
                        # 在初始解中找到对应的批次并替换
                        for i, initial in enumerate(initial_solution):
                            if initial.get('LOT_ID') == optimized.get('LOT_ID'):
                                initial_solution[i] = optimized
                                break
            
            logger.info(f"🎉 并行混合算法完成，生成 {len(initial_solution)} 个排产结果")
            return initial_solution
            
        except Exception as e:
            logger.error(f"并行混合算法失败: {e}")
            return self._execute_hybrid_scheduling(wait_lots, preloaded_data)

    def _execute_heuristic_scheduling_optimized(self, wait_lots: List[Dict], preloaded_data: Dict) -> List[Dict]:
        """🚀 增强版：启发式排产算法（支持特殊阶段规则）"""
        logger.info("🚀 执行增强版启发式排产算法...")
        
        # 🔥 生成排产会话ID，用于跟踪失败记录
        from datetime import datetime
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        logger.info(f"🆔 排产会话ID: {session_id}")
        
        scheduled_lots = []
        equipment_priorities = {}
        
        # 🚀 增强：特殊阶段分类排序 + 产品优先级=0绝对优先权
        def get_enhanced_priority_score(lot):
            """
            增强版优先级计算，支持产品优先级=0的绝对优先权机制
            
            优先级顺序：
            1. 产品优先级=0 (绝对优先权) - 10000级别
            2. 特殊阶段 (BTT/BAKING/LSTR) - 1000级别
            3. 普通批次 - 100级别
            """
            try:
                # 🔥 新增：产品优先级=0绝对优先权检查
                device_priority = self._get_device_absolute_priority(lot, preloaded_data)
                if device_priority > 0:
                    logger.info(f"🔥 产品优先级=0 绝对优先权: {lot.get('LOT_ID', '')} - {lot.get('DEVICE', '')}")
                    return 10000 + device_priority  # 绝对优先权 - 10000级别
                
                # 🚀 特殊阶段分类排序
                special_stage = self._is_special_stage_lot(lot)
                
                if special_stage in ['BTT', 'BAKING']:
                    # BTT和烘烤批次使用特殊评分
                    return 1000 + self._calculate_btt_baking_priority_score(lot, preloaded_data)
                elif special_stage == 'LSTR':
                    # LSTR批次使用特殊评分，但稍低于BTT/BAKING
                    base_score = self._calculate_btt_baking_priority_score(lot, preloaded_data)
                    return 1000 + base_score * 0.9  # 稍微降低优先级
                else:
                    # 普通批次使用标准评分
                    return self._get_lot_priority_score(lot, preloaded_data)
                    
            except Exception as e:
                logger.error(f"计算增强优先级评分失败: {e}")
                return 0.0
        
        # 🔧 确定性排序：添加稳定排序键避免不同机器结果不一致
        sorted_lots = sorted(wait_lots, key=lambda lot: (
            -get_enhanced_priority_score(lot),  # 主要排序键（负数实现降序）
            lot.get('LOT_ID', ''),              # 稳定排序键1：批次ID
            lot.get('CREATE_TIME', ''),         # 稳定排序键2：创建时间
            lot.get('DEVICE', ''),              # 稳定排序键3：产品名称
        ))
        
        # 添加待排产批次到预加载数据中，供优先级计算使用
        preloaded_data['wait_lots'] = wait_lots
        
        for lot in sorted_lots:
            lot_id = lot.get('LOT_ID', '')
            
            # 获取配置需求
            lot_requirements = self.get_lot_configuration_requirements_optimized(lot, preloaded_data)
            if not lot_requirements:
                logger.debug(f"⚠️ 批次 {lot_id} 无法获取配置需求，跳过")
                # 🔥 集成失败跟踪：记录配置需求获取失败
                self.failure_tracker.add_failed_lot(
                    lot, 
                    "配置需求获取失败",
                    f"未找到匹配的测试规范 (DEVICE={lot.get('DEVICE', '')}, STAGE={lot.get('STAGE', '')}, PKG_PN={lot.get('PKG_PN', '')})",
                    "real_scheduling_v2.0",
                    session_id  # 🔥 传递会话ID
                )
                continue
            
            # 查找合适设备
            equipment_candidates = self.find_suitable_equipment_optimized(lot, lot_requirements, preloaded_data)
            if not equipment_candidates:
                logger.debug(f"⚠️ 批次 {lot_id} 无合适设备，跳过")
                # 🔥 集成失败跟踪：记录无合适设备失败
                self.failure_tracker.add_failed_lot(
                    lot,
                    "无合适设备",
                    f"没有可处理该器件的设备 (DEVICE={lot.get('DEVICE', '')}, STAGE={lot.get('STAGE', '')}, 配置需求已找到但无匹配设备)",
                    "real_scheduling_v2.0",
                    session_id  # 🔥 传递会话ID
                )
                continue
            
            # 选择最佳设备
            best_candidate = equipment_candidates[0]
            best_equipment = best_candidate['equipment']
            handler_id = best_equipment.get('HANDLER_ID')
            
            # 设备优先级管理
            if handler_id not in equipment_priorities:
                equipment_priorities[handler_id] = 0
            equipment_priorities[handler_id] += 1
            
            # 构建排产记录
            scheduled_lot = self._build_scheduled_lot_record(lot, best_candidate, equipment_priorities[handler_id])
            scheduled_lots.append(scheduled_lot)
        
        # 保存成功排产记录到数据库
        if scheduled_lots:
            self._save_to_database(scheduled_lots)
        
        # 🔥 保存失败批次记录到数据库
        self.failure_tracker.save_to_database()
        
        # 🔥 记录统计信息
        total_lots = len(wait_lots)
        success_lots = len(scheduled_lots)
        failed_lots = total_lots - success_lots
        
        if failed_lots > 0:
            logger.warning(f"⚠️ 排产完成：成功 {success_lots}/{total_lots}，失败 {failed_lots}个批次已记录")
        else:
            logger.info(f"✅ 排产完成：成功 {success_lots}/{total_lots}，全部批次排产成功")
        
        # 🔥 清空失败跟踪器，为下次排产做准备
        self.failure_tracker.clear()
        
        return scheduled_lots







    def _get_device_absolute_priority(self, lot: Dict, preloaded_data: Dict) -> float:
        """
        🔥 新增：产品优先级=0绝对优先权计算
        
        返回值：
        - 0: 非绝对优先权批次
        - >0: 绝对优先权评分（基于Gap、匹配等级、温度权重、批次优先级、FIFO）
        """
        try:
            device = lot.get('DEVICE', '')
            if not device:
                return 0.0
            
            # 1. 检查是否为产品优先级=0的DEVICE
            device_priority_data = preloaded_data.get('device_priority', [])
            absolute_priority_config = None
            
            for config in device_priority_data:
                if config.get('DEVICE') == device and int(config.get('PRIORITY', 999)) == 0:
                    absolute_priority_config = config
                    break
            
            if not absolute_priority_config:
                return 0.0  # 非绝对优先权
            
            logger.debug(f"🔥 发现绝对优先权DEVICE: {device}")
            
            # 2. 计算Gap = setup_qty - 当前开机数量
            setup_qty = int(absolute_priority_config.get('SETUP_QTY', 0))
            current_running_count = self._get_current_device_running_count(device, preloaded_data)
            gap = setup_qty - current_running_count
            
            logger.debug(f"🔥 Gap计算: setup_qty={setup_qty}, 当前开机={current_running_count}, Gap={gap}")
            
            # 3. 计算绝对优先权评分
            # 基础评分 = Gap * 1000 (Gap越大优先级越高)
            gap_score = max(0, gap) * 1000
            
            # 4. 获取匹配等级评分 (暂时简化，后续可以详细实现)
            match_level_score = self._get_absolute_priority_match_score(lot, preloaded_data)
            
            # 5. 获取温度权重评分 (暂时简化，后续可以详细实现)
            temperature_score = self._get_absolute_priority_temperature_score(lot, preloaded_data)
            
            # 6. 获取批次优先级=0评分
            lot_priority_score = self._get_absolute_priority_lot_score(lot, preloaded_data)
            
            # 7. 获取FIFO评分（基于CREATE_TIME）
            fifo_score = self._get_absolute_priority_fifo_score(lot)
            
            # 8. 综合评分：Gap(主要) + 匹配等级 + 温度权重 + 批次优先级 + FIFO
            final_score = (gap_score * 100 +      # Gap权重最高
                          match_level_score * 10 +  # 匹配等级权重
                          temperature_score * 5 +    # 温度权重
                          lot_priority_score * 2 +   # 批次优先级权重
                          fifo_score * 1)            # FIFO权重最低
            
            logger.info(f"🔥 绝对优先权评分 - {lot.get('LOT_ID', '')}: "
                       f"Gap={gap_score}, 匹配={match_level_score}, 温度={temperature_score}, "
                       f"批次={lot_priority_score}, FIFO={fifo_score}, 总分={final_score}")
            
            return final_score
            
        except Exception as e:
            logger.error(f"计算产品绝对优先权失败: {e}")
            return 0.0

    def _get_current_device_running_count(self, device: str, preloaded_data: Dict) -> int:
        """获取当前该DEVICE的开机数量"""
        try:
            equipment_status = preloaded_data.get('equipment_status', [])
            running_statuses = ['Run', 'ONLINE', 'SetupRun']  # 视为运行状态
            
            count = 0
            for eqp in equipment_status:
                if (eqp.get('DEVICE') == device and 
                    eqp.get('STATUS', '') in running_statuses):
                    count += 1
            
            logger.debug(f"📊 当前{device}开机数量: {count}")
            return count
            
        except Exception as e:
            logger.error(f"获取设备开机数量失败: {e}")
            return 0

    def _get_absolute_priority_match_score(self, lot: Dict, preloaded_data: Dict) -> float:
        """获取绝对优先权匹配等级评分"""
        try:
            # 暂时简化实现，后续可以根据实际匹配等级计算
            # same_setup=100, small_change=80, big_change=60
            return 80.0  # 默认中等匹配评分
        except Exception as e:
            logger.error(f"计算绝对优先权匹配评分失败: {e}")
            return 0.0

    def _get_absolute_priority_temperature_score(self, lot: Dict, preloaded_data: Dict) -> float:
        """获取绝对优先权温度权重评分"""
        try:
            # 获取用户配置的温度权重
            temperature_weight = self._get_configured_temperature_weight(preloaded_data)
            
            if temperature_weight <= 0:
                return 0.0  # 温度权重为0时，不考虑温度影响
            
            # 计算当前批次的温度类型评分
            temperature_score = self._calculate_temperature_type_score(lot)
            
            # 计算设备温度切换成本
            equipment_temp_score = self._calculate_equipment_temperature_continuity_score(lot, preloaded_data)
            
            # 综合温度评分 = 温度类型评分 * 权重 + 设备连续性评分 * 权重
            final_score = (temperature_score * 0.6 + equipment_temp_score * 0.4) * temperature_weight
            
            logger.debug(f"🌡️ 温度评分 - {lot.get('LOT_ID', '')}: "
                        f"类型={temperature_score}, 连续性={equipment_temp_score}, "
                        f"权重={temperature_weight}, 最终={final_score}")
            
            return final_score
            
        except Exception as e:
            logger.error(f"计算绝对优先权温度评分失败: {e}")
            return 0.0

    def _get_configured_temperature_weight(self, preloaded_data: Dict) -> float:
        """获取用户配置的温度权重"""
        try:
            # 从预加载数据中获取当前用户的策略权重配置
            # 这里需要集成前端的策略选择，暂时使用默认智能策略
            from app.models import SchedulingConfig
            
            # 获取当前用户的配置
            config = SchedulingConfig.get_active_config(user_id=None, strategy_name='intelligent')
            if config and hasattr(config, 'temperature_weight'):
                weight = float(config.temperature_weight) if config.temperature_weight else 0.0
                logger.debug(f"🌡️ 获取配置温度权重: {weight}")
                return weight
            
            # 默认返回0（不考虑温度影响）
            return 0.0
            
        except Exception as e:
            logger.error(f"获取温度权重配置失败: {e}")
            return 0.0

    def _calculate_temperature_type_score(self, lot: Dict) -> float:
        """计算批次温度类型评分"""
        try:
            stage = lot.get('STAGE', '').upper()
            
            # 温度类型优先级：常温 > 升温 > 降温
            if any(temp_stage in stage for temp_stage in ['ROOM', 'UIS', 'TRIM']):
                return 100.0  # 常温工序，无需温度切换
            elif 'HOT' in stage:
                return 60.0   # 升温工序，需要升温时间
            elif 'COLD' in stage:
                return 30.0   # 降温工序，需要降温时间
            else:
                return 80.0   # 未知温度工序，默认中等评分
                
        except Exception as e:
            logger.error(f"计算温度类型评分失败: {e}")
            return 0.0

    def _calculate_equipment_temperature_continuity_score(self, lot: Dict, preloaded_data: Dict) -> float:
        """计算设备温度连续性评分（在相同温度下连续生产的优势）"""
        try:
            lot_stage = lot.get('STAGE', '').upper()
            lot_temp_type = self._get_temperature_type_from_stage(lot_stage)
            
            equipment_status = preloaded_data.get('equipment_status', [])
            
            # 查找当前正在运行相同温度工序的设备数量
            same_temp_running_count = 0
            total_running_count = 0
            
            for eqp in equipment_status:
                if eqp.get('STATUS', '') in ['Run', 'ONLINE', 'SetupRun']:
                    total_running_count += 1
                    eqp_stage = eqp.get('STAGE', '').upper()
                    eqp_temp_type = self._get_temperature_type_from_stage(eqp_stage)
                    
                    if eqp_temp_type == lot_temp_type:
                        same_temp_running_count += 1
            
            # 计算温度连续性评分
            if total_running_count == 0:
                return 50.0  # 无设备运行时，默认中等评分
            
            # 相同温度设备占比越高，连续性评分越高
            continuity_ratio = same_temp_running_count / total_running_count
            continuity_score = continuity_ratio * 100
            
            logger.debug(f"🌡️ 温度连续性 - {lot.get('LOT_ID', '')}: "
                        f"类型={lot_temp_type}, 相同温度设备={same_temp_running_count}/{total_running_count}, "
                        f"评分={continuity_score}")
            
            return continuity_score
            
        except Exception as e:
            logger.error(f"计算设备温度连续性评分失败: {e}")
            return 0.0

    def _get_temperature_type_from_stage(self, stage: str) -> str:
        """从STAGE中提取温度类型"""
        try:
            stage_upper = stage.upper()
            
            if any(temp_stage in stage_upper for temp_stage in ['ROOM', 'UIS', 'TRIM']):
                return 'ROOM'  # 常温
            elif 'HOT' in stage_upper:
                return 'HOT'   # 升温
            elif 'COLD' in stage_upper:
                return 'COLD'  # 降温
            else:
                return 'UNKNOWN'  # 未知
                
        except Exception as e:
            logger.error(f"提取温度类型失败: {e}")
            return 'UNKNOWN'

    def _get_absolute_priority_lot_score(self, lot: Dict, preloaded_data: Dict) -> float:
        """获取绝对优先权批次优先级=0评分"""
        try:
            lot_id = lot.get('LOT_ID', '')
            lot_priority_data = preloaded_data.get('lot_priority', [])
            
            for config in lot_priority_data:
                # 简化匹配逻辑：如果LOT_ID或DEVICE匹配且priority=0
                if ((config.get('LOT_ID') == lot_id or 
                     config.get('DEVICE') == lot.get('DEVICE', '')) and
                    int(config.get('PRIORITY', 999)) == 0):
                    logger.debug(f"🔥 发现批次优先级=0: {lot_id}")
                    return 1000.0  # 批次优先级=0高分
            
            return 0.0  # 普通批次
            
        except Exception as e:
            logger.error(f"计算绝对优先权批次评分失败: {e}")
            return 0.0

    def _get_absolute_priority_fifo_score(self, lot: Dict) -> float:
        """获取绝对优先权FIFO评分（基于CREATE_TIME）"""
        try:
            from datetime import datetime
            
            create_time_str = lot.get('CREATE_TIME', '')
            if not create_time_str:
                return 0.0
            
            # 解析CREATE_TIME
            try:
                create_time = datetime.strptime(create_time_str, '%Y-%m-%d %H:%M:%S')
            except:
                # 尝试其他时间格式
                try:
                    create_time = datetime.strptime(create_time_str, '%Y-%m-%d')
                except:
                    return 0.0
            
            # 计算与当前时间的差值（小时）
            now = datetime.now()
            hours_diff = (now - create_time).total_seconds() / 3600
            
            # 时间越早，优先级越高（FIFO）
            fifo_score = min(999, max(0, hours_diff))
            
            logger.debug(f"📅 FIFO评分 - {lot.get('LOT_ID', '')}: 创建时间差={hours_diff:.1f}小时, 评分={fifo_score}")
            return fifo_score
            
        except Exception as e:
            logger.error(f"计算绝对优先权FIFO评分失败: {e}")
            return 0.0

    def _get_lot_priority_score(self, lot: Dict, preloaded_data: Dict) -> float:
        """计算批次优先级评分（包含温度权重）"""
        try:
            # 使用预加载数据计算优先级
            business_score = self.calculate_business_priority_score_optimized(lot, preloaded_data)
            deadline_score = self.calculate_deadline_urgency_score(lot, 1.0)  # 假设1小时处理时间
            
            # 🌡️ 新增：温度权重评分
            temperature_score = self._calculate_normal_priority_temperature_score(lot, preloaded_data)
            
            # 获取配置的温度权重比例
            temperature_weight = self._get_configured_temperature_weight(preloaded_data)
            
            # 根据温度权重调整评分权重
            if temperature_weight > 0:
                # 当有温度权重时，调整各项权重比例
                temp_factor = min(temperature_weight / 100.0, 0.3)  # 温度权重最多占30%
                business_factor = 0.6 * (1 - temp_factor)
                deadline_factor = 0.4 * (1 - temp_factor)
                
                final_score = (business_score * business_factor + 
                              deadline_score * deadline_factor + 
                              temperature_score * temp_factor)
                
                logger.debug(f"🌡️ 常规优先级评分 - {lot.get('LOT_ID', '')}: "
                            f"业务={business_score:.1f}*{business_factor:.2f}, "
                            f"交期={deadline_score:.1f}*{deadline_factor:.2f}, "
                            f"温度={temperature_score:.1f}*{temp_factor:.2f}, "
                            f"总分={final_score:.1f}")
            else:
                # 无温度权重时，使用原有逻辑
                final_score = business_score * 0.6 + deadline_score * 0.4
            
            return final_score
            
        except Exception as e:
            logger.error(f"计算批次优先级评分失败: {e}")
            return 0.0

    def _calculate_normal_priority_temperature_score(self, lot: Dict, preloaded_data: Dict) -> float:
        """计算常规优先级的温度评分"""
        try:
            # 复用绝对优先权的温度评分逻辑，但降低权重影响
            temperature_score = self._calculate_temperature_type_score(lot)
            equipment_temp_score = self._calculate_equipment_temperature_continuity_score(lot, preloaded_data)
            
            # 常规优先级的温度评分相对较低
            final_score = (temperature_score * 0.7 + equipment_temp_score * 0.3)
            
            logger.debug(f"🌡️ 常规温度评分 - {lot.get('LOT_ID', '')}: "
                        f"类型={temperature_score}, 连续性={equipment_temp_score}, "
                        f"最终={final_score}")
            
            return final_score
            
        except Exception as e:
            logger.error(f"计算常规温度评分失败: {e}")
            return 0.0

    def _safe_datetime_field(self, value) -> str:
        """安全处理datetime字段，避免MySQL错误"""
        try:
            if value is None or value == '':
                # 使用当前时间作为默认值
                from datetime import datetime
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, str) and value.strip():
                return value.strip()
            else:
                from datetime import datetime
                return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            from datetime import datetime
            return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    def _build_scheduled_lot_record(self, lot: Dict, best_candidate: Dict, priority: int) -> Dict:
        """构建排产记录 - 增强版：支持LOT_TYPE智能分类"""
        best_equipment = best_candidate['equipment']
        
        # 🚀 增强：智能LOT_TYPE分类
        display_lot_type = self._get_lot_type_classification(lot)
        
        # 🔧 修复：确保PKG_PN有值
        pkg_pn = lot.get('PKG_PN', '').strip()
        if not pkg_pn:
            device = lot.get('DEVICE', '')
            pkg_pn = f"PKG_{device}" if device else 'N/A'
        
        # 🔧 修复：确保评分数据存在
        match_type = best_candidate.get('match_type', 'N/A')
        comprehensive_score = best_candidate.get('comprehensive_score', 0)
        processing_time = best_candidate.get('processing_time', 0)
        changeover_time = best_candidate.get('changeover_time', 0)
        
        # 🔧 修复：添加缺失的selection_reason字段
        selection_reason = best_candidate.get('selection_reason', f"{match_type}匹配")
        
        return {
            'PRIORITY': priority,
            'HANDLER_ID': best_equipment.get('HANDLER_ID', 'N/A'),
            'LOT_ID': lot.get('LOT_ID', ''),
            'LOT_TYPE': display_lot_type,  # 🔧 修复：使用正确的LOT_TYPE
            'GOOD_QTY': lot.get('GOOD_QTY', 0),
            'PROD_ID': lot.get('PROD_ID', ''),
            'DEVICE': lot.get('DEVICE', ''),
            'CHIP_ID': lot.get('CHIP_ID', ''),
            'PKG_PN': pkg_pn,  # 🔧 修复：确保PKG_PN有值
            'PO_ID': lot.get('PO_ID', ''),
            'STAGE': lot.get('STAGE', ''),
            'STEP': lot.get('STEP', ''),  # 🔧 CRITICAL FIX: 添加STEP字段到排产记录
            'WIP_STATE': lot.get('WIP_STATE', 'QUEUE'),
            'PROC_STATE': lot.get('PROC_STATE', ''),
            'HOLD_STATE': lot.get('HOLD_STATE', ''),
            'FLOW_ID': lot.get('FLOW_ID', ''),
            'FLOW_VER': lot.get('FLOW_VER', ''),
            'RELEASE_TIME': self._safe_datetime_field(lot.get('RELEASE_TIME')),  # 🔧 修复：安全处理datetime字段
            'FAC_ID': lot.get('FAC_ID', 'FAC1'),
            'CREATE_TIME': self._safe_datetime_field(lot.get('CREATE_TIME')),  # 🔧 修复：安全处理datetime字段
            # 🔧 修复：确保智能排产详情数据完整
            'match_type': match_type,
            'comprehensive_score': round(comprehensive_score, 1) if comprehensive_score else 0,
            'processing_time': round(processing_time, 2) if processing_time else 0,
            'changeover_time': int(changeover_time) if changeover_time else 0,
            'selection_reason': selection_reason,  # 🔧 修复：添加缺失字段
            'algorithm_version': 'v3.0-enhanced',
            'priority_score': round(comprehensive_score, 1) if comprehensive_score else 0,  # 🔧 修复：添加priority_score字段
            'estimated_hours': round(processing_time, 2) if processing_time else 0,  # 🔧 修复：添加estimated_hours字段
            'equipment_status': best_equipment.get('STATUS', 'AVAILABLE')  # 🔧 修复：添加equipment_status字段
        }

    def clear_all_caches(self):
        """🧹 清理所有缓存"""
        # 安全清理缓存
        if hasattr(self, '_cache_data') and self._cache_data:
            self._cache_data = {key: None for key in self._cache_data}
        
        if hasattr(self, '_cache_timestamps'):
            self._cache_timestamps.clear()
            
        self._computation_cache = {
            'lot_requirements': {},
            'equipment_matches': {},
            'score_calculations': {}
        }
        
        # 🚀 清理智能懒加载缓存
        if hasattr(self, '_lazy_cache'):
            self._lazy_cache.clear()
        if hasattr(self, '_preload_timestamp'):
            self._preload_timestamp = None
        if hasattr(self, '_preload_context'):
            self._preload_context = None
        if hasattr(self, '_data_access_patterns'):
            self._data_access_patterns.clear()
        
        logger.info("🧹 已清理所有缓存（包括智能懒加载缓存）")

    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        stats = self._performance_stats.copy()
        
        # 添加懒加载缓存统计
        if hasattr(self, '_lazy_cache'):
            stats['lazy_cache'] = {
                'cache_size': len(self._lazy_cache),
                'data_types': list(self._data_access_patterns.keys()) if hasattr(self, '_data_access_patterns') else [],
                'access_patterns': self._data_access_patterns.copy() if hasattr(self, '_data_access_patterns') else {}
            }
        
        return stats

    def get_cache_status(self) -> Dict:
        """
        🚀 新增：获取缓存状态信息
        """
        current_time = time.time()
        cache_status = {
            'lazy_cache': {
                'total_entries': len(self._lazy_cache) if hasattr(self, '_lazy_cache') else 0,
                'expired_entries': 0,
                'fresh_entries': 0,
                'data_types': {}
            },
            'preload_status': {
                'last_preload': self._preload_timestamp if hasattr(self, '_preload_timestamp') else None,
                'preload_context': self._preload_context if hasattr(self, '_preload_context') else None,
                'age_seconds': current_time - self._preload_timestamp if hasattr(self, '_preload_timestamp') and self._preload_timestamp else None
            },
            'access_patterns': self._data_access_patterns.copy() if hasattr(self, '_data_access_patterns') else {}
        }
        
        # 分析懒加载缓存状态
        if hasattr(self, '_lazy_cache'):
            for cache_key, cache_data in self._lazy_cache.items():
                data_type = cache_key.split('_')[0]
                cache_age = current_time - cache_data.get('timestamp', 0)
                ttl = self._get_data_ttl(data_type)
                
                if cache_age > ttl:
                    cache_status['lazy_cache']['expired_entries'] += 1
                else:
                    cache_status['lazy_cache']['fresh_entries'] += 1
                
                if data_type not in cache_status['lazy_cache']['data_types']:
                    cache_status['lazy_cache']['data_types'][data_type] = {'count': 0, 'total_age': 0}
                
                cache_status['lazy_cache']['data_types'][data_type]['count'] += 1
                cache_status['lazy_cache']['data_types'][data_type]['total_age'] += cache_age
        
        return cache_status

    def _select_best_handler_config_by_priority(self, device: str, stage: str, matching_recipes: List[Dict], preloaded_data: Dict) -> Optional[Dict]:
        """
        🚀 新增：通过产品优先级表匹配最佳HANDLER_CONFIG
        
        当DEVICE+STAGE查询出多个HANDLER_CONFIG结果时，参考产品优先级表中设定的HANDLER_CONFIG信息来匹配优先顺序
        
        Args:
            device: 产品型号
            stage: 工序阶段  
            matching_recipes: 匹配的recipe记录列表
            preloaded_data: 预加载数据（包含device_priority）
            
        Returns:
            Optional[Dict]: 最佳匹配的recipe记录，如果没有找到返回None
        """
        try:
            device_priority_data = preloaded_data.get('device_priority', [])
            if not device_priority_data:
                logger.warning(f"⚠️ 产品优先级表为空，无法进行优先级匹配")
                return None
            
            # 1. 查找产品优先级表中对应的HANDLER_CONFIG配置
            priority_handler_configs = []
            for priority_record in device_priority_data:
                # 🔧 修复：使用小写字段名匹配数据库实际字段
                priority_device = (priority_record.get('device') or '').strip()
                priority_stage = (priority_record.get('stage') or '').strip()  
                priority_handler_config = (priority_record.get('handler_config') or '').strip()
                
                # 精确匹配DEVICE和STAGE
                if (priority_device == device and 
                    priority_stage == stage and 
                    priority_handler_config):
                    
                    priority_handler_configs.append({
                        'HANDLER_CONFIG': priority_handler_config,
                        'PRIORITY': priority_record.get('priority', 999),  # 🔧 修复：使用小写字段名
                        'RECORD': priority_record
                    })
            
            if not priority_handler_configs:
                logger.debug(f"📋 产品优先级表中未找到{device}-{stage}的HANDLER_CONFIG配置")
                return None
            
            # 2. 按优先级排序（PRIORITY值越小越优先）
            priority_handler_configs.sort(key=lambda x: x['PRIORITY'])
            
            # 3. 根据优先级顺序匹配recipe记录
            for priority_config in priority_handler_configs:
                target_handler_config = priority_config['HANDLER_CONFIG']
                
                for recipe in matching_recipes:
                    recipe_handler_config = (recipe.get('HANDLER_CONFIG') or '').strip()
                    
                    # 精确匹配HANDLER_CONFIG
                    if recipe_handler_config == target_handler_config:
                        # 添加优先级信息到结果中
                        result_recipe = recipe.copy()
                        result_recipe['RECIPE_SOURCE'] = 'PRIORITY_MATCH'
                        result_recipe['PRIORITY_LEVEL'] = priority_config['PRIORITY']
                        
                        logger.info(f"🎯 优先级匹配成功: {device}-{stage} -> HANDLER_CONFIG={target_handler_config} (优先级:{priority_config['PRIORITY']})")
                        return result_recipe
            
            logger.debug(f"📋 优先级表中的HANDLER_CONFIG在recipe中未找到匹配")
            return None
            
        except Exception as e:
            logger.error(f"❌ 优先级匹配失败: {e}")
            return None

    def _select_best_recipe_by_score(self, matching_recipes: List[Dict]) -> Dict:
        """
        🔧 传统评分方式选择最佳recipe配置
        
        Args:
            matching_recipes: 匹配的recipe记录列表
            
        Returns:
            Dict: 评分最高的recipe记录
        """
        best_recipe = None
        best_score = -1
        
        for recipe in matching_recipes:
            score = 0
            
            # HANDLER_CONFIG存在 +10分
            if (recipe.get('HANDLER_CONFIG') or '').strip():
                score += 10
            
            # KIT_PN存在 +5分  
            if (recipe.get('KIT_PN') or '').strip():
                score += 5
            
            # APPROVAL_STATE为Released +3分
            if (recipe.get('APPROVAL_STATE') or '').strip() == 'Released':
                score += 3
            
            # 有VERSION信息 +1分
            if (recipe.get('VERSION') or '').strip():
                score += 1
            
            if score > best_score:
                best_score = score
                best_recipe = recipe
        
        # 如果没有找到合适的，返回第一个
        if best_recipe is None and matching_recipes:
            best_recipe = matching_recipes[0]
            best_score = 0
        
        logger.info(f"🔧 评分方式选择recipe: HANDLER_CONFIG={best_recipe.get('HANDLER_CONFIG', '')}, 评分={best_score}")
        return best_recipe

    def execute_intelligent_scheduling(self, algorithm: str = 'intelligent', optimization_target: str = 'balanced') -> Dict[str, Any]:
        """
        🚀 智能排产统一入口 - 兼容API调用
        这是为了兼容API v2调用而添加的入口方法，内部转发到优化后的排产算法
        
        Args:
            algorithm: 排产策略 (intelligent/deadline/product/value)
            optimization_target: 优化目标 (balanced/speed/accuracy)
            
        Returns:
            Dict: 排产结果
        """
        logger.info(f"🎯 智能排产统一入口调用: algorithm={algorithm}, target={optimization_target}")
        
        # 🔧 修复：转发到会保存数据库的方法，确保STEP字段正确传递
        result = self.execute_real_scheduling(
            algorithm=algorithm,
            user_id=None,  # API调用时无特定用户
            optimization_target=optimization_target
        )
        
        # 转换返回格式以兼容API期望
        if isinstance(result, dict) and 'schedule' in result:
            # 新格式：包含详细指标
            return {
                'success': True,
                'total_lots': result['metrics']['total_batches'],
                'scheduled_lots': result['metrics']['scheduled_batches'],
                'failed_lots': result['metrics']['failed_batches'],
                'success_rate': result['metrics']['success_rate'],
                'execution_time': result['metrics']['execution_time'],
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'schedule_data': result['schedule'],
                'metrics': result['metrics'],
                'api_version': 'v2_compatible'
            }
        else:
            # 旧格式：直接返回排产结果列表
            return {
                'success': True,
                'total_lots': len(result) if result else 0,
                'scheduled_lots': len(result) if result else 0,
                'failed_lots': 0,
                'success_rate': '100%' if result else '0%',
                'execution_time': 0.0,
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'schedule_data': result or [],
                'metrics': {},
                'api_version': 'v2_compatible'
            }

    def _load_priority_configs(self):
        """加载优先级配置数据到缓存"""
        import time
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._cache_timestamp and 
            current_time - self._cache_timestamp < self._cache_timeout and
            self._device_priority_cache is not None and
            self._lot_priority_cache is not None):
            return  # 缓存仍然有效
        
        logger.info("🔄 加载优先级配置数据到缓存...")
        
        # 加载设备优先级配置
        device_config_result = self.data_manager.get_table_data('devicepriorityconfig', per_page=1000)
        if device_config_result.get('success'):
            self._device_priority_cache = device_config_result.get('data', [])
            logger.info(f"✅ 缓存设备优先级配置: {len(self._device_priority_cache)} 条")
        else:
            self._device_priority_cache = []
            logger.warning("⚠️ 设备优先级配置加载失败")
        
        # 加载批次优先级配置
        lot_config_result = self.data_manager.get_table_data('lotpriorityconfig', per_page=1000)
        if lot_config_result.get('success'):
            self._lot_priority_cache = lot_config_result.get('data', [])
            logger.info(f"✅ 缓存批次优先级配置: {len(self._lot_priority_cache)} 条")
        else:
            self._lot_priority_cache = []
            logger.warning("⚠️ 批次优先级配置加载失败")
        
        # 更新缓存时间戳
        self._cache_timestamp = current_time
        
    def get_lot_configuration_requirements_optimized(self, lot: Dict, preloaded_data: Dict) -> Optional[Dict]:
        """
        🚀 优化版：获取批次配置需求 (使用预加载数据)
        增强版：支持更多特殊阶段，提高匹配成功率
        """
        try:
            # 使用预加载数据
            test_specs = preloaded_data.get('test_specs', [])
            recipe_files = preloaded_data.get('recipe_files', [])
            
            device = (lot.get('DEVICE') or '').strip()
            stage = (lot.get('STAGE') or '').strip()
            pkg_pn = (lot.get('PKG_PN') or '').strip()
            
            if not device or not stage:
                logger.warning(f"批次 {lot.get('LOT_ID')} 缺少DEVICE或STAGE信息")
                return None
            
            # 🔧 修复：严格的特殊阶段处理逻辑
            special_stages = {
                'BAKING': {
                    'EQP_CLASS': 'Oven',  # 烘箱专用
                    'REQUIRED_CONFIG': 'BAKING_OVEN',
                    'UPH': 1000,
                    'description': '烘箱工艺 - 必须在烘箱设备上执行'
                },
                'LSTR': {
                    'EQP_CLASS': 'LSTR', 
                    'REQUIRED_CONFIG': 'TAPE_REEL',
                    'UPH': 2000,
                    'description': '编带工艺 - 必须在编带设备上执行'
                },
                'BTT': {
                    'EQP_CLASS': 'Test',
                    'REQUIRED_CONFIG': 'BURN_IN',
                    'UPH': 500,
                    'description': '老化测试 - 必须在老化测试设备上执行'
                }
            }
            
            # 🔧 修复：检查是否为特殊阶段，并验证设备兼容性
            for special_stage, config in special_stages.items():
                if special_stage in stage.upper():
                    logger.info(f"🔍 检测到特殊阶段 - {device}-{stage}: {config['description']}")
                    
                    # 🚨 关键修复：验证是否有对应的设备支持此特殊阶段
                    compatible_equipment = self._check_special_stage_equipment_availability(
                        special_stage, config, preloaded_data
                    )
                    
                    if not compatible_equipment:
                        logger.error(f"❌ {special_stage}阶段无法排产 - EQP_STATUS中没有{config['EQP_CLASS']}类型设备")
                        return None  # 无法排产
                    
                    logger.info(f"✅ 找到{len(compatible_equipment)}台{config['EQP_CLASS']}设备支持{special_stage}阶段")
                    return {
                        'DEVICE': device,
                        'STAGE': stage,
                        'PKG_PN': pkg_pn or f"PKG_{device}",
                        'HB_PN': '',
                        'TB_PN': 'NA',
                        'KIT_PN': f"KIT_{device}_{special_stage}",
                        'HANDLER_CONFIG': config['REQUIRED_CONFIG'],
                        'EQP_CLASS': config['EQP_CLASS'],
                        'UPH': config['UPH'],
                        'TEST_SPEC_SOURCE': f'{special_stage}_VALIDATED',
                        'COMPATIBLE_EQUIPMENT': compatible_equipment
                    }
            
            # 从ET_FT_TEST_SPEC获取配置需求
            for spec in test_specs:
                spec_device = (spec.get('DEVICE') or '').strip()
                spec_stage = (spec.get('STAGE') or '').strip()
                spec_approval = (spec.get('APPROVAL_STATE') or '').strip()
                
                if (spec_device == device and 
                    self._is_stage_match(stage, spec_stage, preloaded_data) and
                    spec_approval == 'Released'):
                    
                    # 从测试规范获取基础配置
                    tb_pn = (spec.get('TB_PN') or '').strip() or 'NA'
                    hb_pn = (spec.get('HB_PN') or '').strip()
                    handler = (spec.get('HANDLER') or '').strip()  # 这是EQP_CLASS
                    
                    config = {
                        'DEVICE': device,
                        'STAGE': stage,
                        'HB_PN': hb_pn,
                        'TB_PN': tb_pn,
                        'HANDLER_CONFIG': '',  # 后续从recipe_file获取
                        'EQP_CLASS': handler,  # ET_FT_TEST_SPEC中的HANDLER字段对应EQP_CLASS
                        'PKG_PN': (spec.get('PKG_PN') or '').strip(),
                        'TESTER': (spec.get('TESTER') or '').strip(),
                        'UPH': spec.get('UPH', 0),
                        'TEST_SPEC_SOURCE': 'ET_FT_TEST_SPEC'
                    }
                    
                    # 🔧 修复：从recipe_file获取最优的KIT_PN和HANDLER_CONFIG
                    # 不再只取第一个匹配，而是评估所有匹配记录选择最优的
                    matching_recipes = []
                    for recipe in recipe_files:
                        if ((recipe.get('DEVICE') or '').strip() == device and 
                            (recipe.get('STAGE') or '').strip() == spec_stage and
                            (recipe.get('PKG_PN') or '').strip() == pkg_pn and
                            (recipe.get('APPROVAL_STATE') or '').strip() == 'Released'):
                            matching_recipes.append(recipe)
                    
                    if matching_recipes:
                        # 🚀 新增：通过产品优先级表匹配最佳HANDLER_CONFIG
                        best_recipe = self._select_best_handler_config_by_priority(
                            device, stage, matching_recipes, preloaded_data
                        )
                        
                        if best_recipe:
                            config['KIT_PN'] = (best_recipe.get('KIT_PN') or '').strip()
                            config['HANDLER_CONFIG'] = (best_recipe.get('HANDLER_CONFIG') or '').strip()
                            config['RECIPE_SOURCE'] = best_recipe.get('RECIPE_SOURCE', 'PRIORITY_MATCH')
                            logger.info(f"🎯 通过优先级匹配选择最佳HANDLER_CONFIG: {config['HANDLER_CONFIG']}")
                        else:
                            # 兜底：使用传统评分方式
                            best_recipe = self._select_best_recipe_by_score(matching_recipes)
                            config['KIT_PN'] = (best_recipe.get('KIT_PN') or '').strip()
                            config['HANDLER_CONFIG'] = (best_recipe.get('HANDLER_CONFIG') or '').strip()
                            config['RECIPE_SOURCE'] = 'SCORE_MATCH'
                            logger.warning(f"⚠️ 使用评分方式选择HANDLER_CONFIG: {config['HANDLER_CONFIG']}")
                    else:
                        logger.warning(f"⚠️ 未找到匹配的recipe配置，HANDLER_CONFIG将为空")
                    
                    # 获取权威UPH数据
                    uph_data = preloaded_data.get('uph_data', {})
                    uph_key = f"{device}|{stage}"
                    authoritative_uph = uph_data.get(uph_key, {}).get('UPH')
                    if authoritative_uph:
                        config['UPH'] = authoritative_uph
                    
                    return config
            
            # 🚀 增强：模糊匹配测试规范
            best_match = None
            match_score = 0
            
            for spec in test_specs:
                spec_device = (spec.get('DEVICE') or '').strip()
                spec_stage = (spec.get('STAGE') or '').strip()
                spec_approval = (spec.get('APPROVAL_STATE') or '').strip()
                
                if spec_approval != 'Released':
                    continue
                
                current_score = 0
                
                # 设备完全匹配 +100分
                if spec_device == device:
                    current_score += 100
                # 设备部分匹配 +50分
                elif device in spec_device or spec_device in device:
                    current_score += 50
                
                # 阶段完全匹配 +100分
                if self._is_stage_match(stage, spec_stage, preloaded_data):
                    current_score += 100
                # 阶段部分匹配 +30分
                elif stage.upper() in spec_stage.upper() or spec_stage.upper() in stage.upper():
                    current_score += 30
                
                # 更新最佳匹配
                if current_score > match_score and current_score >= 150:  # 至少需要150分
                    match_score = current_score
                    best_match = spec
            
            if best_match:
                logger.info(f"✅ 模糊匹配成功 - {device}-{stage}: 评分{match_score}")
                
                # 从测试规范获取基础配置
                tb_pn = (best_match.get('TB_PN') or '').strip() or 'NA'
                hb_pn = (best_match.get('HB_PN') or '').strip()
                handler = (best_match.get('HANDLER') or '').strip()
                
                config = {
                    'DEVICE': device,
                    'STAGE': stage,
                    'HB_PN': hb_pn,
                    'TB_PN': tb_pn,
                    'HANDLER_CONFIG': '',
                    'EQP_CLASS': handler,
                    'PKG_PN': (best_match.get('PKG_PN') or '').strip() or pkg_pn or f"PKG_{device}",
                    'TESTER': (best_match.get('TESTER') or '').strip(),
                    'UPH': best_match.get('UPH', 0),
                    'TEST_SPEC_SOURCE': 'ET_FT_TEST_SPEC_FUZZY',
                    'MATCH_SCORE': match_score
                }
                
                # 从recipe_file获取KIT_PN和HANDLER_CONFIG
                for recipe in recipe_files:
                    recipe_device = (recipe.get('DEVICE') or '').strip()
                    recipe_stage = (recipe.get('STAGE') or '').strip()
                    recipe_pkg = (recipe.get('PKG_PN') or '').strip()
                    
                    # 宽松匹配条件
                    if (recipe_device == device and 
                        (recipe_stage == stage or self._is_stage_match(stage, recipe_stage, preloaded_data)) and
                            (recipe.get('APPROVAL_STATE') or '').strip() == 'Released'):
                            
                            config['KIT_PN'] = (recipe.get('KIT_PN') or '').strip()
                            config['HANDLER_CONFIG'] = (recipe.get('HANDLER_CONFIG') or '').strip()
                            config['SOCKET_PN'] = (recipe.get('SOCKET_PN') or '').strip()
                            break
                    
                    # 获取权威UPH数据
                    uph_data = preloaded_data.get('uph_data', {})
                    uph_key = f"{device}|{stage}"
                    authoritative_uph = uph_data.get(uph_key, {}).get('UPH')
                    if authoritative_uph:
                        config['UPH'] = authoritative_uph
                    
                    return config
            
            # 🚀 兜底方案：生成默认配置需求，确保所有批次都能参与排产
            logger.warning(f"批次 {lot.get('LOT_ID')} 未找到匹配的测试规范，启用兜底配置生成")
            
            default_config = self._generate_fallback_configuration(device, stage, pkg_pn, preloaded_data)
            return default_config
            
        except Exception as e:
            logger.error(f"获取批次配置需求失败: {e}")
            return None

    def _generate_fallback_configuration(self, device: str, stage: str, pkg_pn: str, preloaded_data: Dict) -> Dict:
        """
        🚀 兜底配置生成器：确保所有批次都能获得基础配置需求
        """
        try:
            logger.info(f"📦 生成兜底配置 - {device}-{stage}")
            
            # 基于STAGE推断EQP_CLASS
            stage_upper = stage.upper()
            if 'HOT' in stage_upper or 'HIGH' in stage_upper:
                eqp_class = 'HOT_TESTER'
                handler_config = 'HIGH_TEMP_CONFIG'
            elif 'COLD' in stage_upper or 'LOW' in stage_upper:
                eqp_class = 'COLD_TESTER'  
                handler_config = 'LOW_TEMP_CONFIG'
            elif 'ROOM' in stage_upper or 'TTR' in stage_upper:
                eqp_class = 'ROOM_TESTER'
                handler_config = 'ROOM_TEMP_CONFIG'
            elif 'BAKING' in stage_upper:
                eqp_class = 'OVEN'
                handler_config = 'BAKING_OVEN'
            elif 'LSTR' in stage_upper:
                eqp_class = 'LSTR'
                handler_config = 'TAPE_REEL'
            elif 'BTT' in stage_upper or 'BURN' in stage_upper:
                eqp_class = 'BURN_IN'
                handler_config = 'BURN_IN_CONFIG'
            else:
                eqp_class = 'FT_TESTER'  # 默认FT测试机
                handler_config = 'STANDARD_FT'
            
            # 生成默认硬件配置
            kit_pn = f"KIT_{device}_{stage}"
            hb_pn = f"HB_{device}" if device else "HB_DEFAULT"
            tb_pn = f"TB_{device}" if device else "TB_DEFAULT"
            
            # 获取默认UPH
            uph_data = preloaded_data.get('uph_data', {})
            uph_key = f"{device}|{stage}"
            default_uph = uph_data.get(uph_key, {}).get('UPH', 1000)  # 默认UPH=1000
            
            config = {
                'DEVICE': device,
                'STAGE': stage,
                'PKG_PN': pkg_pn or f"PKG_{device}",
                'HB_PN': hb_pn,
                'TB_PN': tb_pn,
                'KIT_PN': kit_pn,
                'HANDLER_CONFIG': handler_config,
                'EQP_CLASS': eqp_class,
                'UPH': default_uph,
                'TEST_SPEC_SOURCE': 'FALLBACK_GENERATED',
                'FALLBACK_REASON': '未找到匹配测试规范，自动生成兜底配置'
            }
            
            logger.info(f"✅ 兜底配置生成成功 - {device}-{stage}: EQP_CLASS={eqp_class}, UPH={default_uph}")
            return config
            
        except Exception as e:
            logger.error(f"生成兜底配置失败: {e}")
            # 最终兜底配置
            return {
                'DEVICE': device,
                'STAGE': stage,
                'PKG_PN': pkg_pn or f"PKG_{device}",
                'HB_PN': f"HB_{device}",
                'TB_PN': f"TB_{device}",
                'KIT_PN': f"KIT_{device}",
                'HANDLER_CONFIG': 'STANDARD_CONFIG',
                'EQP_CLASS': 'FT_TESTER',
                'UPH': 1000,
                'TEST_SPEC_SOURCE': 'EMERGENCY_FALLBACK'
            }

    # 🗑️ 已移除冗余的兼容性包装器方法：
    # - get_lot_configuration_requirements() → 直接使用 get_lot_configuration_requirements_optimized()
    # - find_suitable_equipment() → 直接使用 find_suitable_equipment_optimized()
    # 这些方法造成性能损耗（重复数据预加载）且未被主流程使用

    def find_suitable_equipment_optimized(self, lot: Dict, lot_requirements: Dict, preloaded_data: Dict) -> List[Dict]:
        """🚀 增强版：为批次查找合适的设备 (支持特殊阶段规则)"""
        try:
            # 识别特殊阶段
            special_stage = self._is_special_stage_lot(lot)
            
            # 使用预加载数据
            equipment_status = preloaded_data.get('equipment_status', [])
            
            # 筛选可用设备
            available_statuses = ['IDLE', 'Run', 'Wait', '0', 'READY', 'ONLINE', 'SetupRun', '']
            available_equipment = [eqp for eqp in equipment_status 
                                 if eqp.get('STATUS', '').strip() in available_statuses]
            
            if not available_equipment:
                logger.warning("没有可用设备")
                return []
            
            # 🚀 特殊阶段处理：BTT和BAKING默认都可上机
            if special_stage in ['BTT', 'BAKING']:
                logger.info(f"✅ 特殊阶段处理: {special_stage} - 所有设备默认可用")
                equipment_scores = []
                
                for equipment in available_equipment:
                    # BTT和烘烤批次使用特殊优先级评分
                    priority_score = self._calculate_btt_baking_priority_score(lot, preloaded_data)
                    
                    # 简化评分：主要基于优先级和负载
                    load_score = self.calculate_load_balance_score(equipment, 2.0, 0)  # 假设2小时，无改机时间
                    
                    comprehensive_score = priority_score * 0.7 + load_score * 0.3
                    
                    equipment_scores.append({
                        'equipment': equipment,
                        'comprehensive_score': comprehensive_score,
                        'match_type': f'{special_stage}特殊阶段',
                        'match_score': 90,  # 固定高分
                        'processing_time': 2.0,
                        'changeover_time': 0,
                        'priority_score': priority_score,
                        'load_score': load_score
                    })
                
                # 🔧 确定性设备评分排序
                equipment_scores.sort(key=lambda x: (
                    -x.get('comprehensive_score', 0),  # 主要排序键
                    x.get('equipment', {}).get('HANDLER_ID', ''),  # 稳定键：设备ID
                    x.get('equipment', {}).get('EQP_TYPE', ''),    # 稳定键：设备类型
                ))
                return equipment_scores
            
            # 计算批次预计加工时间（使用优化的UPH获取方法）
            processing_time = self._calculate_processing_time(lot, lot_requirements, preloaded_data)
            
            equipment_scores = []
            
            for equipment in available_equipment:
                # 🚀 LSTR特殊处理：基于封装类型匹配
                if special_stage == 'LSTR':
                    lot_pkg_pn = (lot.get('PKG_PN') or '').strip()
                    eqp_pkg_pn = (equipment.get('PKG_PN') or '').strip()
                    
                    # LSTR封装兼容性检查
                    if not self._check_lstr_pkg_compatibility(lot_pkg_pn, eqp_pkg_pn):
                        logger.debug(f"LSTR封装不兼容: {lot_pkg_pn} vs {eqp_pkg_pn}")
                        continue
                    
                    # LSTR使用简化评分
                    match_score = 85 if lot_pkg_pn == eqp_pkg_pn else 70  # 精确匹配更高分
                    match_type = 'LSTR封装匹配'
                    changeover_time = 30  # LSTR改机时间较短
                    
                else:
                # 1. 技术匹配度评分 (使用优化后的SQL规则匹配)
                    match_score, match_type, changeover_time = self.calculate_equipment_match_score_optimized(
                    lot_requirements, equipment, preloaded_data)
                
                if match_score == 0:  # 不匹配的设备跳过
                    continue
                
                # 2. 负载均衡评分
                load_score = self.calculate_load_balance_score(equipment, processing_time, changeover_time)
                
                # 3. 交期紧迫度评分
                deadline_score = self.calculate_deadline_urgency_score(lot, processing_time)
                
                # 4. 产值效率评分
                value_score = self.calculate_value_efficiency_score(lot, processing_time)
                
                # 5. 业务优先级评分
                if special_stage == 'LSTR':
                    # LSTR使用特殊优先级评分
                    priority_score = self._calculate_btt_baking_priority_score(lot, preloaded_data)
                else:
                    priority_score = self.calculate_business_priority_score_optimized(lot, preloaded_data)
                
                # 6. 同产品续排加分
                continuation_bonus = 20.0 if self.check_same_product_continuation(lot_requirements, equipment) else 0.0
                
                # 综合评分计算
                weights = self.default_weights
                comprehensive_score = (
                    match_score * weights['tech_match_weight'] / 100.0 +
                    load_score * weights['load_balance_weight'] / 100.0 +
                    deadline_score * weights['deadline_weight'] / 100.0 +
                    value_score * weights['value_efficiency_weight'] / 100.0 +
                    priority_score * weights['business_priority_weight'] / 100.0 +
                    continuation_bonus
                )
                
                equipment_scores.append({
                    'equipment': equipment,
                    'comprehensive_score': comprehensive_score,
                    'match_score': match_score,
                    'match_type': match_type,
                    'changeover_time': changeover_time,
                    'load_score': load_score,
                    'deadline_score': deadline_score,
                    'value_score': value_score,
                    'priority_score': priority_score,
                    'continuation_bonus': continuation_bonus,
                    'processing_time': processing_time,
                    'selection_reason': f"{match_type}({match_score}分); 负载({load_score:.1f}分); 交期({deadline_score:.1f}分)"
                })
            
            # 🔧 确定性设备评分排序
            equipment_scores.sort(key=lambda x: (
                -x.get('comprehensive_score', 0),  # 主要排序键
                x.get('equipment', {}).get('HANDLER_ID', ''),  # 稳定键：设备ID
                x.get('equipment', {}).get('EQP_TYPE', ''),    # 稳定键：设备类型
            ))
            
            return equipment_scores
            
        except Exception as e:
            logger.error(f"查找合适设备失败: {e}")
            return []

    def calculate_business_priority_score_optimized(self, lot: Dict, preloaded_data: Dict) -> float:
        """🚀 优化版：计算业务优先级评分 (使用预加载数据)"""
        try:
            device = lot.get('DEVICE', '')
            lot_id = lot.get('LOT_ID', '')
            
            # 使用预加载的优先级配置数据
            device_priority_data = preloaded_data.get('device_priority', [])
            lot_priority_data = preloaded_data.get('lot_priority', [])
            
            # 1. 查询产品优先级配置
            device_priority = 50.0
            for config in device_priority_data:
                if config.get('DEVICE', '').strip() == device.strip():
                    device_priority = float(config.get('PRIORITY', 50) or 50)
                    break
            
            # 2. 查询批次优先级配置  
            lot_priority = 50.0
            for config in lot_priority_data:
                device_pattern = config.get('DEVICE', '').strip()
                if device_pattern and device_pattern in lot_id:
                    lot_priority = float(config.get('PRIORITY', 50) or 50)
                    break
            
            # 3. FIFO评分（基于工单号）
            fifo_score = self._get_fifo_score(lot_id)
            
            # 综合优先级评分
            priority_score = (device_priority * 0.4 + 
                            lot_priority * 0.4 + 
                            fifo_score * 0.2)
            
            return max(20.0, min(100.0, priority_score))
            
        except Exception as e:
            logger.error(f"计算业务优先级评分失败: {e}")
            return 50.0

    def _is_stage_match(self, lot_stage: str, spec_stage: str, preloaded_data: Dict = None) -> bool:
        """
        🔗 智能匹配STAGE字段（支持数据库配置的智能匹配）
        优先使用数据库配置的映射规则，如无配置则使用传统规则
        
        Args:
            lot_stage: 批次的STAGE (如 HOT-FT, COLD-FT, ROOM-TTR-FT)
            spec_stage: 测试规范的STAGE (如 Hot, Cold, ROOM-TTR)
            preloaded_data: 预加载数据，包含映射配置
            
        Returns:
            bool: 是否匹配
        """
        try:
            # 如果有预加载数据，使用智能匹配
            if preloaded_data:
                return self._smart_stage_match(lot_stage, spec_stage, preloaded_data)
            
            # 否则使用传统匹配逻辑
            return self._legacy_stage_match(lot_stage, spec_stage)
            
        except Exception as e:
            logger.error(f"STAGE匹配失败: {e}")
            return False
    
    def get_kit_configuration(self, device: str, stage: str, pkg_pn: str) -> Optional[Dict]:
        """
        获取KIT配置信息
        通过 DEVICE + STAGE + PKG_PN 查询 et_recipe_file
        
        Args:
            device: 设备类型
            stage: 阶段
            pkg_pn: 封装零件号
            
        Returns:
            Dict: KIT配置信息 {KIT_PN, SOCKET_PN} 或 None
        """
        try:
            # 查询配方文件（使用缓存优化）
            recipe_data_dict, recipe_source = self.data_manager.get_recipe_file_data()
            if not recipe_data_dict:
                logger.warning("无法获取配方文件数据，跳过KIT配置查询")
                return None
                
            # 转换为列表格式进行查找
            recipe_data = list(recipe_data_dict.values())
            
            # 查找匹配的KIT配置
            for recipe in recipe_data:
                if (recipe.get('DEVICE', '').strip() == device and 
                    recipe.get('STAGE', '').strip() == stage and
                    recipe.get('PKG_PN', '').strip() == pkg_pn and
                    recipe.get('APPROVAL_STATE', '').strip() == 'Released'):
                    
                    return {
                        'KIT_PN': recipe.get('KIT_PN', '').strip(),
                        'SOCKET_PN': recipe.get('SOCKET_PN', '').strip(),
                        'HANDLER_CONFIG_RECIPE': recipe.get('HANDLER_CONFIG', '').strip()
                    }
            
            logger.debug(f"未找到KIT配置 (DEVICE={device}, STAGE={stage}, PKG_PN={pkg_pn})")
            return None
            
        except Exception as e:
            logger.error(f"获取KIT配置失败: {e}")
            return None
    


    def _calculate_hardware_compatibility(self, req_kit: str, req_hb: str, req_tb: str, 
                                        eqp_kit: str, eqp_hb: str, eqp_tb: str) -> float:
        """计算硬件兼容性评分"""
        try:
            score = 0.0
            total_weight = 0.0
            
            # KIT匹配权重最高（50%）
            kit_weight = 50.0
            if req_kit and eqp_kit:
                if req_kit == eqp_kit:
                    score += kit_weight
                elif self._is_kit_family_compatible(req_kit, eqp_kit):
                    score += kit_weight * 0.8  # 同系列KIT 80%兼容
                total_weight += kit_weight
            
            # HB匹配权重（30%）
            hb_weight = 30.0
            if req_hb and eqp_hb:
                if req_hb == eqp_hb:
                    score += hb_weight
                elif self._is_hb_compatible(req_hb, eqp_hb):
                    score += hb_weight * 0.6  # 兼容HB 60%分数
                total_weight += hb_weight
            
            # TB匹配权重（20%）
            tb_weight = 20.0
            if req_tb and eqp_tb:
                if req_tb == eqp_tb:
                    score += tb_weight
                elif self._is_tb_compatible(req_tb, eqp_tb):
                    score += tb_weight * 0.7  # 兼容TB 70%分数
                total_weight += tb_weight
            
            return (score / total_weight * 100) if total_weight > 0 else 50.0
            
        except Exception as e:
            logger.error(f"计算硬件兼容性失败: {e}")
            return 50.0

    def _calculate_config_compatibility(self, req_config: str, eqp_config: str, stage: str) -> float:
        """计算配置兼容性评分"""
        try:
            if not req_config or not eqp_config:
                return 70.0  # 无配置信息时给予中等分数
            
            if req_config == eqp_config:
                return 100.0  # 完全匹配
            
            # 基于STAGE的特殊兼容性规则
            if 'BAKING' in stage.upper():
                return 95.0  # 烘箱通常兼容性很高
            elif 'LSTR' in stage.upper():
                return 85.0  # 编带机配置相对灵活
            elif 'FT' in stage.upper():
                # 终测配置相对严格
                if self._is_ft_config_compatible(req_config, eqp_config):
                    return 75.0
                else:
                    return 40.0
            
            return 60.0  # 默认中等兼容性
            
        except Exception as e:
            logger.error(f"计算配置兼容性失败: {e}")
            return 60.0

    def _get_equipment_performance_score(self, handler_id: str) -> float:
        """获取设备历史性能评分"""
        try:
            # 这里可以集成设备历史数据分析
            # 暂时返回基于设备ID的简单评分
            if not handler_id:
                return 0.0
            
            # 基于设备编号的性能假设（实际应该从历史数据计算）
            performance_factors = {
                'reliability': 0.4,  # 可靠性权重
                'efficiency': 0.3,   # 效率权重
                'quality': 0.3       # 质量权重
            }
            
            # 简化的性能评分逻辑（实际应该查询历史数据）
            base_score = hash(handler_id) % 10  # 0-9的基础分
            normalized_score = (base_score / 9.0) * 10.0  # 标准化到0-10分
            
            return min(10.0, max(-5.0, normalized_score - 5.0))  # -5到+5的调整范围
            
        except Exception as e:
            logger.error(f"获取设备性能评分失败: {e}")
            return 0.0

    def _get_current_load_factor(self, handler_id: str) -> float:
        """获取当前负载因子"""
        try:
            current_load = self.equipment_workload.get(handler_id, 0.0)
            
            # 负载因子计算：轻负载加分，重负载减分
            if current_load < 8.0:  # 轻负载
                return -2.0  # 加分
            elif current_load < 16.0:  # 中等负载
                return 0.0
            elif current_load < 24.0:  # 重负载
                return 3.0  # 减分
            else:  # 超负载
                return 8.0  # 大幅减分
                
        except Exception as e:
            logger.error(f"获取负载因子失败: {e}")
            return 0.0

    def _calculate_intelligent_changeover_time(self, hardware_score: float, config_score: float, status: str, 
                                             match_type: str = '', req_kit: str = '', eqp_kit: str = '', 
                                             req_hb: str = '', eqp_hb: str = '', req_tb: str = '', eqp_tb: str = '') -> int:
        """
        🔧 修复：基于真实业务规则的改机时间计算
        
        用户需求修复：
        4. 改机时间是如何锚定的？
        
        真实业务规则：
        1. 同设置匹配：0分钟（无需改机）
        2. 小改机匹配：45分钟（更换HB/TB）
        3. 大改机匹配：120分钟（更换KIT+HB+TB）
        4. 特殊阶段：根据设备类型确定
        
        Args:
            hardware_score: 硬件兼容性评分 (0-100)
            config_score: 配置兼容性评分 (0-100)
            status: 设备状态 (Run, IDLE, Wait, DOWN)
            match_type: 匹配类型 (同设置匹配/小改机匹配/大改机匹配)
            req_kit, eqp_kit: 需求和设备的KIT信息
            req_hb, eqp_hb: 需求和设备的HB信息
            req_tb, eqp_tb: 需求和设备的TB信息
            
        Returns:
            int: 改机时间（分钟）
        """
        try:
            # 🔧 基于匹配类型的标准改机时间
            base_time = 0
            reason = ""
            
            if '同设置' in match_type:
                base_time = 0  # 无需改机
                reason = "KIT+HB+TB完全匹配，无需改机"
            elif '小改机' in match_type:
                base_time = 45  # 标准小改机时间
                reason = "EQP_CLASS匹配，需更换HB/TB"
            elif '大改机' in match_type:
                base_time = 120  # 标准大改机时间
                reason = "HANDLER_CONFIG匹配，需更换KIT+HB+TB"
            else:
                # 🚨 不兼容的情况
                base_time = 9999  # 表示无法改机
                reason = "设备不兼容，无法改机"
            
            # 🔧 基于实际硬件差异的精细调整
            if base_time > 0 and base_time < 9999:
                # 检查具体需要更换的组件
                kit_change = req_kit != eqp_kit if req_kit and eqp_kit else False
                hb_change = req_hb != eqp_hb if req_hb and eqp_hb else False
                tb_change = req_tb != eqp_tb if req_tb and eqp_tb else False
                
                # 根据实际更换组件调整时间
                component_time = 0
                if kit_change:
                    component_time += 60  # KIT更换需要1小时
                if hb_change:
                    component_time += 30  # HB更换需要30分钟
                if tb_change:
                    component_time += 15  # TB更换需要15分钟
                
                # 使用更精确的时间
                if component_time > 0:
                    base_time = component_time
                    reason += f" (KIT:{kit_change}, HB:{hb_change}, TB:{tb_change})"
            
            # 🔧 根据设备状态调整
            status_upper = status.upper()
            if status_upper == 'IDLE':  # 待机
                status_factor = 1.0
            elif status_upper == 'WAIT':  # 等待
                status_factor = 1.2
            elif status_upper == 'RUN':  # 运行中
                status_factor = 1.5  # 需要中断当前作业
            elif status_upper == 'DOWN':  # 故障
                base_time = 9999  # 无法使用
                reason = f"设备状态异常({status})，无法使用"
                status_factor = 1.0
            else:  # 其他状态
                status_factor = 1.3
            
            if base_time == 9999:
                final_time = 9999
            else:
                final_time = int(base_time * status_factor)
                reason += f", 状态调整系数:{status_factor}"
            
            # 限制在合理范围内
            if final_time != 9999:
                final_time = max(0, min(final_time, 300))  # 0分钟到5小时
            
            logger.debug(f"🔧 改机时间计算: {match_type}, {reason}, 最终时间={final_time}分钟")
            
            return final_time
            
        except Exception as e:
            logger.error(f"计算智能改机时间失败: {e}")
            return 60

    def _is_kit_family_compatible(self, req_kit: str, eqp_kit: str) -> bool:
        """判断KIT是否属于同一系列"""
        try:
            if not req_kit or not eqp_kit:
                return False
            
            # 提取KIT系列标识（假设前缀相同表示同系列）
            req_prefix = req_kit.split('-')[0] if '-' in req_kit else req_kit[:3]
            eqp_prefix = eqp_kit.split('-')[0] if '-' in eqp_kit else eqp_kit[:3]
            
            return req_prefix == eqp_prefix
            
        except Exception as e:
            logger.error(f"判断KIT系列兼容性失败: {e}")
            return False

    def _is_hb_compatible(self, req_hb: str, eqp_hb: str) -> bool:
        """判断HB是否兼容"""
        try:
            if not req_hb or not eqp_hb:
                return False
            
            # HB兼容性规则（可根据实际情况调整）
            # 例如：某些HB可以向下兼容
            return req_hb.startswith(eqp_hb[:2]) or eqp_hb.startswith(req_hb[:2])
            
        except Exception as e:
            logger.error(f"判断HB兼容性失败: {e}")
            return False

    def _is_tb_compatible(self, req_tb: str, eqp_tb: str) -> bool:
        """判断TB是否兼容"""
        try:
            if not req_tb or not eqp_tb:
                return False
            
            # TB兼容性规则（可根据实际情况调整）
            return req_tb.startswith(eqp_tb[:2]) or eqp_tb.startswith(req_tb[:2])
            
        except Exception as e:
            logger.error(f"判断TB兼容性失败: {e}")
            return False

    def _is_ft_config_compatible(self, req_config: str, eqp_config: str) -> bool:
        """判断终测配置是否兼容"""
        try:
            if not req_config or not eqp_config:
                return False
            
            # 终测配置兼容性规则（通常比较严格）
            # 可以根据实际的配置命名规则调整
            return req_config.split('_')[0] == eqp_config.split('_')[0]
            
        except Exception as e:
            logger.error(f"判断终测配置兼容性失败: {e}")
            return False
    
    def calculate_load_balance_score(self, equipment: Dict, processing_time: float, changeover_time: int) -> float:
        """
        智能负载均衡评分 (基于真实案例优化)
        
        参考：半导体制造中的动态负载均衡算法
        考虑设备利用率、队列长度、历史性能等多个因素
        """
        try:
            handler_id = equipment.get('HANDLER_ID', '')
            
            # 获取当前设备负载
            current_load = self.equipment_workload.get(handler_id, 0.0)
            
            # 计算新增负载（改机时间转为小时）
            changeover_hours = changeover_time / 60.0
            total_new_load = current_load + changeover_hours + processing_time
            
            # 动态负载阈值计算（基于设备类型和能力）
            equipment_capacity = self._get_equipment_capacity(equipment)
            max_load = equipment_capacity.get('max_daily_hours', 24.0)
            optimal_load = equipment_capacity.get('optimal_load_ratio', 0.75) * max_load
            
            # 多层次负载评分
            load_ratio = total_new_load / max_load
            
            if load_ratio <= 0.3:  # 轻负载区间
                base_score = 100.0
                # 考虑设备空置成本
                idle_penalty = min(10.0, (0.3 - load_ratio) * 20)
                return max(90.0, base_score - idle_penalty)
                
            elif load_ratio <= 0.6:  # 理想负载区间
                return 100.0  # 最优区间
                
            elif load_ratio <= 0.8:  # 高负载区间
                return 90.0 - (load_ratio - 0.6) * 50  # 线性递减
                
            elif load_ratio <= 1.0:  # 满负载区间
                return 70.0 - (load_ratio - 0.8) * 100  # 快速递减
                
            else:  # 超负载区间
                overload_penalty = (load_ratio - 1.0) * 200
                return max(0.0, 50.0 - overload_penalty)
                
        except Exception as e:
            logger.error(f"计算智能负载均衡评分失败: {e}")
            return 50.0

    def _get_equipment_capacity(self, equipment: Dict) -> Dict:
        """获取设备产能信息"""
        try:
            eqp_type = equipment.get('EQP_TYPE', '').upper()
            
            # 基于设备类型的产能配置
            capacity_config = {
                'TESTER': {
                    'max_daily_hours': 22.0,  # 测试机可以长时间运行
                    'optimal_load_ratio': 0.85,
                    'maintenance_hours': 2.0
                },
                'HANDLER': {
                    'max_daily_hours': 20.0,  # 分选机需要更多维护时间
                    'optimal_load_ratio': 0.75,
                    'maintenance_hours': 4.0
                },
                'BAKING': {
                    'max_daily_hours': 24.0,  # 烘箱可以连续运行
                    'optimal_load_ratio': 0.90,
                    'maintenance_hours': 0.5
                },
                'LSTR': {
                    'max_daily_hours': 16.0,  # 编带机人工操作较多
                    'optimal_load_ratio': 0.70,
                    'maintenance_hours': 2.0
                }
            }
            
            return capacity_config.get(eqp_type, {
                'max_daily_hours': 20.0,
                'optimal_load_ratio': 0.75,
                'maintenance_hours': 2.0
            })
            
        except Exception as e:
            logger.error(f"获取设备产能信息失败: {e}")
            return {
                'max_daily_hours': 20.0,
                'optimal_load_ratio': 0.75,
                'maintenance_hours': 2.0
            }
    
    def calculate_deadline_urgency_score(self, lot: Dict, processing_time: float) -> float:
        """
        智能交期紧迫度评分 (基于真实案例优化)
        
        参考：车规芯片制造中的多维度交期管理
        综合考虑客户优先级、合同交期、库存风险等因素
        """
        try:
            # 1. 客户优先级检查 (最高优先级)
            priority_level = lot.get('PRIORITY', '').strip().lower()
            customer_type = lot.get('CUSTOMER_TYPE', '').strip().upper()  # VIP, NORMAL, etc.
            
            # VIP客户或关键项目特殊处理
            if customer_type == 'VIP' or priority_level == '0':
                return 180.0  # 最高优先级
            elif priority_level == '1':
                return 150.0  # 高优先级
            elif priority_level == '2':
                return 120.0  # 中等优先级
            
            # 2. 合同交期分析
            delivery_date = lot.get('DELIVERY_DATE') or lot.get('REQ_DATE') or lot.get('DUE_DATE')
            
            if delivery_date:
                urgency_score = self._calculate_delivery_urgency(delivery_date, processing_time)
                
                # 3. 库存风险评估
                inventory_risk = self._calculate_inventory_risk(lot)
                
                # 4. 生产连续性考虑
                continuity_bonus = self._calculate_production_continuity_bonus(lot)
                
                # 综合评分
                final_score = urgency_score + inventory_risk + continuity_bonus
                return min(200.0, max(20.0, final_score))
            
            # 5. 无交期信息时的智能FIFO
            fifo_score = self._calculate_intelligent_fifo_score(lot)
            
            # 6. 批次类型优先级
            lot_type_bonus = self._get_lot_type_priority_bonus(lot.get('LOT_TYPE', ''))
            
            return min(100.0, max(30.0, fifo_score + lot_type_bonus))
                
        except Exception as e:
            logger.error(f"计算智能交期紧迫度评分失败: {e}")
            return 50.0

    def _calculate_delivery_urgency(self, delivery_date, processing_time: float) -> float:
        """计算交期紧迫度"""
        try:
            # 解析交期
            if isinstance(delivery_date, str):
                try:
                    delivery_dt = datetime.strptime(delivery_date, '%Y-%m-%d')
                except ValueError:
                    try:
                        delivery_dt = datetime.strptime(delivery_date, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        return 50.0  # 解析失败，返回中等评分
            else:
                delivery_dt = delivery_date
            
            # 计算剩余时间
            current_time = datetime.now()
            completion_time = current_time + timedelta(hours=processing_time)
            remaining_hours = (delivery_dt - completion_time).total_seconds() / 3600
                
            # 智能紧迫度评分
            if remaining_hours < -24:  # 超期超过1天
                return 200.0  # 最高紧急
            elif remaining_hours < 0:  # 已超期但不超过1天
                return 180.0
            elif remaining_hours < 4:  # 4小时内
                return 160.0
            elif remaining_hours < 8:  # 8小时内
                return 140.0
            elif remaining_hours < 24:  # 24小时内
                return 120.0  
            elif remaining_hours < 48:  # 48小时内
                return 100.0
            elif remaining_hours < 72:  # 72小时内
                return 80.0
            elif remaining_hours < 168:  # 1周内
                return 60.0
            else:  # 1周以上
                return 40.0
                
        except Exception as e:
            logger.error(f"计算交期紧迫度失败: {e}")
            return 50.0

    def _calculate_inventory_risk(self, lot: Dict) -> float:
        """计算库存风险评分"""
        try:
            # 获取库存相关信息
            good_qty = float(lot.get('GOOD_QTY', 0) or 0)
            device = lot.get('DEVICE', '')
            
            # 基于数量的风险评估
            qty_risk = 0.0
            if good_qty > 10000:  # 大批量
                qty_risk = 15.0
            elif good_qty > 5000:  # 中批量
                qty_risk = 10.0
            elif good_qty > 1000:  # 小批量
                qty_risk = 5.0
            
            # 基于产品类型的风险评估（车规芯片通常风险较高）
            device_risk = 0.0
            if 'AUTO' in device.upper() or 'CAR' in device.upper():
                device_risk = 10.0  # 车规产品风险较高
            elif 'CONSUMER' in device.upper():
                device_risk = 5.0   # 消费级产品风险中等
            
            return min(20.0, qty_risk + device_risk)
            
        except Exception as e:
            logger.error(f"计算库存风险失败: {e}")
            return 0.0

    def _calculate_production_continuity_bonus(self, lot: Dict) -> float:
        """计算生产连续性奖励"""
        try:
            device = lot.get('DEVICE', '')
            stage = lot.get('STAGE', '')
            
            # 检查是否有同类产品正在生产（提高连续性）
            # 这里简化处理，实际应该查询当前生产状态
            continuity_key = f"{device}_{stage}"
            
            # 基于工艺流程的连续性奖励
            if 'FT' in stage.upper():  # 终测工艺连续性重要
                return 8.0
            elif 'BAKING' in stage.upper():  # 烘箱工艺相对独立
                return 3.0
            elif 'LSTR' in stage.upper():  # 编带工艺
                return 5.0
            
            return 0.0
            
        except Exception as e:
            logger.error(f"计算生产连续性奖励失败: {e}")
            return 0.0

    def _calculate_intelligent_fifo_score(self, lot: Dict) -> float:
        """智能FIFO评分"""
        try:
            lot_id = lot.get('LOT_ID', '')
            create_time = lot.get('CREATE_TIME') or lot.get('START_TIME')
            
            # 优先使用创建时间
            if create_time:
                if isinstance(create_time, str):
                    try:
                        create_dt = datetime.strptime(create_time, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        try:
                            create_dt = datetime.strptime(create_time, '%Y-%m-%d')
                        except ValueError:
                            create_dt = None
                else:
                    create_dt = create_time
                
                if create_dt:
                    # 基于等待时间的FIFO评分
                    waiting_hours = (datetime.now() - create_dt).total_seconds() / 3600
                    if waiting_hours > 72:  # 等待超过3天
                        return 80.0
                    elif waiting_hours > 48:  # 等待超过2天
                        return 70.0
                    elif waiting_hours > 24:  # 等待超过1天
                        return 60.0
                    else:
                        return 50.0
            
            # 基于LOT_ID的FIFO（备用方案）
            if lot_id:
                import re
                numbers = re.findall(r'\d+', lot_id)
                if numbers:
                    lot_number = int(numbers[-1])
                    # 较小的编号获得较高的优先级
                    base_score = 55.0
                    fifo_factor = min(10.0, (lot_number % 1000) / 100.0)
                    return max(45.0, base_score - fifo_factor)
            
            return 50.0  # 默认FIFO评分
                
        except Exception as e:
            logger.error(f"计算智能FIFO评分失败: {e}")
            return 50.0

    def _get_lot_type_priority_bonus(self, lot_type: str) -> float:
        """获取批次类型优先级奖励"""
        try:
            lot_type = lot_type.strip() if lot_type else ''
            
            # 基于批次类型的优先级设置
            if lot_type == '工程批':
                return 15.0  # 工程批优先级较高
            elif lot_type == '量产批':
                return 10.0  # 量产批正常优先级
            elif 'PILOT' in lot_type.upper():
                return 12.0  # 试产批
            elif 'QUAL' in lot_type.upper():
                return 18.0  # 认证批优先级最高
            
            return 5.0  # 默认奖励
            
        except Exception as e:
            logger.error(f"获取批次类型优先级奖励失败: {e}")
            return 5.0
    
    def calculate_value_efficiency_score(self, lot: Dict, processing_time: float) -> float:
        """计算产值效率评分"""
        try:
            # 获取批次产值信息
            good_qty = float(lot.get('GOOD_QTY', 0) or 0)
            price = float(lot.get('PRICE', 0) or lot.get('UNIT_PRICE', 0) or 0)
            
            if good_qty <= 0 or processing_time <= 0:
                return 50.0  # 无效数据，默认中等评分
            
            # 计算每小时产值
            total_value = good_qty * price
            value_per_hour = total_value / processing_time if processing_time > 0 else 0
            
            # 基准产值率（可配置）
            base_value_rate = 10000.0  # 每小时10000元为基准
            
            # 产值效率评分
            efficiency_ratio = value_per_hour / base_value_rate
            score = min(100.0, efficiency_ratio * 100)
            
            return max(20.0, score)  # 最低20分
            
        except Exception as e:
            logger.error(f"计算产值效率评分失败: {e}")
            return 50.0
    
    def calculate_business_priority_score(self, lot: Dict) -> float:
        """计算业务优先级评分"""
        try:
            device = lot.get('DEVICE', '')
            lot_id = lot.get('LOT_ID', '')
            
            # 1. 查询产品优先级配置
            device_priority = self._get_device_priority(device)
            
            # 2. 查询批次优先级配置  
            lot_priority = self._get_lot_priority(lot_id)
            
            # 3. FIFO评分（基于工单号）
            fifo_score = self._get_fifo_score(lot_id)
            
            # 综合优先级评分
            priority_score = (device_priority * 0.4 + 
                            lot_priority * 0.4 + 
                            fifo_score * 0.2)
            
            return max(20.0, min(100.0, priority_score))
            
        except Exception as e:
            logger.error(f"计算业务优先级评分失败: {e}")
            return 50.0
    
    def _get_device_priority(self, device: str) -> float:
        """获取产品优先级评分 - 使用缓存优化"""
        try:
            # 确保缓存已加载
            self._load_priority_configs()
            
            # 从缓存中查找
            for config in self._device_priority_cache:
                if config.get('DEVICE', '').strip() == device.strip():
                    priority = float(config.get('PRIORITY', 50) or 50)
                    return min(100.0, max(0.0, priority))
            return 50.0
        except Exception:
            return 50.0
    
    def _get_lot_priority(self, lot_id: str) -> float:
        """获取批次优先级评分 - 使用缓存优化"""
        try:
            # 确保缓存已加载
            self._load_priority_configs()
            
            # 从缓存中查找
            for config in self._lot_priority_cache:
                # 这里可以根据批次ID模式匹配
                device_pattern = config.get('DEVICE', '').strip()
                if device_pattern and device_pattern in lot_id:
                    priority = float(config.get('PRIORITY', 50) or 50)
                    return min(100.0, max(0.0, priority))
            return 50.0
        except Exception:
            return 50.0
    
    def _get_fifo_score(self, lot_id: str) -> float:
        """获取FIFO评分（基于工单号）"""
        try:
            # 提取工单号中的数字部分
            import re
            numbers = re.findall(r'\d+', lot_id)
            if numbers:
                # 数字越小，优先级越高
                lot_number = int(numbers[-1])  # 取最后一个数字
                # 简化计算：假设工单号在1-9999范围内
                fifo_score = max(20.0, 100.0 - (lot_number / 100.0))
                return min(100.0, fifo_score)
            return 50.0
        except Exception:
            return 50.0
    
    def check_same_product_continuation(self, lot_requirements: Dict, equipment: Dict) -> bool:
        """检查是否可以同产品续排"""
        try:
            handler_id = equipment.get('HANDLER_ID', '')
            req_device = lot_requirements.get('DEVICE', '')
            eqp_device = equipment.get('DEVICE', '')
            
            # 检查设备是否正在处理相同的产品
            if eqp_device and req_device:
                eqp_base = eqp_device.split('_')[0] if '_' in eqp_device else eqp_device
                req_base = req_device.split('_')[0] if '_' in req_device else req_device
                return eqp_base == req_base
                
            return False
            
        except Exception as e:
            logger.error(f"检查同产品续排失败: {e}")
            return False
    
    def _extract_kit_requirements(self, lot_requirements: Dict) -> Dict:
        """
        从批次需求中提取KIT要求和对应的HANDLER_CONFIG
        基于KIT编码规则分析设备类型需求
        """
        try:
            kit_pn = lot_requirements.get('KIT_PN', '').strip()
            required_handler_config = lot_requirements.get('HANDLER_CONFIG', '').strip()
            stage = lot_requirements.get('STAGE', '').strip()
            
            # 如果直接有HANDLER_CONFIG，优先使用
            if required_handler_config and required_handler_config != 'PnP':
                return {
                    'required_handler_config': required_handler_config,
                    'kit_based_config': None,
                    'equipment_type_hint': self._get_equipment_type_from_config(required_handler_config)
                }
            
            # 基于KIT编码分析设备要求
            kit_config = None
            if kit_pn:
                # 分析KIT后缀确定HANDLER_CONFIG
                if kit_pn.endswith('-TS'):
                    kit_config = 'C6800T_S'
                elif kit_pn.endswith('-HB'):
                    kit_config = 'C6800H_B'
                elif kit_pn.endswith('-TG'):
                    kit_config = 'C6800T_G'
                elif kit_pn.endswith('-TB'):
                    kit_config = 'C6800T_B'
                elif kit_pn.endswith('-T'):
                    kit_config = 'C6800T'
                elif 'CKC-' in kit_pn and not any(kit_pn.endswith(suffix) for suffix in ['-TS', '-HB', '-TG', '-TB', '-T']):
                    kit_config = 'C6800H'  # 默认处理机
            
            # 基于STAGE分析设备类型需求
            stage_upper = stage.upper()
            equipment_type_hint = None
            
            if any(test_stage in stage_upper for test_stage in ['HOT-FT', 'COLD-FT', 'ROOM-TTR-FT', 'ROOM-TEST-FT']):
                equipment_type_hint = '平移式分选机'  # 测试类阶段
            elif 'TRIM-FT' in stage_upper:
                equipment_type_hint = '烧录机'  # 编程类阶段
            elif 'BAKING' in stage_upper:
                equipment_type_hint = '烘箱'  # 烘烤阶段
            elif 'LSTR' in stage_upper:
                equipment_type_hint = '纯编带机'  # 纯编带阶段
            
            return {
                'required_handler_config': required_handler_config if required_handler_config != 'PnP' else None,
                'kit_based_config': kit_config,
                'equipment_type_hint': equipment_type_hint
            }
            
        except Exception as e:
            logger.error(f"提取KIT要求失败: {e}")
            return {
                'required_handler_config': None,
                'kit_based_config': None,
                'equipment_type_hint': None
            }
    
    def _get_equipment_type_from_config(self, handler_config: str) -> str:
        """根据HANDLER_CONFIG推断设备类型"""
        if 'IPS' in handler_config:
            return '烧录机'
        elif 'C6800' in handler_config:
            return '平移式分选机'
        elif 'C9D' in handler_config:
            return '重力式分选机'
        elif any(config in handler_config for config in ['SKD', 'F1850', 'H1618']):
            return '转盘式分选机'
        else:
            return '未知'

    def _is_equipment_type_compatible(self, eqp_type: str, eqp_config: str, req_config: str, req_stage: str) -> bool:
        """
        检查设备类型是否与批次需求兼容
        
        Args:
            eqp_type: 设备类型 (如 '烧录机', '测试机')
            eqp_config: 设备配置 (如 'IPS5800S', 'STS8200')  
            req_config: 需求配置 (如 'PnP', 'STS8200')
            req_stage: 需求阶段 (如 'HOT-FT', 'TRIM-FT', 'BAKING2')
            
        Returns:
            bool: 是否兼容
        """
        try:
            # 1. 基础类型匹配检查
            stage_upper = req_stage.upper()
            
            # 测试类型的阶段需要测试设备 (注意：TRIM-FT是编程阶段，不是测试)
            test_stages = ['HOT-FT', 'COLD-FT', 'ROOM-TTR-FT', 'ROOM-TEST-FT', 'HOT', 'COLD', 'ROOM-TTR', 'ROOM-TEST']
            if any(stage in stage_upper for stage in test_stages):
                # 需要测试机 - 烧录机绝对不能做测试
                if eqp_type == '烧录机' or 'HANK' in eqp_config or 'IPS' in eqp_config:
                    return False  # 烧录机/编程机不能做测试
                # 测试需要测试机配置
                if req_config == 'PnP':
                    # PnP测试需要测试设备，不能用烧录设备
                    if 'IPS' in eqp_config or eqp_type == '烧录机':
                        return False
                # 检查测试机配置
                if 'STS' in req_config and 'STS' not in eqp_config:
                    return False
            
            # 烧录/编程类型的阶段需要烧录设备  
            program_stages = ['TRIM-FT', 'TRIM', 'BAKING2', 'BAKING', 'LSTR', 'PROGRAM', 'BURN', 'WRITE']
            if any(stage in stage_upper for stage in program_stages):
                # 需要烧录机 - 测试机绝对不能做烧录
                if eqp_type == '测试机' or eqp_type == '平移式分选机' or 'HCHC' in eqp_config or 'STS' in eqp_config:
                    return False  # 测试机/分选机不能做烧录
                # 烧录需要烧录机配置  
                if req_config == 'PnP':
                    # PnP烧录需要烧录设备，不能用测试设备
                    if 'STS' in eqp_config or eqp_type == '测试机':
                        return False
                # 检查烧录机配置
                if 'IPS' in req_config and 'IPS' not in eqp_config:
                    return False
            
            # 2. 配置兼容性检查
            if req_config and eqp_config:
                # PnP配置检查
                if req_config == 'PnP':
                    # PnP要求自动化程度高的设备
                    if 'IPS' in eqp_config or 'STS' in eqp_config:
                        return True
                    return False
                
                # 精确配置匹配
                if req_config == eqp_config:
                    return True
                
                # 兼容配置检查 (如IPS5800S兼容IPS5800)
                if req_config in eqp_config or eqp_config in req_config:
                    return True
                    
                # 同系列设备兼容 (如STS8200和STS8250)
                req_base = req_config[:6] if len(req_config) >= 6 else req_config
                eqp_base = eqp_config[:6] if len(eqp_config) >= 6 else eqp_config
                if req_base == eqp_base:
                    return True
            
            # 3. 无配置要求时，认为兼容
            if not req_config:
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"设备类型兼容性检查失败: {e}")
            # 出错时保守返回False，避免错误分配
            return False
    

    
    def _generate_estimated_due_date(self, lot: Dict, processing_time: float) -> str:
        """
        为没有交期的批次生成预计完成时间作为DUE_DATE
        """
        try:
            current_time = datetime.now()
            estimated_completion = current_time + timedelta(hours=processing_time)
            return estimated_completion.strftime('%Y-%m-%d %H:%M:%S')
        except Exception:
            # 默认24小时后完成
            estimated_completion = datetime.now() + timedelta(hours=24)
            return estimated_completion.strftime('%Y-%m-%d %H:%M:%S')
    
    def execute_real_scheduling(self, algorithm: str = 'ortools', user_id: str = None, optimization_target: str = 'balanced') -> List[Dict]:
        """
        执行基于Google OR-Tools的智能排产算法
        
        算法特点：
        1. 多维度评分：技术匹配、负载均衡、交期紧迫、产值效率、业务优先级
        2. 智能权重：根据业务策略动态调整各维度权重
        3. 🎯 优化目标：根据用户选择的优化目标调整权重配置
        4. 约束优化：设备能力、改机时间、产能限制等复杂约束
        5. 全局最优：基于约束规划的全局最优解搜索
        
        Args:
            algorithm: 算法类型 ('ortools', 'intelligent', 'deadline', 'product', 'value')
            user_id: 用户ID，用于获取个性化权重配置
            optimization_target: 优化目标 ('balanced', 'makespan', 'efficiency')
            
        Returns:
            List[Dict]: 排产结果列表，包含完整的批次和设备分配信息
        """
        try:
            start_time = time.time()
            logger.info(f"🚀 开始执行OR-Tools智能排产算法 ({algorithm})...")
            
            # 🚀 Task 1.1 + P0优化目标集成: 动态获取策略权重配置并应用优化目标调整
            dynamic_weights = self._get_strategy_weights(algorithm, user_id, optimization_target)
            if dynamic_weights:
                # 临时覆盖default_weights，确保后续算法使用动态权重
                original_weights = self.default_weights.copy()
                self.default_weights.update(dynamic_weights)
                logger.info(f"✅ 策略权重动态加载成功 - 策略: {algorithm}, 用户: {user_id or 'system'}, 优化目标: {optimization_target}")
                logger.debug(f"🔧 当前权重配置: {self.default_weights}")
            else:
                logger.warning(f"⚠️ 策略权重获取失败，使用默认配置 - 策略: {algorithm}, 优化目标: {optimization_target}")
            
            # 0. 预加载优先级配置缓存
            self._load_priority_configs()
            
            # 1. 获取待排产批次和设备数据
            wait_lots, wait_source = self.data_manager.get_wait_lot_data()
            logger.info(f"📋 从{wait_source}获取到 {len(wait_lots)} 个待排产批次")
            
            if not wait_lots:
                logger.warning("⚠️ 没有待排产批次")
                return []
            
            # 获取设备数据 - 移除限制获取完整数据
            equipment_result = self.data_manager.get_table_data('EQP_STATUS')
            if not equipment_result.get('success'):
                logger.error("无法获取设备状态数据")
                return []
            
            all_equipment = equipment_result.get('data', [])
            
            # 根据EQP_STATUS表字段说明：STATUS是TEXT类型，值为Run,IDLE,Wait,DOWN
            # 所有设备可用，不分STATUS状态
            available_statuses = ['Run', 'IDLE', 'Wait', 'DOWN', 'Pause', 'SetupRun', '']
            available_equipment = []
            
            for eqp in all_equipment:
                status = eqp.get('STATUS', '').strip()
                
                # 根据实际数据库字段说明处理状态
                if status in available_statuses or status.upper() in [s.upper() for s in available_statuses]:
                    available_equipment.append(eqp)
            
            if not available_equipment:
                logger.warning("没有可用设备")
                return []
            
            # 🎯 统一算法架构：所有前端策略都使用增强启发式算法，仅权重配置不同
            logger.info(f"🎯 执行增强启发式算法（{algorithm.upper()}策略 - 权重已动态配置）")
            logger.info(f"📊 当前权重配置: 技术匹配={self.default_weights.get('tech_match_weight', 0)}%, "
                       f"负载均衡={self.default_weights.get('load_balance_weight', 0)}%, "
                       f"交期紧迫={self.default_weights.get('deadline_weight', 0)}%, "
                       f"产值效率={self.default_weights.get('value_efficiency_weight', 0)}%, "
                       f"业务优先级={self.default_weights.get('business_priority_weight', 0)}%")
            
            # 🚀 智能预加载策略：根据批次数量和算法类型决定预加载策略
            context = {
                'lot_count': len(wait_lots),
                'algorithm': algorithm,
                'optimization_target': optimization_target,
                'user_id': user_id
            }
            preloaded_data = self._smart_preload_strategy(context)
            
            # 🚀 所有策略统一使用增强启发式算法
            # 差异化体现在前面已动态配置的权重参数中
            schedule_results = self._execute_heuristic_scheduling_optimized(wait_lots, preloaded_data)
            
            # 📝 记录策略使用情况
            logger.info(f"✅ {algorithm.upper()}策略执行完成，权重配置已生效")
            
            # 🔧 修复重复保存问题：移除重复的数据库保存调用
            # _execute_heuristic_scheduling_optimized方法内部已经调用了以下操作：
            # 1. self._save_to_database(scheduled_lots) - 保存成功排产记录
            # 2. self.failure_tracker.save_to_database() - 保存失败记录
            # 3. self.failure_tracker.clear() - 清空失败跟踪器
            # 因此这里不需要重复调用，避免产生重复记录
            
            # 🔥 确保失败记录已保存的注释保留，但移除实际的重复调用
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 🔧 返回包含统计信息的完整结果
            total_batches = len(wait_lots)
            scheduled_batches = len(schedule_results)
            failed_batches = total_batches - scheduled_batches
            success_rate = f"{scheduled_batches/total_batches*100:.1f}%" if total_batches > 0 else "0%"
            
            logger.info(f"🎉 OR-Tools智能排产完成！成功: {scheduled_batches}/{total_batches} ({success_rate}), 耗时: {execution_time:.2f} 秒")
            
            # 🚀 Task 1.1: 恢复原始权重配置，避免影响后续调用
            if dynamic_weights:
                self.default_weights = original_weights
                logger.debug("🔄 权重配置已恢复为默认值")
            
            # 🔧 返回完整的排产结果（包含统计信息）
            return {
                'schedule': schedule_results,
                'metrics': {
                    'total_batches': total_batches,
                    'scheduled_batches': scheduled_batches,
                    'failed_batches': failed_batches,
                    'success_rate': success_rate,
                    'execution_time': execution_time,
                    'algorithm': algorithm,
                    'optimization_target': optimization_target
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 执行OR-Tools智能排产失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 🔥 集成失败跟踪：记录算法执行异常失败
            try:
                wait_lots, _ = self.data_manager.get_wait_lot_data()
                for lot in wait_lots:
                    self.failure_tracker.add_failed_lot(
                        lot,
                        "算法执行异常",
                        f"排产算法执行失败: {str(e)}",
                        "real_scheduling_v2.0"
                    )
                self.failure_tracker.save_to_database()
                self.failure_tracker.clear()
                
                total_batches = len(wait_lots)
            except Exception as tracker_error:
                logger.error(f"记录算法异常失败: {tracker_error}")
                total_batches = 0
            
            # 🔧 失败时也返回完整的结构
            return {
                'schedule': [],
                'metrics': {
                    'total_batches': total_batches,
                    'scheduled_batches': 0,
                    'failed_batches': total_batches,
                    'success_rate': '0%',
                    'execution_time': 0.0,
                    'algorithm': algorithm,
                    'optimization_target': optimization_target
                }
            }

    def _get_strategy_weights(self, strategy: str, user_id: str = None, optimization_target: str = 'balanced') -> Dict:
        """
        🚀 Task 1.1 + P0 优化目标集成: 动态获取策略权重配置并应用优化目标调整
        
        实现前端策略选择 + 优化目标选择 → 数据库权重配置 → 优化目标调整 → 算法动态使用的完整链路
        
        Args:
            strategy: 排产策略名称 ('intelligent', 'deadline', 'product', 'value')
            user_id: 用户ID，用于获取个性化配置
            optimization_target: 优化目标 ('balanced', 'makespan', 'efficiency')
            
        Returns:
            Dict: 调整后的权重配置字典，如果获取失败返回None
        """
        try:
            from app.models import SchedulingConfig
            
            logger.debug(f"🔍 开始获取策略权重 - 策略: {strategy}, 用户: {user_id or 'system'}")
            
            # 1. 获取策略权重配置（支持用户个性化配置）
            weights = SchedulingConfig.get_strategy_weights(
                strategy_name=strategy, 
                user_id=user_id
            )
            
            if weights:
                # 2. 验证权重配置完整性
                required_fields = [
                    'tech_match_weight', 'load_balance_weight', 'deadline_weight',
                    'value_efficiency_weight', 'business_priority_weight'
                ]
                
                missing_fields = [field for field in required_fields if field not in weights]
                if missing_fields:
                    logger.warning(f"⚠️ 权重配置缺少字段: {missing_fields}")
                    return None
                
                # 3. 权重总和验证
                total_weight = sum(float(weights.get(field, 0)) for field in required_fields)
                if abs(total_weight - 100.0) > 0.01:
                    logger.warning(f"⚠️ 权重总和异常: {total_weight}%，期望100%")
                    # 不返回None，允许系统自动调整使用
                
                # 4. 转换为算法期望的格式（保持向后兼容）
                strategy_weights = {
                    'tech_match_weight': float(weights.get('tech_match_weight', 25.0)),
                    'load_balance_weight': float(weights.get('load_balance_weight', 20.0)),
                    'deadline_weight': float(weights.get('deadline_weight', 25.0)),
                    'value_efficiency_weight': float(weights.get('value_efficiency_weight', 20.0)),
                    'business_priority_weight': float(weights.get('business_priority_weight', 10.0))
                }
                
                # 5. 🎯 P0新功能：根据优化目标调整权重配置
                adjusted_weights = self._apply_optimization_target_adjustments(strategy_weights, optimization_target)
                
                logger.info(f"✅ 策略权重配置获取成功 - {strategy}: {strategy_weights}")
                if optimization_target != 'balanced':
                    logger.info(f"🎯 优化目标调整应用 - {optimization_target}: {adjusted_weights}")
                
                return adjusted_weights
            else:
                logger.warning(f"⚠️ 未找到策略 '{strategy}' 的权重配置")
                return None
                
        except Exception as e:
            logger.error(f"❌ 获取策略权重配置失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _apply_optimization_target_adjustments(self, base_weights: Dict, optimization_target: str) -> Dict:
        """
        🎯 P0新功能：根据优化目标调整权重配置
        
        Args:
            base_weights: 基础权重配置（来自策略+用户配置）
            optimization_target: 优化目标 ('balanced', 'makespan', 'efficiency')
            
        Returns:
            Dict: 调整后的权重配置
        """
        # 创建调整后的权重副本
        adjusted_weights = base_weights.copy()
        
        if optimization_target == 'balanced':
            # 均衡优化：保持原权重配置不变
            logger.debug("🎯 优化目标: 均衡优化 - 保持策略默认权重")
            return adjusted_weights
            
        elif optimization_target == 'makespan':
            # 最小化完工时间：强化交期和负载均衡权重
            logger.debug("🎯 优化目标: 最小化完工时间 - 强化交期和负载均衡")
            
            # 调整权重分配：重点关注交期(40%)和负载均衡(35%)
            adjusted_weights.update({
                'tech_match_weight': 15.0,      # 降低技术匹配权重
                'load_balance_weight': 35.0,    # 大幅提高负载均衡权重，确保快速完成
                'deadline_weight': 40.0,        # 大幅提高交期权重，避免延期
                'value_efficiency_weight': 5.0, # 降低产值权重，专注时间
                'business_priority_weight': 5.0 # 降低业务优先级权重
            })
            
        elif optimization_target == 'efficiency':
            # 最大化效率：强化技术匹配和产值效率权重
            logger.debug("🎯 优化目标: 最大化效率 - 强化技术匹配和产值效率")
            
            # 调整权重分配：重点关注技术匹配(35%)和产值效率(30%)
            adjusted_weights.update({
                'tech_match_weight': 35.0,      # 大幅提高技术匹配，减少改机时间
                'load_balance_weight': 15.0,    # 降低负载均衡权重
                'deadline_weight': 15.0,        # 降低交期权重，专注效率
                'value_efficiency_weight': 30.0, # 大幅提高产值效率权重
                'business_priority_weight': 5.0  # 降低业务优先级权重
            })
            
        else:
            logger.warning(f"⚠️ 未知的优化目标: {optimization_target}，使用默认权重")
            return adjusted_weights
        
        # 验证调整后权重总和
        total_weight = sum(adjusted_weights[key] for key in [
            'tech_match_weight', 'load_balance_weight', 'deadline_weight',
            'value_efficiency_weight', 'business_priority_weight'
        ])
        
        if abs(total_weight - 100.0) > 0.01:
            logger.warning(f"⚠️ 优化目标调整后权重总和异常: {total_weight}%")
        
        return adjusted_weights





    def _can_equipment_handle_lot(self, lot_data: Dict, equipment_data: Dict) -> bool:
        """检查设备是否能处理指定批次"""
        try:
            lot_requirements = lot_data['requirements']
            equipment = equipment_data['equipment']
            
            # 使用现有的设备匹配逻辑
            match_score, _, _ = self.calculate_equipment_match_score_optimized(lot_requirements, equipment, {})
            return match_score > 0  # 匹配分数大于0表示可以处理
            
        except Exception as e:
            logger.error(f"检查设备兼容性失败: {e}")
            return False

    def _get_priority_weight(self, lot: Dict) -> float:
        """获取批次优先级权重"""
        try:
            # 使用现有的业务优先级评分逻辑
            return self.calculate_business_priority_score(lot)
        except Exception:
            return 50.0

    def _calculate_changeover_time(self, lot_data: Dict, equipment_data: Dict) -> int:
        """计算改机时间"""
        try:
            lot_requirements = lot_data['requirements']
            equipment = equipment_data['equipment']
            
            # 使用现有的设备匹配逻辑获取改机时间
            _, _, changeover_time = self.calculate_equipment_match_score_optimized(lot_requirements, equipment, {})
            return changeover_time
            
        except Exception as e:
            logger.error(f"计算改机时间失败: {e}")
            return 30  # 默认改机时间


    
    def _save_to_database(self, schedule_results: List[Dict]) -> None:
        """保存排产结果到lotprioritydone表 - 修复连接泄漏"""
        try:
            # 使用连接池上下文管理器 - 完全修复连接泄漏
            from app.utils.db_connection_pool import get_db_connection_context
            
            # 🔧 第一步：获取原始批次数据
            wait_lots = []
            with get_db_connection_context() as wait_conn:
                wait_cursor = wait_conn.cursor()
                wait_cursor.execute("""
                    SELECT LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID, PKG_PN, 
                           PO_ID, STAGE, STEP, WIP_STATE, PROC_STATE, HOLD_STATE, FLOW_ID, FLOW_VER,
                           FAC_ID, CREATE_TIME
                    FROM ET_WAIT_LOT 
                    WHERE GOOD_QTY > 0 AND LOT_ID IS NOT NULL
                """)
                wait_lots_raw = wait_cursor.fetchall()
                wait_cursor.close()
                
                # 将原始数据转换为字典格式
                columns = ['LOT_ID', 'LOT_TYPE', 'GOOD_QTY', 'PROD_ID', 'DEVICE', 'CHIP_ID', 'PKG_PN', 
                          'PO_ID', 'STAGE', 'STEP', 'WIP_STATE', 'PROC_STATE', 'HOLD_STATE', 'FLOW_ID', 'FLOW_VER',
                          'FAC_ID', 'CREATE_TIME']
                for row in wait_lots_raw:
                    wait_lots.append(dict(zip(columns, row)))
            
            # 🔧 第二步：数据处理（在连接池外进行，提高性能）
            # 创建批次ID到原始数据的映射
            lot_map = {lot.get('LOT_ID'): lot for lot in wait_lots}
            
            # 按设备分组，分配PRIORITY执行顺序
            equipment_groups = {}
            for result in schedule_results:
                handler_id = result.get('HANDLER_ID', '')
                if handler_id not in equipment_groups:
                    equipment_groups[handler_id] = []
                equipment_groups[handler_id].append(result)
            
            # 为每个设备组内的批次分配1,2,3...的PRIORITY序号
            processed_results = []
            for handler_id, group_results in equipment_groups.items():
                # 🔧 确定性分组结果排序
                group_results.sort(key=lambda x: (
                    -x.get('COMPREHENSIVE_SCORE', 0),  # 主要排序键
                    x.get('LOT_ID', ''),               # 稳定键：批次ID
                    x.get('HANDLER_ID', ''),           # 稳定键：设备ID
                ))
                
                for priority_order, result in enumerate(group_results, 1):
                    result['EXECUTION_PRIORITY'] = priority_order  # 设置执行顺序
                    processed_results.append(result)
            
            # 准备插入数据
            insert_data = []
            for result in processed_results:
                # 查找原始批次信息
                original_lot = lot_map.get(result.get('LOT_ID'))
                
                # ✅ 修复：按照数据库实际字段顺序传递数据 (STEP字段放在最后)
                record = (
                    int(result.get('EXECUTION_PRIORITY', 1)),       # PRIORITY (同设备执行顺序1,2,3...，整数类型)
                    result.get('HANDLER_ID', ''),                   # HANDLER_ID (排产算法生成)
                    result.get('LOT_ID', ''),                       # LOT_ID
                    original_lot.get('LOT_TYPE', '量产批') if original_lot else '量产批',  # LOT_TYPE (从原始数据获取，默认量产批)
                    result.get('GOOD_QTY', 0),                      # GOOD_QTY
                    result.get('DEVICE', ''),                       # PROD_ID (使用DEVICE作为默认值)
                    result.get('DEVICE', ''),                       # DEVICE
                    original_lot.get('CHIP_ID', '') if original_lot else '',   # CHIP_ID
                    original_lot.get('PKG_PN', '') if original_lot else '',    # PKG_PN
                    original_lot.get('PO_ID', '') if original_lot else '',     # PO_ID (从待排产表传递)
                    result.get('STAGE', ''),                        # STAGE  
                    original_lot.get('WIP_STATE', '') if original_lot else '', # WIP_STATE
                    original_lot.get('PROC_STATE', '') if original_lot else '', # PROC_STATE
                    original_lot.get('HOLD_STATE', 0) if original_lot else 0,  # HOLD_STATE
                    original_lot.get('FLOW_ID', '') if original_lot else '',   # FLOW_ID
                    original_lot.get('FLOW_VER', '') if original_lot else '',  # FLOW_VER
                    original_lot.get('RELEASE_TIME', '') if original_lot else '',  # RELEASE_TIME
                    original_lot.get('FAC_ID', '') if original_lot else '',    # FAC_ID
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S'),   # CREATE_TIME (使用当前时间)
                    result.get('STEP', '') or (original_lot.get('STEP', '') if original_lot else '')  # STEP (最后一个字段!)
                )
                insert_data.append(record)
            
            # 🔧 第三步：数据库写入操作（在连接池内完成所有操作）
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 清空现有数据
                cursor.execute("DELETE FROM lotprioritydone")
                logger.info("🗑️ 已清空 lotprioritydone 表")
                
                # ✅ 修复：按照数据库实际字段顺序插入数据
                insert_query = """
                INSERT INTO lotprioritydone (
                    PRIORITY, HANDLER_ID, LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, 
                    CHIP_ID, PKG_PN, PO_ID, STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, 
                    FLOW_ID, FLOW_VER, RELEASE_TIME, FAC_ID, CREATE_TIME, STEP
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
                """
                
                # 批量插入
                cursor.executemany(insert_query, insert_data)
                conn.commit()
                cursor.close()
                
                logger.info(f"✅ 成功保存 {len(insert_data)} 条排产记录到 lotprioritydone 表")
                
        except Exception as e:
            logger.error(f"❌ 保存排产结果到数据库失败: {e}")
            import traceback
            traceback.print_exc()