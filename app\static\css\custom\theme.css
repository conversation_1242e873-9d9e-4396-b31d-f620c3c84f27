/* APS系统主题色彩定义 */
:root {
    --aps-primary: #b72424;
    --aps-primary-dark: #a01e1e;
    --aps-primary-light: #d73027;
    --aps-primary-lighter: #f8e6e6;
    --aps-secondary: #6c757d;
    --aps-success: #28a745;
    --aps-info: #17a2b8;
    --aps-warning: #ffc107;
    --aps-danger: #dc3545;
    
    /* 表格主题化扩展 */
    --aps-table-hover: rgba(183, 36, 36, 0.05);
    --aps-table-border: #dee2e6;
    --aps-table-header-bg: #f8f9fa;
    --aps-table-cell-padding: 0.4rem 0.5rem;
}

/* 分页组件主题化 */
.pagination .page-link {
    color: var(--aps-primary);
    border-color: #dee2e6;
}

.pagination .page-link:hover {
    color: var(--aps-primary-dark);
    background-color: var(--aps-primary-lighter);
    border-color: var(--aps-primary-light);
}

.pagination .page-link:focus {
    color: var(--aps-primary-dark);
    background-color: var(--aps-primary-lighter);
    border-color: var(--aps-primary);
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.25);
}

.pagination .page-item.active .page-link {
    color: #fff;
    background-color: var(--aps-primary);
    border-color: var(--aps-primary);
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* 按钮主题化 */
.btn-primary {
    background-color: var(--aps-primary);
    border-color: var(--aps-primary);
}

.btn-primary:hover {
    background-color: var(--aps-primary-dark);
    border-color: var(--aps-primary-dark);
}

.btn-primary:focus, .btn-primary.focus {
    box-shadow: 0 0 0 0.2rem rgba(183, 36, 36, 0.5);
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active {
    background-color: var(--aps-primary-dark);
    border-color: var(--aps-primary-dark);
}

/* 链接主题化 */
a {
    color: var(--aps-primary);
}

a:hover {
    color: var(--aps-primary-dark);
}

/* 表格边框主题化 */
.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

/* 导航栏主题化 */
.navbar-brand {
    color: var(--aps-primary) !important;
}

.nav-link.active {
    color: var(--aps-primary) !important;
}

/* 统一表格样式系统兼容性 */
.aps-table {
    --table-accent-bg: var(--aps-table-hover);
    --table-border-color: var(--aps-table-border);
}

.aps-table-responsive {
    --scrollbar-color: var(--aps-primary);
} 