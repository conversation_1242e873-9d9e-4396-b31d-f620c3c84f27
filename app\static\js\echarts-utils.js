class ChartUtils {
    static defaultTheme = {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        backgroundColor: 'transparent',
        textStyle: {},
        title: {
            textStyle: {
                color: '#464646'
            },
            subtextStyle: {
                color: '#6E7079'
            }
        },
        line: {
            itemStyle: {
                borderWidth: 1
            },
            lineStyle: {
                width: 2
            },
            symbolSize: 4,
            symbol: 'circle',
            smooth: false
        },
        radar: {
            itemStyle: {
                borderWidth: 1
            },
            lineStyle: {
                width: 2
            },
            symbolSize: 4,
            symbol: 'circle',
            smooth: false
        },
        bar: {
            itemStyle: {
                barBorderWidth: 0,
                barBorderColor: '#ccc'
            }
        },
        pie: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            }
        },
        scatter: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            }
        },
        boxplot: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            }
        },
        parallel: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            }
        },
        sankey: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            }
        },
        funnel: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            }
        },
        gauge: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            }
        },
        candlestick: {
            itemStyle: {
                color: '#eb5454',
                color0: '#47b262',
                borderColor: '#eb5454',
                borderColor0: '#47b262',
                borderWidth: 1
            }
        },
        graph: {
            itemStyle: {
                borderWidth: 0,
                borderColor: '#ccc'
            },
            lineStyle: {
                width: 1,
                color: '#aaa'
            },
            symbolSize: 4,
            symbol: 'circle',
            smooth: false,
            color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
            label: {
                color: '#eee'
            }
        },
        map: {
            itemStyle: {
                areaColor: '#eee',
                borderColor: '#444',
                borderWidth: 0.5
            },
            label: {
                color: '#000'
            },
            emphasis: {
                itemStyle: {
                    areaColor: 'rgba(255,215,0,0.8)',
                    borderColor: '#444',
                    borderWidth: 1
                },
                label: {
                    color: 'rgb(100,0,0)'
                }
            }
        },
        geo: {
            itemStyle: {
                areaColor: '#eee',
                borderColor: '#444',
                borderWidth: 0.5
            },
            label: {
                color: '#000'
            },
            emphasis: {
                itemStyle: {
                    areaColor: 'rgba(255,215,0,0.8)',
                    borderColor: '#444',
                    borderWidth: 1
                },
                label: {
                    color: 'rgb(100,0,0)'
                }
            }
        },
        categoryAxis: {
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#6E7079'
                }
            },
            axisTick: {
                show: true,
                lineStyle: {
                    color: '#6E7079'
                }
            },
            axisLabel: {
                show: true,
                color: '#6E7079'
            },
            splitLine: {
                show: false,
                lineStyle: {
                    color: ['#E0E6F1']
                }
            },
            splitArea: {
                show: false,
                areaStyle: {
                    color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']
                }
            }
        },
        valueAxis: {
            axisLine: {
                show: false,
                lineStyle: {
                    color: '#6E7079'
                }
            },
            axisTick: {
                show: false,
                lineStyle: {
                    color: '#6E7079'
                }
            },
            axisLabel: {
                show: true,
                color: '#6E7079'
            },
            splitLine: {
                show: true,
                lineStyle: {
                    color: ['#E0E6F1']
                }
            },
            splitArea: {
                show: false,
                areaStyle: {
                    color: ['rgba(250,250,250,0.2)', 'rgba(210,219,238,0.2)']
                }
            }
        },
        toolbox: {
            iconStyle: {
                borderColor: '#999'
            },
            emphasis: {
                iconStyle: {
                    borderColor: '#666'
                }
            }
        },
        legend: {
            textStyle: {
                color: '#333'
            }
        },
        tooltip: {
            axisPointer: {
                lineStyle: {
                    color: '#ccc',
                    width: 1
                },
                crossStyle: {
                    color: '#ccc',
                    width: 1
                }
            }
        },
        timeline: {
            lineStyle: {
                color: '#DAE1F5',
                width: 2
            },
            itemStyle: {
                color: '#A4B1D7',
                borderWidth: 1
            },
            controlStyle: {
                color: '#A4B1D7',
                borderColor: '#A4B1D7',
                borderWidth: 1
            },
            checkpointStyle: {
                color: '#316bf3',
                borderColor: 'fff'
            },
            label: {
                color: '#A4B1D7'
            },
            emphasis: {
                itemStyle: {
                    color: '#FFF'
                },
                controlStyle: {
                    color: '#A4B1D7',
                    borderColor: '#A4B1D7',
                    borderWidth: 2
                },
                label: {
                    color: '#A4B1D7'
                }
            }
        },
        visualMap: {
            color: ['#bf444c', '#d88273', '#f6efa6']
        },
        dataZoom: {
            handleSize: 'undefined%',
            textStyle: {}
        },
        markPoint: {
            label: {
                color: '#eee'
            },
            emphasis: {
                label: {
                    color: '#eee'
                }
            }
        }
    };

    static createChart(container, option = {}) {
        const chart = echarts.init(container);
        chart.setOption({
            ...this.defaultTheme,
            ...option
        });
        
        // 自动调整大小
        window.addEventListener('resize', () => {
            chart.resize();
        });
        
        return chart;
    }

    static createLineChart(container, data, title = '') {
        const option = {
            title: {
                text: title,
                left: 'center'
            },
            tooltip: {
                trigger: 'axis'
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: data.xAxis
            },
            yAxis: {
                type: 'value'
            },
            series: data.series.map(series => ({
                name: series.name,
                type: 'line',
                data: series.data,
                smooth: true,
                areaStyle: {
                    opacity: 0.1
                }
            }))
        };
        
        return this.createChart(container, option);
    }

    static createBarChart(container, data, title = '') {
        const option = {
            title: {
                text: title,
                left: 'center'
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.xAxis,
                axisLabel: {
                    interval: 0,
                    rotate: data.xAxis.length > 8 ? 45 : 0
                }
            },
            yAxis: {
                type: 'value'
            },
            series: data.series.map(series => ({
                name: series.name,
                type: 'bar',
                data: series.data,
                barMaxWidth: 50
            }))
        };
        
        return this.createChart(container, option);
    }

    static createPieChart(container, data, title = '') {
        const option = {
            title: {
                text: title,
                left: 'center'
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
                orient: 'vertical',
                left: 'left',
                data: data.map(item => item.name)
            },
            series: [{
                name: title,
                type: 'pie',
                radius: ['50%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '20',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: data
            }]
        };
        
        return this.createChart(container, option);
    }

    static createGaugeChart(container, value, title = '') {
        const option = {
            title: {
                text: title,
                left: 'center'
            },
            tooltip: {
                formatter: '{a} <br/>{b} : {c}%'
            },
            series: [{
                name: title,
                type: 'gauge',
                detail: { formatter: '{value}%' },
                data: [{ value: value, name: title }],
                axisLine: {
                    lineStyle: {
                        width: 30,
                        color: [
                            [0.3, '#ff6e76'],
                            [0.7, '#fddd60'],
                            [1, '#7cffb2']
                        ]
                    }
                }
            }]
        };
        
        return this.createChart(container, option);
    }
} 