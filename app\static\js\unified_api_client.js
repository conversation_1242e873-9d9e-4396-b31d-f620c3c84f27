/**
 * 统一API客户端 - 缓存统一化修复版
 * 
 * 🚀 核心功能：
 * - 统一的缓存控制策略
 * - 自动添加防缓存头部
 * - 标准化错误处理
 * - 性能监控
 */

class UnifiedAPIClient {
    constructor() {
        this.baseHeaders = {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
        
        // 性能统计
        this.stats = {
            totalRequests: 0,
            successRequests: 0,
            failedRequests: 0,
            totalResponseTime: 0,
            avgResponseTime: 0
        };
        
        console.log('🚀 统一API客户端初始化完成');
    }
    
    /**
     * 统一的无缓存请求方法
     * 确保所有请求都不被浏览器缓存
     */
    async fetchWithoutCache(url, options = {}) {
        const startTime = performance.now();
        this.stats.totalRequests++;
        
        try {
            // 添加时间戳防缓存
            const timestamp = Date.now();
            const separator = url.includes('?') ? '&' : '?';
            const finalUrl = `${url}${separator}_t=${timestamp}`;
            
            // 强制防缓存头部
            const cacheHeaders = {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            };
            
            const finalOptions = {
                ...options,
                headers: {
                    ...cacheHeaders,
                    ...this.baseHeaders,
                    ...options.headers
                }
            };
            
            console.log(`📡 API请求: ${finalUrl}`);
            const response = await fetch(finalUrl, finalOptions);
            
            // 记录性能统计
            const responseTime = performance.now() - startTime;
            this.stats.totalResponseTime += responseTime;
            this.stats.avgResponseTime = this.stats.totalResponseTime / this.stats.totalRequests;
            
            if (response.ok) {
                this.stats.successRequests++;
                console.log(`✅ API响应成功: ${finalUrl}, 耗时: ${responseTime.toFixed(1)}ms`);
            } else {
                this.stats.failedRequests++;
                console.warn(`⚠️ API响应失败: ${finalUrl}, 状态: ${response.status}`);
            }
            
            return response;
        } catch (error) {
            this.stats.failedRequests++;
            const responseTime = performance.now() - startTime;
            console.error(`❌ API请求异常: ${url}, 耗时: ${responseTime.toFixed(1)}ms, 错误:`, error);
            throw error;
        }
    }
    
    /**
     * 统一的数据获取方法
     */
    async getData(endpoint, params = {}) {
        try {
            const url = new URL(endpoint, window.location.origin);
            
            // 添加查询参数
            Object.keys(params).forEach(key => {
                if (params[key] !== undefined && params[key] !== null) {
                    url.searchParams.append(key, params[key]);
                }
            });
            
            const response = await this.fetchWithoutCache(url.toString());
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error(`❌ 数据获取失败: ${endpoint}`, error);
            throw error;
        }
    }
    
    /**
     * 统一的分页数据获取方法
     */
    async getPaginatedData(endpoint, page = 1, pageSize = 50, filters = {}) {
        try {
            const params = {
                page: page,
                size: pageSize,
                per_page: pageSize,
                ...filters
            };
            
            console.log(`📊 获取分页数据: ${endpoint}, 第${page}页, ${pageSize}条/页`);
            const data = await this.getData(endpoint, params);
            
            if (data.success) {
                console.log(`✅ 分页数据获取成功: 共${data.total || 0}条记录`);
            }
            
            return data;
        } catch (error) {
            console.error(`❌ 分页数据获取失败: ${endpoint}`, error);
            throw error;
        }
    }
    
    /**
     * 统一的POST请求方法
     */
    async postData(endpoint, data = {}) {
        try {
            const response = await this.fetchWithoutCache(endpoint, {
                method: 'POST',
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result;
        } catch (error) {
            console.error(`❌ POST请求失败: ${endpoint}`, error);
            throw error;
        }
    }
    
    /**
     * 统一的PUT请求方法
     */
    async putData(endpoint, data = {}) {
        try {
            const response = await this.fetchWithoutCache(endpoint, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result;
        } catch (error) {
            console.error(`❌ PUT请求失败: ${endpoint}`, error);
            throw error;
        }
    }
    
    /**
     * 统一的DELETE请求方法
     */
    async deleteData(endpoint) {
        try {
            const response = await this.fetchWithoutCache(endpoint, {
                method: 'DELETE'
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const result = await response.json();
            return result;
        } catch (error) {
            console.error(`❌ DELETE请求失败: ${endpoint}`, error);
            throw error;
        }
    }
    
    /**
     * 批量数据更新后的缓存失效
     */
    async invalidateCache(tables = []) {
        try {
            if (tables.length === 0) {
                console.log('🧹 清理所有缓存');
                await this.postData('/api/v2/system/cache/clear');
            } else {
                console.log(`🧹 清理指定表缓存: ${tables.join(', ')}`);
                for (const table of tables) {
                    await this.postData('/api/v2/system/cache/clear', { table_name: table });
                }
            }
            console.log('✅ 缓存清理完成');
        } catch (error) {
            console.warn('⚠️ 缓存清理失败:', error);
        }
    }
    
    /**
     * 获取性能统计信息
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalRequests > 0 ? 
                (this.stats.successRequests / this.stats.totalRequests * 100).toFixed(1) + '%' : '0%'
        };
    }
    
    /**
     * 健康检查
     */
    async healthCheck() {
        try {
            const response = await this.getData('/api/v2/system/cache/status');
            return {
                api_client: 'healthy',
                cache_system: response.success ? 'healthy' : 'unhealthy',
                stats: this.getStats(),
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                api_client: 'unhealthy',
                error: error.message,
                stats: this.getStats(),
                timestamp: new Date().toISOString()
            };
        }
    }
    
    /**
     * 显示性能报告
     */
    showPerformanceReport() {
        const stats = this.getStats();
        console.group('📊 API客户端性能报告');
        console.log(`总请求数: ${stats.totalRequests}`);
        console.log(`成功请求: ${stats.successRequests}`);
        console.log(`失败请求: ${stats.failedRequests}`);
        console.log(`成功率: ${stats.successRate}`);
        console.log(`平均响应时间: ${stats.avgResponseTime.toFixed(1)}ms`);
        console.groupEnd();
    }
}

// 全局实例 - 单例模式
window.apiClient = new UnifiedAPIClient();

// 便捷函数 - 向后兼容
window.fetchWithoutCache = (url, options) => window.apiClient.fetchWithoutCache(url, options);
window.getData = (endpoint, params) => window.apiClient.getData(endpoint, params);
window.getPaginatedData = (endpoint, page, pageSize, filters) => 
    window.apiClient.getPaginatedData(endpoint, page, pageSize, filters);

// 页面卸载时显示性能报告
window.addEventListener('beforeunload', () => {
    if (window.apiClient.stats.totalRequests > 0) {
        window.apiClient.showPerformanceReport();
    }
});

console.log('🚀 统一API客户端加载完成');
