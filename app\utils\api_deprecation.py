#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
API废弃处理工具

为废弃的API端点提供统一的处理机制，包括警告、日志记录和重定向
"""

from flask import jsonify, request, redirect, url_for
from functools import wraps
import logging
import json
from datetime import datetime
import os

# 设置废弃API日志
deprecation_logger = logging.getLogger('api.deprecation')
file_handler = logging.FileHandler('logs/deprecated_api_calls.log')
file_handler.setLevel(logging.INFO)
file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
deprecation_logger.addHandler(file_handler)

# 加载废弃API映射配置
DEPRECATED_API_MAP = {}
try:
    config_path = os.path.join(os.path.dirname(__file__), '../config/deprecated_api_map.json')
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            DEPRECATED_API_MAP = json.load(f)
except Exception as e:
    deprecation_logger.error(f"无法加载废弃API映射配置: {e}")

def deprecated_api(new_endpoint=None, removal_version=None, removal_date=None, status_code=200):
    """
    标记废弃的API端点

    Args:
        new_endpoint (str): 替代的新API端点路径
        removal_version (str): 计划移除的版本
        removal_date (str): 计划移除的日期，格式为YYYY-MM-DD
        status_code (int): 返回状态码，默认为200

    返回:
        装饰器函数
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 记录废弃API的调用
            user_agent = request.headers.get('User-Agent', '未知')
            referrer = request.headers.get('Referer', '未知')
            client_ip = request.remote_addr

            log_data = {
                'endpoint': request.path,
                'method': request.method,
                'user_agent': user_agent,
                'referrer': referrer,
                'client_ip': client_ip,
                'timestamp': datetime.now().isoformat()
            }

            # 记录到专门的废弃API日志
            deprecation_logger.warning(
                f"已调用废弃的API: {request.path} [{request.method}] - "
                f"IP: {client_ip}, 引用: {referrer}"
            )

            # 添加废弃警告到响应头
            warning_message = "此API已废弃"
            if removal_version:
                warning_message += f"，将在版本 {removal_version} 中移除"
            if removal_date:
                warning_message += f"，计划移除日期: {removal_date}"
            if new_endpoint:
                warning_message += f"，请改用: {new_endpoint}"
                
            # 根据配置决定是执行原函数还是重定向
            if new_endpoint and request.args.get('redirect', '').lower() == 'true':
                # 执行重定向
                try:
                    return redirect(new_endpoint, code=302)
                except Exception as e:
                    deprecation_logger.error(f"重定向到 {new_endpoint} 失败: {e}")
                    # 退回到警告模式
            
            # 执行原函数并添加警告
            result = func(*args, **kwargs)
            
            # 如果结果是元组(response, status_code)，则取出response
            if isinstance(result, tuple) and len(result) > 0:
                response, *rest = result
                if hasattr(response, 'headers'):
                    response.headers['X-API-Deprecated'] = 'true'
                    response.headers['X-API-Deprecated-Warning'] = warning_message
                    if new_endpoint:
                        response.headers['X-API-Replacement'] = new_endpoint
                return result
            
            # 如果结果是响应对象，直接添加头部
            if hasattr(result, 'headers'):
                result.headers['X-API-Deprecated'] = 'true'
                result.headers['X-API-Deprecated-Warning'] = warning_message
                if new_endpoint:
                    result.headers['X-API-Replacement'] = new_endpoint
                return result
                
            # 如果结果是字典，包装为JSON响应并添加警告
            if isinstance(result, dict):
                result['deprecated'] = True
                result['deprecation_warning'] = warning_message
                if new_endpoint:
                    result['replacement_api'] = new_endpoint
                response = jsonify(result)
                response.headers['X-API-Deprecated'] = 'true'
                response.headers['X-API-Deprecated-Warning'] = warning_message
                if new_endpoint:
                    response.headers['X-API-Replacement'] = new_endpoint
                return response, status_code
                
            # 未处理的情况，直接返回
            return result
            
        # 标记为废弃
        wrapper.__deprecated__ = True
        wrapper.__replacement__ = new_endpoint
        return wrapper
    
    return decorator

def deprecated_redirect(func=None, target_endpoint=None):
    """
    为废弃的路由提供自动重定向

    Args:
        func: 被装饰的函数
        target_endpoint: 目标端点路由

    Returns:
        装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def wrapped(*args, **kwargs):
            # 确定重定向目标
            redirect_target = target_endpoint
            
            # 如果没有指定目标，查找配置的映射
            if not redirect_target and request.path in DEPRECATED_API_MAP:
                redirect_target = DEPRECATED_API_MAP[request.path]
            
            # 如果没有重定向目标，执行原函数并添加警告
            if not redirect_target:
                deprecation_logger.warning(f"已调用废弃的API但无重定向目标: {request.path}")
                return deprecated_api()(f)(*args, **kwargs)
            
            # 记录重定向
            deprecation_logger.info(f"重定向废弃的API: {request.path} -> {redirect_target}")
            
            # 执行重定向
            return redirect(redirect_target, code=301)  # 301表示永久重定向
            
        return wrapped
    
    if func:
        return decorator(func)
    return decorator

def create_deprecation_notice(old_path, new_path=None, removal_date=None):
    """
    创建废弃API的通知响应

    Args:
        old_path: 废弃的API路径
        new_path: 替代的API路径
        removal_date: 计划移除的日期

    Returns:
        JSON响应
    """
    message = f"此API端点 ({old_path}) 已被废弃"
    if new_path:
        message += f"，请使用新的API端点: {new_path}"
    if removal_date:
        message += f"，将在 {removal_date} 之后移除"
        
    response = jsonify({
        'success': False,
        'error': 'deprecated_api',
        'message': message,
        'deprecated': True,
        'replacement_api': new_path
    })
    
    response.headers['X-API-Deprecated'] = 'true'
    response.headers['X-API-Deprecated-Warning'] = message
    if new_path:
        response.headers['X-API-Replacement'] = new_path
        
    return response, 299  # 使用299自定义状态码表示废弃但正常工作 