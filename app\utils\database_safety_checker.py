#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库安全检查工具
防止SQLAlchemy模型与真实数据库结构不匹配导致的数据损坏
"""

import logging
import pymysql
from sqlalchemy import text
from typing import Dict, List, Set, Tuple
from sqlalchemy import inspect
from app import db

logger = logging.getLogger(__name__)

class DatabaseSafetyChecker:
    """数据库安全检查器"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def check_table_structure_compatibility(self, table_name: str) -> Dict:
        """检查指定表的SQLAlchemy模型与真实数据库结构是否兼容"""
        try:
            # 获取真实数据库表结构
            real_structure = self._get_real_table_structure(table_name)
            if not real_structure:
                return {
                    'compatible': False,
                    'reason': f'表 {table_name} 在真实数据库中不存在',
                    'real_columns': [],
                    'model_columns': []
                }
            
            # 获取SQLAlchemy模型结构
            model_structure = self._get_model_table_structure(table_name)
            if not model_structure:
                return {
                    'compatible': False,
                    'reason': f'表 {table_name} 没有对应的SQLAlchemy模型',
                    'real_columns': list(real_structure.keys()),
                    'model_columns': []
                }
            
            # 比较结构
            compatibility = self._compare_structures(real_structure, model_structure)
            compatibility.update({
                'real_columns': list(real_structure.keys()),
                'model_columns': list(model_structure.keys())
            })
            
            return compatibility
            
        except Exception as e:
            self.logger.error(f"检查表 {table_name} 结构兼容性失败: {e}")
            return {
                'compatible': False,
                'reason': f'检查失败: {str(e)}',
                'real_columns': [],
                'model_columns': []
            }
    
    def _get_real_table_structure(self, table_name: str) -> Dict:
        """获取真实数据库表结构"""
        try:
            # 使用连接池上下文管理器 - 修复连接泄漏
            from app.utils.db_helper import get_mysql_connection_context
            with get_mysql_connection_context() as conn:
                cursor = conn.cursor()

                # 首先尝试原始表名
                try:
                    cursor.execute(f"DESCRIBE {table_name}")
                    columns = cursor.fetchall()
            except pymysql.err.ProgrammingError as e:
                if "doesn't exist" in str(e):
                    # 如果表不存在，尝试大小写变体
                    alternative_names = [table_name.upper(), table_name.lower()]
                    columns = None
                    
                    for alt_name in alternative_names:
                        if alt_name != table_name:  # 避免重复尝试
                            try:
                                cursor.execute(f"DESCRIBE {alt_name}")
                                columns = cursor.fetchall()
                                self.logger.info(f"表 {table_name} 找到替代名称: {alt_name}")
                                break
                            except pymysql.err.ProgrammingError:
                                continue
                    
                    if columns is None:
                        raise e  # 重新抛出原始异常
                else:
                    raise e
            
            structure = {}
            for column in columns:
                field, type_, null, key, default, extra = column
                structure[field] = {
                    'type': type_,
                    'nullable': null == 'YES',
                    'key': key,
                    'default': default,
                    'extra': extra
                }
            
            cursor.close()
            return structure
            
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 真实结构失败: {e}")
            return {}
    
    def _get_model_table_structure(self, table_name: str) -> Dict:
        """获取SQLAlchemy模型表结构"""
        try:
            # 检查是否在Flask应用上下文中
            from flask import has_app_context, current_app
            if not has_app_context():
                self.logger.warning(f"无Flask应用上下文，无法检查表 {table_name} 的模型结构")
                return {}
            
            # 通过SQLAlchemy获取模型结构
            inspector = inspect(db.engine)
            
            # 检查表是否存在于模型中
            model_tables = []
            for cls in db.Model.registry._class_registry.values():
                if hasattr(cls, '__tablename__') and cls.__tablename__ == table_name:
                    # 获取列信息
                    structure = {}
                    for column in cls.__table__.columns:
                        structure[column.name] = {
                            'type': str(column.type),
                            'nullable': column.nullable,
                            'primary_key': column.primary_key,
                            'default': str(column.default) if column.default else None
                        }
                    return structure
            
            return {}
            
        except Exception as e:
            self.logger.error(f"获取表 {table_name} 模型结构失败: {e}")
            return {}
    
    def _compare_structures(self, real_structure: Dict, model_structure: Dict) -> Dict:
        """比较真实数据库结构和模型结构"""
        real_columns = set(real_structure.keys())
        model_columns = set(model_structure.keys())
        
        # 找出差异
        missing_in_model = real_columns - model_columns
        missing_in_real = model_columns - real_columns
        common_columns = real_columns & model_columns
        
        # 检查共同列的类型兼容性
        type_mismatches = []
        for col in common_columns:
            real_type = real_structure[col]['type'].lower()
            model_type = model_structure[col]['type'].lower()
            
            # 简单的类型兼容性检查
            if not self._are_types_compatible(real_type, model_type):
                type_mismatches.append({
                    'column': col,
                    'real_type': real_type,
                    'model_type': model_type
                })
        
        # 判断是否兼容
        compatible = (
            len(missing_in_model) == 0 and 
            len(missing_in_real) == 0 and 
            len(type_mismatches) == 0
        )
        
        reason = []
        if missing_in_model:
            reason.append(f"模型中缺少列: {', '.join(missing_in_model)}")
        if missing_in_real:
            reason.append(f"数据库中缺少列: {', '.join(missing_in_real)}")
        if type_mismatches:
            reason.append(f"类型不匹配: {len(type_mismatches)} 个列")
        
        return {
            'compatible': compatible,
            'reason': '; '.join(reason) if reason else '结构完全匹配',
            'missing_in_model': list(missing_in_model),
            'missing_in_real': list(missing_in_real),
            'type_mismatches': type_mismatches,
            'common_columns': list(common_columns)
        }
    
    def _are_types_compatible(self, real_type: str, model_type: str) -> bool:
        """检查两种数据类型是否兼容"""
        # 简化的类型兼容性检查
        type_mappings = {
            'varchar': ['string', 'text'],
            'text': ['string', 'text'],
            'int': ['integer', 'int'],
            'tinyint': ['boolean', 'bool', 'integer'],
            'datetime': ['datetime'],
            'timestamp': ['datetime'],
            'float': ['float', 'numeric'],
            'decimal': ['numeric', 'decimal']
        }
        
        real_base = real_type.split('(')[0].lower()
        model_base = model_type.split('(')[0].lower()
        
        if real_base == model_base:
            return True
        
        for db_type, compatible_types in type_mappings.items():
            if real_base.startswith(db_type) and any(model_base.startswith(ct) for ct in compatible_types):
                return True
        
        return False
    
    def check_critical_tables(self) -> Dict:
        """检查关键业务表的兼容性"""
        # 统一使用小写表名，兼容Linux MySQL（区分大小写）
        critical_tables = [
            'users', 'user_permissions', 'ct', 'wip_lot', 'et_wait_lot',
            'eqp_status', 'et_ft_test_spec', 'et_uph_eqp', 'tcc_inv'
        ]
        
        results = {}
        safe_tables = []
        unsafe_tables = []
        
        for table in critical_tables:
            result = self.check_table_structure_compatibility(table)
            results[table] = result
            
            if result['compatible']:
                safe_tables.append(table)
            else:
                unsafe_tables.append(table)
        
        return {
            'safe_tables': safe_tables,
            'unsafe_tables': unsafe_tables,
            'details': results,
            'overall_safe': len(unsafe_tables) == 0
        }
    
    def is_safe_to_use_sqlalchemy_create_all(self) -> Tuple[bool, str]:
        """检查是否安全使用db.create_all()"""
        try:
            # 检查关键表
            critical_check = self.check_critical_tables()
            
            if not critical_check['overall_safe']:
                unsafe_tables = ', '.join(critical_check['unsafe_tables'])
                return False, f"以下关键表结构不匹配: {unsafe_tables}"
            
            # 检查是否有太多不匹配的表
            total_unsafe = len(critical_check['unsafe_tables'])
            if total_unsafe > 5:
                return False, f"发现 {total_unsafe} 个表结构不匹配，风险太高"
            
            return True, "结构检查通过，可以安全使用SQLAlchemy"
            
        except Exception as e:
            return False, f"安全检查失败: {str(e)}"

# 全局实例
safety_checker = DatabaseSafetyChecker()

def check_database_safety() -> Tuple[bool, str]:
    """快速安全检查"""
    return safety_checker.is_safe_to_use_sqlalchemy_create_all()

def get_table_compatibility_report(table_name: str) -> Dict:
    """获取指定表的兼容性报告"""
    return safety_checker.check_table_structure_compatibility(table_name) 