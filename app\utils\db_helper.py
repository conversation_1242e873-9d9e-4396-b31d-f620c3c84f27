# -*- coding: utf-8 -*-
"""
数据库连接助手 - 100% MySQL模式
统一管理MySQL数据库连接

Author: AI Assistant
Date: 2025-06-12
"""

import pymysql
from flask import current_app
import os
import logging
# 已迁移到连接池，不再需要配置管理器
# from .db_config_manager import get_config_manager

logger = logging.getLogger(__name__)

def get_mysql_connection(database=None):
    """获取MySQL数据库连接 - 使用连接池优化
    
    Args:
        database: 数据库名称，None表示使用默认业务数据库(aps)
    """
    try:
        # 使用连接池获取连接，避免重复创建
        from sqlalchemy import text
        
        # 确定数据库名称
        if database is None:
            db_name = 'aps'  # 默认业务数据库
        elif database == 'system':
            db_name = 'aps'  # 系统数据库已迁移到aps数据库
        else:
            db_name = database
        
        # 从连接池获取连接
        from app.utils.db_connection_pool import get_db_connection
        conn = get_db_connection(db_name)
        logger.debug(f"从连接池获取数据库连接: {db_name}")
        return conn
        
    except Exception as e:
        logger.error(f"连接池获取连接失败: {e}")
        # 如果连接池失败，回退到原有方式
        return _get_fallback_connection(database)

def _get_fallback_connection(database=None):
    """回退数据库连接方法（使用Flask配置）"""
    try:
        # 确定数据库名称 - 优先使用统一配置
        try:
            from config.aps_config import config as aps_config
            
            # 确定数据库名称
            if database is None:
                db_name = aps_config.DB_NAME
            elif database == 'system':
                db_name = aps_config.DB_NAME  # 系统数据库也使用主数据库
            else:
                db_name = database
            
            host = aps_config.DB_HOST
            port = aps_config.DB_PORT
            user = aps_config.DB_USER
            password = aps_config.DB_PASSWORD
            charset = aps_config.DB_CHARSET
            logger.debug(f"✅ 回退连接使用统一配置: {host}:{port}")
        except Exception as e:
            logger.debug(f"统一配置读取失败: {e}，尝试Flask配置")
            
            # 回退到Flask配置或环境变量
            flask_config = current_app.config if current_app else {}
            
            # 确定数据库名称
            if database is None:
                db_name = flask_config.get('MYSQL_DATABASE', 'aps')
            elif database == 'system':
                db_name = flask_config.get('MYSQL_SYSTEM_DATABASE', 'aps')
            else:
                db_name = database
            
            # 尝试从Flask配置或环境变量获取连接参数
            import os
            host = flask_config.get('MYSQL_HOST') or os.environ.get('DB_HOST', 'localhost')
            port = flask_config.get('MYSQL_PORT') or int(os.environ.get('DB_PORT', 3306))
            user = flask_config.get('MYSQL_USER') or os.environ.get('DB_USER', 'root')
            password = flask_config.get('MYSQL_PASSWORD') or os.environ.get('DB_PASSWORD', 'WWWwww123!')
            charset = flask_config.get('MYSQL_CHARSET', 'utf8mb4')
            
            logger.debug(f"✅ 回退连接使用Flask配置/环境变量: {host}:{port}")
        
        conn = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=db_name,
            charset=charset,
            cursorclass=pymysql.cursors.DictCursor,
            autocommit=False
        )
        
        logger.warning(f"使用回退配置连接数据库: {db_name}")
        return conn
        
    except Exception as e:
        logger.error(f"回退数据库连接也失败: {e}")
        raise

def get_aps_db():
    """获取APS业务数据库连接 (aps)"""
    return get_mysql_connection()

def get_system_db():
    """获取系统配置数据库连接 (已迁移到aps)"""
    return get_mysql_connection('aps')

# 表到数据库的映射
TABLE_DB_MAPPING = {
    # 系统配置表 - 已迁移到aps数据库
    'ai_settings': get_aps_db,
    'settings': get_aps_db,
    'user_filter_presets': get_aps_db,
    'scheduling_tasks': get_aps_db,
    'database_info': get_aps_db,
    'migration_log': get_aps_db,
    'system_settings': get_aps_db,
    
    # 核心业务表 - aps数据库
    'users': get_aps_db,
    'user_permissions': get_aps_db,
    'user_action_logs': get_aps_db,
    'product_priority_config': get_aps_db,
    'email_configs': get_aps_db,
    'email_attachments': get_aps_db,
    'order_data': get_aps_db,
    'excel_mappings': get_aps_db,
    'resources': get_aps_db,
    'products': get_aps_db,
    'production_orders': get_aps_db,
    'production_schedules': get_aps_db,
    'customer_orders': get_aps_db,
    'order_items': get_aps_db,
    'test_specs': get_aps_db,
    'maintenance_records': get_aps_db,
    'resource_usage_logs': get_aps_db,
    'wip_records': get_aps_db,
    
    # 生产数据表 - aps数据库
    'v_wip_lot_unified': get_aps_db,
    'v_et_wait_lot_unified': get_aps_db,
    'LotPriorityDone': get_aps_db,
    'lot_wip': get_aps_db,
    'v_ct_unified': get_aps_db,
    'v_eqp_status_unified': get_aps_db,
    'v_et_ft_test_spec_unified': get_aps_db,
    'v_et_uph_eqp_unified': get_aps_db,
    'v_et_recipe_file_unified': get_aps_db,
    'tcc_inv': get_aps_db,
    
    # 优先级配置表 - aps数据库
    'lot_priority_config': get_aps_db,
    'device_priority_config': get_aps_db,
    
    # 兼容性映射（大小写变体）
    'v_ct_unified': get_aps_db,
    'v_eqp_status_unified': get_aps_db,
    'v_et_ft_test_spec_unified': get_aps_db,
    'v_et_uph_eqp_unified': get_aps_db,
    'v_et_recipe_file_unified': get_aps_db,
    'TCC_INV': get_aps_db,
    'v_et_wait_lot_unified': get_aps_db,
    'v_wip_lot_unified': get_aps_db,
    'LOT_PRIORITY_CONFIG': get_aps_db,
    'DEVICE_PRIORITY_CONFIG': get_aps_db,
}

def get_db_for_table(table_name):
    """根据表名获取对应的数据库连接函数"""
    return TABLE_DB_MAPPING.get(table_name.lower(), get_aps_db)

def execute_query(table_name, query, params=None):
    """在正确的数据库中执行查询"""
    db_func = get_db_for_table(table_name)
    conn = db_func()
    try:
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        if query.strip().upper().startswith('SELECT'):
            return cursor.fetchall()
        else:
            conn.commit()
            return cursor.rowcount
    finally:
        conn.close()

def is_mysql_available():
    """检查MySQL是否可用 (Flask-SQLAlchemy版本)"""
    try:
        from sqlalchemy import text
        from app import db
        
        # 使用Flask-SQLAlchemy测试连接
        db.session.execute(text("SELECT 1"))
        return True
    except Exception:
        # 回退到原有方式
        try:
            conn = get_mysql_connection()
            try:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                return True
            finally:
                conn.close()
        except Exception:
            return False

def return_mysql_connection(connection, database=None):
    """归还MySQL数据库连接到连接池
    
    Args:
        connection: 数据库连接对象
        database: 数据库名称
    """
    try:
        from sqlalchemy import text
        
        db_name = database or 'aps'
        return_db_connection(connection, db_name)
        logger.debug(f"连接已归还到连接池: {db_name}")
    except Exception as e:
        logger.error(f"连接归还失败，强制关闭: {e}")
        try:
            connection.close()
        except:
            pass
