"""
性能监控中间件
"""
import time
import logging
from flask import request, g

logger = logging.getLogger(__name__)

class PerformanceMonitor:
    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        app.before_request(self.before_request)
        app.after_request(self.after_request)
    
    def before_request(self):
        g.start_time = time.time()
        
        # 记录用户活动和开始请求计数
        try:
            from app.utils.performance_metrics import performance_metrics
            from flask_login import current_user
            
            # 开始请求计数
            performance_metrics.start_request()
            
            # 记录用户活动
            if hasattr(current_user, 'username') and current_user.is_authenticated:
                performance_metrics.record_user_activity(current_user.username)
        except Exception:
            pass  # 静默处理错误，不影响正常请求
    
    def after_request(self, response):
        if hasattr(g, 'start_time'):
            duration = time.time() - g.start_time
            
            # 记录性能指标
            try:
                from app.utils.performance_metrics import performance_metrics
                
                # 结束请求计数
                performance_metrics.end_request()
                
                # 记录请求指标
                performance_metrics.record_request(
                    endpoint=request.endpoint or request.path,
                    duration=duration,
                    status_code=response.status_code
                )
            except Exception:
                pass  # 静默处理错误
            
            # 记录慢请求
            if duration > 2.0:  # 超过2秒的请求
                logger.warning(f"慢请求: {request.path} - {duration:.2f}s")
            
            # 添加性能头
            response.headers['X-Response-Time'] = f"{duration:.3f}s"
        
        return response
