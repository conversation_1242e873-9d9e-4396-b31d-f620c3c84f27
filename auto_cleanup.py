#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
自动清理临时测试文件脚本
由 cleanup_temp_files.py 生成
'''

import os
import shutil

def cleanup_files():
    '''清理临时文件'''
    
    # 临时测试文件
    temp_files = ['add_suggestion_column.py', 'analyze_performance_issue.py', 'analyze_scheduling_issue.py', 'batch_migrate_connections.py', 'check_connection_pool_migration.py', 'comprehensive_connection_test.py', 'comprehensive_export_test.py', 'comprehensive_startup_fix.py', 'connection_pool_monitor.py', 'debug_api_v3_export.py', 'final_verification.py', 'fix_connection_leaks.py', 'fix_v3_export_conflict.py', 'scheduling_failure_fix.py', 'simple_test.py', 'test_adjust_mode_fix.py', 'test_api_v3_export.py', 'test_comprehensive_excel_fix.py', 'test_connection_pool_fix.py', 'test_connection_pool_performance.py', 'test_daily_log_email.py', 'test_duplicate_fix.py', 'test_exe_logging.py', 'test_export_integration.py', 'test_no_pool_limit_simulation.py', 'test_step_field_integration.py', 'test_universal_optimization.py', 'unified_export_system.py', 'verify_after_scheduling.py', 'verify_unified_export.py', 'verify_v3_export_fix.py']
    
    # 临时报告文件
    report_files = ['connection_pool_monitor.log', 'connection_pool_test.log', 'connection_pool_test_report.txt', 'Excel自动保存问题全面修复报告.md', 'scheduling_validation.log', 'scheduling_validation_report_20250730_143703.txt', 'scheduling_validation_report_20250730_143841.txt', '完整导出功能统一方案.md', '排产重复检测配置说明.md', '统一导出系统使用指南.md']
    
    deleted_count = 0
    total_size = 0
    
    print("🗑️ 开始清理临时文件...")
    
    # 删除临时测试文件
    for file in temp_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            os.remove(file)
            print(f"   ✅ 删除: {file} ({size} bytes)")
            deleted_count += 1
            total_size += size
    
    # 删除临时报告文件
    for file in report_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            os.remove(file)
            print(f"   ✅ 删除: {file} ({size} bytes)")
            deleted_count += 1
            total_size += size
    
    print(f"\n🎉 清理完成!")
    print(f"   删除文件数: {deleted_count}")
    print(f"   释放空间: {total_size / 1024:.1f} KB")

if __name__ == "__main__":
    cleanup_files()
