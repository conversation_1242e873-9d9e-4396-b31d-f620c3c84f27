#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池健康检查脚本
用于诊断和监控连接池状态，防止连接池耗尽问题

Author: AI Assistant
Date: 2025-08-18
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def check_connection_pool_health():
    """检查连接池健康状态"""
    try:
        from app.utils.db_connection_pool import (
            get_connection_pool_health, 
            print_connection_stats,
            get_connection_pool
        )
        
        print("🔍 数据库连接池健康检查")
        print("=" * 60)
        print(f"🕒 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 获取健康状态
        health = get_connection_pool_health()
        
        # 显示总体状态
        status_emoji = {
            'healthy': '✅',
            'warning': '⚠️',
            'critical': '❌'
        }
        
        print(f"📊 总体状态: {status_emoji.get(health['status'], '❓')} {health['status'].upper()}")
        print()
        
        # 显示各连接池状态
        if health['pools']:
            print("📋 连接池详情:")
            for db_name, pool_info in health['pools'].items():
                status = pool_info['status']
                active = pool_info['active_connections']
                total = pool_info['total_connections']
                usage_rate = pool_info['usage_rate']
                
                print(f"  🗄️ {db_name}:")
                print(f"    状态: {status_emoji.get(status, '❓')} {status}")
                print(f"    连接: {active}/{total} ({usage_rate:.1%})")
                
                if status == 'critical':
                    print(f"    ❌ 严重: 连接池使用率过高，可能即将耗尽")
                elif status == 'warning':
                    print(f"    ⚠️ 警告: 连接池使用率偏高，需要关注")
                else:
                    print(f"    ✅ 正常: 连接池状态良好")
                print()
        
        # 显示警告和错误
        if health['warnings']:
            print("⚠️ 警告信息:")
            for warning in health['warnings']:
                print(f"  • {warning}")
            print()
        
        if health['errors']:
            print("❌ 错误信息:")
            for error in health['errors']:
                print(f"  • {error}")
            print()
        
        # 显示详细统计
        print("📈 详细统计:")
        print_connection_stats()
        
        return health
        
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_connection_pool():
    """测试连接池基本功能"""
    try:
        from app.utils.db_connection_pool import get_db_connection_context
        
        print("\n🧪 连接池功能测试")
        print("-" * 40)
        
        # 测试连接获取和释放
        test_count = 5
        success_count = 0
        
        for i in range(test_count):
            try:
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    if result and result[0] == 1:
                        success_count += 1
                        print(f"  ✅ 测试 {i+1}: 连接正常")
                    else:
                        print(f"  ❌ 测试 {i+1}: 查询结果异常")
            except Exception as e:
                print(f"  ❌ 测试 {i+1}: 连接失败 - {e}")
        
        success_rate = success_count / test_count
        print(f"\n📊 测试结果: {success_count}/{test_count} ({success_rate:.1%})")
        
        if success_rate >= 0.8:
            print("✅ 连接池功能正常")
        else:
            print("❌ 连接池功能异常，需要检查")
        
        return success_rate >= 0.8
        
    except Exception as e:
        print(f"❌ 连接池测试失败: {e}")
        return False

def generate_health_report(health_data):
    """生成健康检查报告"""
    if not health_data:
        return
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"connection_pool_health_report_{timestamp}.json"
    
    try:
        # 添加时间戳
        health_data['timestamp'] = datetime.now().isoformat()
        health_data['check_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(health_data, f, indent=2, ensure_ascii=False)
        
        print(f"📝 健康检查报告已保存: {report_file}")
        
    except Exception as e:
        print(f"❌ 保存报告失败: {e}")

def continuous_monitoring(interval=30, duration=300):
    """持续监控连接池状态"""
    print(f"\n🔄 开始持续监控 (间隔: {interval}秒, 持续: {duration}秒)")
    print("-" * 60)
    
    start_time = time.time()
    check_count = 0
    
    while time.time() - start_time < duration:
        check_count += 1
        print(f"\n📊 第 {check_count} 次检查 ({datetime.now().strftime('%H:%M:%S')})")
        
        health = get_connection_pool_health()
        if health:
            status = health['status']
            if status == 'critical':
                print("❌ 发现严重问题，建议立即处理！")
            elif status == 'warning':
                print("⚠️ 发现警告，建议关注")
            else:
                print("✅ 状态正常")
        
        time.sleep(interval)
    
    print(f"\n✅ 监控完成，共检查 {check_count} 次")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'test':
            # 测试模式
            health = check_connection_pool_health()
            test_result = test_connection_pool()
            if health:
                generate_health_report(health)
                
        elif command == 'monitor':
            # 持续监控模式
            interval = int(sys.argv[2]) if len(sys.argv) > 2 else 30
            duration = int(sys.argv[3]) if len(sys.argv) > 3 else 300
            continuous_monitoring(interval, duration)
            
        elif command == 'health':
            # 仅健康检查
            health = check_connection_pool_health()
            if health:
                generate_health_report(health)
        else:
            print("❌ 未知命令")
            print("用法:")
            print("  python check_connection_pool_health.py health  # 健康检查")
            print("  python check_connection_pool_health.py test   # 功能测试")
            print("  python check_connection_pool_health.py monitor [间隔] [持续时间]  # 持续监控")
    else:
        # 默认：完整检查
        health = check_connection_pool_health()
        test_result = test_connection_pool()
        
        if health:
            generate_health_report(health)
        
        # 给出建议
        print("\n💡 建议:")
        if health and health['status'] == 'critical':
            print("  1. 立即重启应用释放连接")
            print("  2. 检查代码中的连接泄漏")
            print("  3. 考虑增加连接池大小")
        elif health and health['status'] == 'warning':
            print("  1. 监控连接池使用情况")
            print("  2. 检查是否有连接泄漏")
            print("  3. 优化数据库查询性能")
        else:
            print("  连接池状态良好，继续保持")

if __name__ == '__main__':
    main()
