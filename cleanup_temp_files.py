#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理根目录下的临时测试文件
帮助区分哪些文件可以安全删除，哪些需要保留
"""

import os
import glob
from pathlib import Path

def categorize_files():
    """分类根目录下的Python文件"""
    
    # 核心项目文件（绝对不能删除）
    core_files = {
        'run.py': '主应用启动文件',
        'logging_config.py': '日志配置文件',
        'config.ini': '主配置文件',
        'requirements.txt': '依赖包列表',
        'setup_dev_config.py': '开发环境配置',
        'README.md': '项目说明文档'
    }
    
    # 配置和部署文件（建议保留）
    config_files = {
        'mysql_optimization_config.cnf': 'MySQL优化配置',
        'aps_nginx.conf': 'Nginx配置',
        'aps_supervisor.conf': 'Supervisor配置',
        'start_aps.sh': '启动脚本',
        'config_template.ini': '配置模板',
        'AEC-FT-Intelligent-Commander-Platform-v1.3.4.spec': 'PyInstaller配置',
        'build_login_fix.ps1': 'PowerShell构建脚本',
        '.cursorrules': 'Cursor IDE规则',
        '.gitignore': 'Git忽略文件',
        '.dockerignore': 'Docker忽略文件'
    }
    
    # 临时测试文件（可以安全删除）
    temp_test_files = {
        # 导出功能测试相关
        'fix_v3_export_conflict.py': 'v3导出冲突修复脚本（已完成）',
        'verify_v3_export_fix.py': 'v3导出修复验证脚本（已完成）',
        'test_api_v3_export.py': 'API v3导出测试脚本',
        'debug_api_v3_export.py': 'API v3导出调试脚本',
        'comprehensive_export_test.py': '综合导出测试脚本',
        'unified_export_system.py': '统一导出系统生成脚本（已完成）',
        'verify_unified_export.py': '统一导出验证脚本（已完成）',
        'test_export_integration.py': '导出集成测试脚本',
        
        # 连接池测试相关
        'comprehensive_connection_test.py': '连接池综合测试脚本（已完成）',
        'connection_pool_monitor.py': '连接池监控脚本（已完成）',
        'fix_connection_leaks.py': '连接泄漏修复脚本（已完成）',
        'test_connection_pool_fix.py': '连接池修复测试脚本',
        'test_no_pool_limit_simulation.py': '无连接池限制模拟脚本',
        'test_connection_pool_performance.py': '连接池性能测试脚本',
        'batch_migrate_connections.py': '批量连接迁移脚本',
        'check_connection_pool_migration.py': '连接池迁移检查脚本',
        
        # Excel和数据修复测试
        'test_duplicate_fix.py': '重复数据修复测试',
        'test_comprehensive_excel_fix.py': 'Excel修复综合测试',
        'test_step_field_integration.py': '步骤字段集成测试',
        'test_adjust_mode_fix.py': '调整模式修复测试',
        'test_universal_optimization.py': '通用优化测试',
        
        # 其他功能测试
        'test_daily_log_email.py': '日常日志邮件测试',
        'test_exe_logging.py': '可执行文件日志测试',
        'simple_test.py': '简单测试脚本',
        
        # 问题分析和修复脚本（一次性任务已完成）
        'analyze_performance_issue.py': '性能问题分析脚本（已完成）',
        'analyze_scheduling_issue.py': '排产问题分析脚本（已完成）',
        'final_verification.py': '最终验证脚本（已完成）',
        'verify_after_scheduling.py': '排产后验证脚本（已完成）',
        'scheduling_failure_fix.py': '排产失败修复脚本（已完成）',
        'comprehensive_startup_fix.py': '综合启动修复脚本（已完成）',
        'add_suggestion_column.py': '添加建议列脚本（已完成）'
    }
    
    # 可能有用的工具脚本（建议评估后决定）
    utility_files = {
        'quick_check.py': '快速检查工具',
        'simple_scheduling_check.py': '简单排产检查工具',
        'validate_scheduling_logic.py': '排产逻辑验证工具',
        'implement_step_via_wip_state.py': 'WIP状态步骤实现工具',
        'compile_scss.py': 'SCSS编译工具'
    }
    
    # 临时报告和日志文件
    temp_reports = {
        '统一导出系统使用指南.md': '统一导出系统使用指南（可保留作为文档）',
        '完整导出功能统一方案.md': '导出功能统一方案报告（可保留作为文档）',
        'Excel自动保存问题全面修复报告.md': 'Excel修复报告（已完成，可删除）',
        '排产重复检测配置说明.md': '排产重复检测说明（可保留）',
        'connection_pool_test_report.txt': '连接池测试报告（已完成，可删除）',
        'scheduling_validation_report_20250730_143703.txt': '排产验证报告（已完成，可删除）',
        'scheduling_validation_report_20250730_143841.txt': '排产验证报告（已完成，可删除）',
        'connection_pool_monitor.log': '连接池监控日志（可删除）',
        'connection_pool_test.log': '连接池测试日志（可删除）',
        'scheduling_validation.log': '排产验证日志（可删除）'
    }
    
    return core_files, config_files, temp_test_files, utility_files, temp_reports

def scan_current_files():
    """扫描当前根目录的文件"""
    current_files = []
    
    for file_path in Path('.').iterdir():
        if file_path.is_file():
            current_files.append(file_path.name)
    
    return current_files

def generate_cleanup_recommendations():
    """生成清理建议"""
    
    core_files, config_files, temp_test_files, utility_files, temp_reports = categorize_files()
    current_files = scan_current_files()
    
    print("🔍 根目录文件清理分析报告")
    print("=" * 70)
    
    # 分析当前存在的文件
    existing_core = [f for f in current_files if f in core_files]
    existing_config = [f for f in current_files if f in config_files]
    existing_temp = [f for f in current_files if f in temp_test_files]
    existing_utility = [f for f in current_files if f in utility_files]
    existing_reports = [f for f in current_files if f in temp_reports]
    
    # 显示分析结果
    print(f"\n📋 核心项目文件 (绝对不能删除): {len(existing_core)} 个")
    for file in existing_core:
        print(f"   ✅ {file} - {core_files[file]}")
    
    print(f"\n📋 配置和部署文件 (建议保留): {len(existing_config)} 个")
    for file in existing_config:
        print(f"   🔧 {file} - {config_files[file]}")
    
    print(f"\n📋 临时测试文件 (可以安全删除): {len(existing_temp)} 个")
    for file in existing_temp:
        print(f"   🗑️ {file} - {temp_test_files[file]}")
    
    print(f"\n📋 工具脚本 (建议评估): {len(existing_utility)} 个")
    for file in existing_utility:
        print(f"   🔍 {file} - {utility_files[file]}")
    
    print(f"\n📋 临时报告文件 (可考虑删除): {len(existing_reports)} 个")
    for file in existing_reports:
        print(f"   📄 {file} - {temp_reports[file]}")
    
    # 统计
    total_files = len(current_files)
    deletable_files = len(existing_temp) + len(existing_reports)
    
    print(f"\n📊 清理统计:")
    print(f"   总文件数: {total_files}")
    print(f"   可删除文件: {deletable_files}")
    print(f"   清理后剩余: {total_files - deletable_files}")
    print(f"   可释放空间: 约 {estimate_space(existing_temp + existing_reports)}")
    
    return existing_temp, existing_reports

def estimate_space(files):
    """估算文件占用空间"""
    total_size = 0
    for file in files:
        if os.path.exists(file):
            total_size += os.path.getsize(file)
    
    if total_size < 1024:
        return f"{total_size} B"
    elif total_size < 1024 * 1024:
        return f"{total_size / 1024:.1f} KB"
    else:
        return f"{total_size / (1024 * 1024):.1f} MB"

def create_cleanup_script(temp_files, report_files):
    """创建清理脚本"""
    
    script_content = f"""#!/usr/bin/env python3
# -*- coding: utf-8 -*-
'''
自动清理临时测试文件脚本
由 cleanup_temp_files.py 生成
'''

import os
import shutil

def cleanup_files():
    '''清理临时文件'''
    
    # 临时测试文件
    temp_files = {repr(temp_files)}
    
    # 临时报告文件
    report_files = {repr(report_files)}
    
    deleted_count = 0
    total_size = 0
    
    print("🗑️ 开始清理临时文件...")
    
    # 删除临时测试文件
    for file in temp_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            os.remove(file)
            print(f"   ✅ 删除: {{file}} ({{size}} bytes)")
            deleted_count += 1
            total_size += size
    
    # 删除临时报告文件
    for file in report_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            os.remove(file)
            print(f"   ✅ 删除: {{file}} ({{size}} bytes)")
            deleted_count += 1
            total_size += size
    
    print(f"\\n🎉 清理完成!")
    print(f"   删除文件数: {{deleted_count}}")
    print(f"   释放空间: {{total_size / 1024:.1f}} KB")

if __name__ == "__main__":
    cleanup_files()
"""
    
    with open('auto_cleanup.py', 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"\n🔧 已生成自动清理脚本: auto_cleanup.py")
    print(f"   运行方式: python auto_cleanup.py")

def main():
    """主函数"""
    
    temp_files, report_files = generate_cleanup_recommendations()
    
    print(f"\n🚀 建议的清理操作:")
    print(f"   1. 可以直接删除所有 🗑️ 标记的临时测试文件")
    print(f"   2. 临时报告文件可根据需要保留或删除")
    print(f"   3. 工具脚本建议评估后再决定是否删除")
    
    # 询问是否生成自动清理脚本
    print(f"\n❓ 是否生成自动清理脚本？")
    print(f"   这将创建一个 auto_cleanup.py 文件来批量删除临时文件")
    
    create_cleanup_script(temp_files, report_files)
    
    print(f"\n⚠️ 注意事项:")
    print(f"   • 删除前请确认这些文件确实不再需要")
    print(f"   • 建议先做好备份")
    print(f"   • 核心项目文件绝对不要删除")

if __name__ == "__main__":
    main()