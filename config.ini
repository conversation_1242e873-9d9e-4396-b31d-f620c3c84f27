# =================================================================
# APS开发环境配置文件
# 当前配置: 远程测试服务器
# =================================================================

[DATABASE]
# 数据库主机地址
host = localhost

# 数据库端口
port = 3306

# 数据库用户名
username = root

# 数据库密码
password = WWWwww123!

# 数据库名称
database = aps

# 字符集
charset = utf8mb4

# Flask-SQLAlchemy连接池配置（主应用）
pool_size = 60
max_overflow = 90
pool_timeout = 30
pool_recycle = 3600

# 自定义连接池配置（工具脚本） - 修复连接池满问题
custom_pool_min_size = 30
custom_pool_init_size = 60
custom_pool_max_size = 120

[APPLICATION]
# 应用监听地址
host = 0.0.0.0

# 应用端口
port = 5000

# 调试模式
debug = True

# Excel数据路径（开发环境）
excel_path = ./Excel数据2025.7.23

[SYSTEM]
# 时区设置
timezone = Asia/Shanghai

# 日志级别
log_level = DEBUG

# 最大工作线程数
max_workers = 10

[EMAIL]
# 发送邮箱地址（请修改为实际邮箱）
sender_email = <EMAIL>

# 发送邮箱密码（请修改为实际密码）
# 注意：如果使用163/QQ邮箱，需要使用授权码而不是登录密码
sender_password = your_email_password_here

# 接收邮箱（已固定）
recipient = <EMAIL>

# 连接超时配置
DB_CONNECTION_TIMEOUT=30
DB_POOL_TIMEOUT=10

# 性能优化配置
CACHE_TIMEOUT=300
API_TIMEOUT=30
SESSION_TIMEOUT=3600

# 并发控制
MAX_WORKERS=8
ASYNC_POOL_SIZE=50
