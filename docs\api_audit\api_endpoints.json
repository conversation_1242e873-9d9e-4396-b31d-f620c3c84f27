[{"endpoint": "/ai/chat", "method": "POST", "function": null, "file": "app\\api\\ai_assistant.py", "version": "未知", "module": "其他"}, {"endpoint": "/ai/db_chat", "method": "POST", "function": null, "file": "app\\api\\ai_database_assistant.py", "version": "未知", "module": "其他"}, {"endpoint": "/auth/users", "method": "GET", "function": null, "file": "app\\api\\auth.py", "version": "未知", "module": "认证授权"}, {"endpoint": "/auth/users", "method": "GET", "function": null, "file": "app\\api\\auth.py", "version": "未知", "module": "认证授权"}, {"endpoint": "/auth/users/<username>", "method": "GET", "function": null, "file": "app\\api\\auth.py", "version": "未知", "module": "认证授权"}, {"endpoint": "/auth/users/<username>", "method": "GET", "function": null, "file": "app\\api\\auth.py", "version": "未知", "module": "认证授权"}, {"endpoint": "/auth/users/<username>", "method": "GET", "function": null, "file": "app\\api\\auth.py", "version": "未知", "module": "认证授权"}, {"endpoint": "/embed.min.js", "method": "POST", "function": "get_embed_script", "file": "app\\api\\dify_proxy.py", "version": "未知", "module": "其他"}, {"endpoint": "/v1/chat-messages", "method": "POST", "function": "get_embed_script", "file": "app\\api\\dify_proxy.py", "version": "未知", "module": "其他"}, {"endpoint": "/v1/conversations", "method": "POST", "function": "get_embed_script", "file": "app\\api\\dify_proxy.py", "version": "未知", "module": "其他"}, {"endpoint": "/v1/conversations/<conversation_id>/messages", "method": "POST", "function": "get_embed_script", "file": "app\\api\\dify_proxy.py", "version": "未知", "module": "其他"}, {"endpoint": "/config", "method": "POST", "function": "get_embed_script", "file": "app\\api\\dify_proxy.py", "version": "未知", "module": "其他"}, {"endpoint": "/test-connection", "method": "POST", "function": "get_embed_script", "file": "app\\api\\dify_proxy.py", "version": "未知", "module": "其他"}, {"endpoint": "/api/email_configs", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs/<int:config_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs/<int:config_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs/<int:config_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs/<int:config_id>/test", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs/test", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs/<int:config_id>/preview", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_configs/<int:config_id>/fetch", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_attachments", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_attachments/<int:attachment_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_attachments/<int:attachment_id>/process", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/stats", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/<int:order_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/<int:order_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/export", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_attachments/<int:attachment_id>/download", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_attachments/batch_process", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/download_summary", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/excel_mappings", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/excel_mappings/<int:mapping_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/excel_mappings/<int:mapping_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/excel_mappings/<int:mapping_id>", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/email_attachments/dashboard", "method": "GET", "function": null, "file": "app\\api\\email_attachment.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/start", "method": "POST", "function": null, "file": "app\\api\\order_processing_api.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/<task_id>/pause", "method": "POST", "function": null, "file": "app\\api\\order_processing_api.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/<task_id>/stop", "method": "POST", "function": null, "file": "app\\api\\order_processing_api.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/<task_id>/status", "method": "POST", "function": null, "file": "app\\api\\order_processing_api.py", "version": "v1", "module": "其他"}, {"endpoint": "/api/order_data/stats", "method": "POST", "function": null, "file": "app\\api\\order_processing_api.py", "version": "v1", "module": "其他"}, {"endpoint": "/production/schedules/<int:schedule_id>", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/schedules/<int:schedule_id>", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/batch/upload", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/save-order", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/save-priority-done", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/import-from-directory", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/test-database-connection", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/database-status", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/production/history-data", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/test-all-database-connections", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/production/auto-schedule/stop", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/save-import-path", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/get-import-path", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/imported-files", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/file-data/<filename>", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/export-file/<filename>", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/import-progress", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/delete-file", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/check-mysql-databases", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/user-filter-presets", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/user-filter-presets/<int:preset_id>", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/production/auto-schedule", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/manual-schedule", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/save-schedule-history", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/schedule-history", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/production/export-schedule", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "生产管理"}, {"endpoint": "/api/production/history-times", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "v1", "module": "生产管理"}, {"endpoint": "/orders/parse-excel", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/orders/scan-files", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/orders/preview-file", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/orders/scan-lot-types", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/orders/classification-rules", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/orders/parse-progress/<task_id>", "method": "PUT", "function": "get_history_data", "file": "app\\api\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/tables", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/columns", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/info", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/structure", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/data", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/data", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/data/<record_id>", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/data/<record_id>", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/data/batch", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/validate", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/config/tables/<table_name>", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/cache/clear", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/migration/status", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/migration/test-page", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/page/<table_name>", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/navigation", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/universal/<table_name>", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables/<table_name>/export", "method": "GET", "function": "get_supported_tables", "file": "app\\api\\routes_v3.py", "version": "未知", "module": "其他"}, {"endpoint": "/status", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/jobs", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/jobs", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/test", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/jobs/<job_id>", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/jobs/<job_id>/run", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/logs", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/start", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/stop", "method": "GET", "function": null, "file": "app\\api\\scheduler_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/wait-lots", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/wip-lots", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/lots/<lot_id>", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/lots/<lot_id>/priority", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/lots/<lot_id>/equipment", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/scheduling", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/test-specs", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/test-specs/<spec_id>", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/statistics", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "GET", "function": "get_wait_lots", "file": "app\\api\\unified_production_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/data", "method": "GET", "function": "get_wip_lot_data", "file": "app\\api_v2\\wip_lot_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/summary", "method": "GET", "function": "get_wip_lot_data", "file": "app\\api_v2\\wip_lot_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/create", "method": "GET", "function": "get_wip_lot_data", "file": "app\\api_v2\\wip_lot_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/update/<int:record_id>", "method": "GET", "function": "get_wip_lot_data", "file": "app\\api_v2\\wip_lot_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/delete/<int:record_id>", "method": "GET", "function": "get_wip_lot_data", "file": "app\\api_v2\\wip_lot_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "GET", "function": "health_check", "file": "app\\api_v2\\__init__.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/user/info", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/users/<username>/permissions", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/users/<username>/permissions", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/session/status", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/session/refresh", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/debug/login-status", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/user/activity", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/menu-settings", "method": "GET", "function": "health_check", "file": "app\\api_v2\\auth\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/status", "method": "GET", "function": "system_status", "file": "app\\api_v2\\common\\__init__.py", "version": "未知", "module": "其他"}, {"endpoint": "/time", "method": "GET", "function": "system_status", "file": "app\\api_v2\\common\\__init__.py", "version": "未知", "module": "其他"}, {"endpoint": "/api/v2/high-concurrency/email-processing/start", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\high_concurrency_api.py", "version": "v2", "module": "其他"}, {"endpoint": "/api/v2/high-concurrency/email-processing/status", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\high_concurrency_api.py", "version": "v2", "module": "其他"}, {"endpoint": "/api/v2/high-concurrency/email-processing/stop", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\high_concurrency_api.py", "version": "v2", "module": "其他"}, {"endpoint": "/api/v2/high-concurrency/config", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\high_concurrency_api.py", "version": "v2", "module": "其他"}, {"endpoint": "/api/v2/high-concurrency/test-data/scan", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\high_concurrency_api.py", "version": "v2", "module": "其他"}, {"endpoint": "/api/v2/high-concurrency/performance/metrics", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\high_concurrency_api.py", "version": "v2", "module": "其他"}, {"endpoint": "/parse-excel-optimized", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\optimized_parser_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/parse-progress/<task_id>", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\optimized_parser_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/duplicate-confirm", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\optimized_parser_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/performance-stats", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\optimized_parser_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/cleanup-tasks", "method": "POST", "function": null, "file": "app\\api_v2\\orders\\optimized_parser_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/ft-summary", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\order_data_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/cp-summary", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\order_data_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/ft-summary/export", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\order_data_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/cp-summary/export", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\order_data_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/attachments/scan", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/attachments/scan/local", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/attachments/<int:attachment_id>/process", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/attachments/process-batch", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/preview", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/export", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/processing/status", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/parsing/validate", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/processing/start", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/processing/<task_id>/pause", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/processing/<task_id>/stop", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/processing/<task_id>/status", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/test-socketio", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/email-configs/<int:config_id>/save-auth", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/email-configs/<int:config_id>/get-auth", "method": "POST", "function": "scan_attachments", "file": "app\\api_v2\\orders\\semi_auto_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/summary-data", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/update-classification", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/batch-update-classification", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/classification-stats", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/generate-engineering-summary", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/generate-production-summary", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/preview-summary", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/download-summary/<filename>", "method": "GET", "function": null, "file": "app\\api_v2\\orders\\summary_preview_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/order-processing/start", "method": "POST", "function": "start_order_processing", "file": "app\\api_v2\\production\\order_processing.py", "version": "未知", "module": "其他"}, {"endpoint": "/order-processing/control/<task_id>", "method": "POST", "function": "start_order_processing", "file": "app\\api_v2\\production\\order_processing.py", "version": "未知", "module": "其他"}, {"endpoint": "/order-processing/status/<task_id>", "method": "POST", "function": "start_order_processing", "file": "app\\api_v2\\production\\order_processing.py", "version": "未知", "module": "其他"}, {"endpoint": "/order-processing/list", "method": "POST", "function": "start_order_processing", "file": "app\\api_v2\\production\\order_processing.py", "version": "未知", "module": "其他"}, {"endpoint": "/order-processing/stats", "method": "POST", "function": "start_order_processing", "file": "app\\api_v2\\production\\order_processing.py", "version": "未知", "module": "其他"}, {"endpoint": "/order-processing/health", "method": "POST", "function": "start_order_processing", "file": "app\\api_v2\\production\\order_processing.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/product", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/product", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/lot", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/device", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/match-batches", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/apply-matches", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/matching-report", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/batch-info/<batch_id>", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\priority_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/orders", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/orders/<int:order_id>", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/orders", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/schedules", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data-source/status", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data-source/switch", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data-source/validate", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data-source/metrics", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data-source/wait-lots", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data-source/uph-equipment", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data-source/equipment-status", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/scheduling/execute", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/priority/product", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/priority/product", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/priority/lot", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/priority/device", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/scheduling/history", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/scheduling/export", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/orders/export", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "订单管理"}, {"endpoint": "/priority-settings/upload", "method": "POST", "function": "health_check", "file": "app\\api_v2\\production\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/<int:lot_id>", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/<int:lot_id>", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/<int:lot_id>", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/columns", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/move-to-scheduled", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/upload", "method": "GET", "function": "health_check", "file": "app\\api_v2\\production\\wait_lots_api.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/<table_name>", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/columns/<table_name>", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/tables", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/<table_name>/export", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/<table_name>", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/<table_name>", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/<table_name>/<record_id>", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/data/<table_name>/batch", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/validate/<table_name>", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/validate/all", "method": "POST", "function": "resources_health_v2", "file": "app\\api_v2\\resources\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/stats", "method": "GET", "function": null, "file": "app\\api_v2\\system\\dashboard.py", "version": "未知", "module": "其他"}, {"endpoint": "/charts", "method": "GET", "function": null, "file": "app\\api_v2\\system\\dashboard.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/<int:config_id>", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/<int:config_id>", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/<int:config_id>", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/<int:config_id>/test", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/types", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/test", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/test-batch", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/configs/set-default/<int:config_id>", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/mappings", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/mappings", "method": "GET", "function": null, "file": "app\\api_v2\\system\\database_config.py", "version": "未知", "module": "其他"}, {"endpoint": "/metrics", "method": "GET", "function": null, "file": "app\\api_v2\\system\\missing_monitoring.py", "version": "未知", "module": "其他"}, {"endpoint": "/health-check", "method": "GET", "function": null, "file": "app\\api_v2\\system\\missing_monitoring.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "GET", "function": "health_check", "file": "app\\api_v2\\system\\monitoring.py", "version": "未知", "module": "其他"}, {"endpoint": "/metrics", "method": "GET", "function": "health_check", "file": "app\\api_v2\\system\\monitoring.py", "version": "未知", "module": "其他"}, {"endpoint": "/metrics/history", "method": "GET", "function": "health_check", "file": "app\\api_v2\\system\\monitoring.py", "version": "未知", "module": "其他"}, {"endpoint": "/metrics/clear", "method": "GET", "function": "health_check", "file": "app\\api_v2\\system\\monitoring.py", "version": "未知", "module": "其他"}, {"endpoint": "/health", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/database/status", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/database/test-connection", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/database/config", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/settings", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/settings", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/check-mysql-databases", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/email-scheduler", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/ai-config", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}, {"endpoint": "/ai-settings", "method": "POST", "function": "health_check", "file": "app\\api_v2\\system\\routes.py", "version": "未知", "module": "其他"}]