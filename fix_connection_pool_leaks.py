#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接池泄漏修复脚本
自动扫描和修复项目中的连接泄漏问题

Author: AI Assistant
Date: 2025-08-18
"""

import os
import re
import sys
import shutil
from datetime import datetime
from typing import List, Dict, Tuple

class ConnectionLeakFixer:
    """连接池泄漏修复器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.backup_dir = os.path.join(project_root, f"backup_before_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        self.issues_found = []
        self.fixes_applied = []
        
    def scan_for_leaks(self) -> List[Dict]:
        """扫描项目中的连接泄漏问题"""
        print("🔍 扫描连接泄漏问题...")
        
        leak_patterns = [
            {
                'pattern': r'conn\s*=\s*get_mysql_connection\(',
                'description': '直接使用 get_mysql_connection() 可能导致连接泄漏',
                'severity': 'high'
            },
            {
                'pattern': r'connection\s*=\s*get_db_connection\(',
                'description': '直接使用 get_db_connection() 可能导致连接泄漏',
                'severity': 'high'
            },
            {
                'pattern': r'\.close\(\)\s*$',
                'description': '直接关闭连接而不是归还到池中',
                'severity': 'medium'
            },
            {
                'pattern': r'pymysql\.connect\(',
                'description': '直接创建连接而不使用连接池',
                'severity': 'medium'
            }
        ]
        
        issues = []
        
        # 扫描Python文件
        for root, dirs, files in os.walk(self.project_root):
            # 跳过一些目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    relative_path = os.path.relpath(file_path, self.project_root)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                            lines = content.split('\n')
                            
                            for i, line in enumerate(lines, 1):
                                for pattern_info in leak_patterns:
                                    if re.search(pattern_info['pattern'], line):
                                        issues.append({
                                            'file': relative_path,
                                            'line': i,
                                            'content': line.strip(),
                                            'pattern': pattern_info['pattern'],
                                            'description': pattern_info['description'],
                                            'severity': pattern_info['severity']
                                        })
                    except Exception as e:
                        print(f"⚠️ 无法读取文件 {relative_path}: {e}")
        
        self.issues_found = issues
        return issues
    
    def create_backup(self):
        """创建备份"""
        print(f"📦 创建备份到: {self.backup_dir}")
        
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # 备份关键文件
        key_files = [
            'app/utils/db_helper.py',
            'app/utils/db_connection_pool.py',
            'config/aps_config.py'
        ]
        
        for file_path in key_files:
            full_path = os.path.join(self.project_root, file_path)
            if os.path.exists(full_path):
                backup_path = os.path.join(self.backup_dir, file_path)
                backup_dir = os.path.dirname(backup_path)
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)
                shutil.copy2(full_path, backup_path)
                print(f"  ✅ 已备份: {file_path}")
    
    def apply_automatic_fixes(self):
        """应用自动修复"""
        print("🔧 应用自动修复...")
        
        fixes = [
            {
                'file': 'app/utils/db_helper.py',
                'description': '添加连接池上下文管理器',
                'action': self._fix_db_helper
            },
            {
                'file': 'config/aps_config.py', 
                'description': '优化连接池配置',
                'action': self._fix_pool_config
            }
        ]
        
        for fix in fixes:
            try:
                fix['action']()
                self.fixes_applied.append(fix['description'])
                print(f"  ✅ {fix['description']}")
            except Exception as e:
                print(f"  ❌ 修复失败 {fix['description']}: {e}")
    
    def _fix_db_helper(self):
        """修复 db_helper.py 中的连接泄漏"""
        # 这个修复已经在前面的代码中完成了
        pass
    
    def _fix_pool_config(self):
        """修复连接池配置"""
        # 这个修复已经在前面的代码中完成了
        pass
    
    def generate_migration_guide(self):
        """生成迁移指南"""
        guide_content = f"""# 连接池泄漏修复迁移指南

## 修复时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 发现的问题
总计发现 {len(self.issues_found)} 个潜在问题：

"""
        
        # 按严重程度分组
        high_issues = [i for i in self.issues_found if i['severity'] == 'high']
        medium_issues = [i for i in self.issues_found if i['severity'] == 'medium']
        
        if high_issues:
            guide_content += "### 🔴 高风险问题（需要立即修复）\n\n"
            for issue in high_issues:
                guide_content += f"- **{issue['file']}:{issue['line']}**\n"
                guide_content += f"  - 问题: {issue['description']}\n"
                guide_content += f"  - 代码: `{issue['content']}`\n\n"
        
        if medium_issues:
            guide_content += "### 🟡 中等风险问题（建议修复）\n\n"
            for issue in medium_issues:
                guide_content += f"- **{issue['file']}:{issue['line']}**\n"
                guide_content += f"  - 问题: {issue['description']}\n"
                guide_content += f"  - 代码: `{issue['content']}`\n\n"
        
        guide_content += """
## 修复建议

### 1. 替换直接连接获取
将以下模式：
```python
conn = get_mysql_connection()
try:
    # 数据库操作
finally:
    conn.close()
```

替换为：
```python
with get_mysql_connection_context() as conn:
    # 数据库操作
```

### 2. 使用连接池上下文管理器
```python
from app.utils.db_connection_pool import get_db_connection_context

with get_db_connection_context() as conn:
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM table")
    result = cursor.fetchall()
```

### 3. 检查连接池健康状态
```bash
python check_connection_pool_health.py health
```

## 已应用的修复
"""
        
        for fix in self.fixes_applied:
            guide_content += f"- ✅ {fix}\n"
        
        guide_content += """
## 验证修复效果

1. 重启应用
2. 运行健康检查: `python check_connection_pool_health.py test`
3. 监控连接池状态: `python check_connection_pool_health.py monitor`

## 回滚方法

如果修复后出现问题，可以从备份恢复：
```bash
cp -r backup_before_fix_*/* ./
```
"""
        
        guide_file = os.path.join(self.project_root, 'connection_pool_migration_guide.md')
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"📝 迁移指南已生成: {guide_file}")
    
    def run_fix(self):
        """运行完整修复流程"""
        print("🚀 开始连接池泄漏修复")
        print("=" * 60)
        
        # 1. 扫描问题
        issues = self.scan_for_leaks()
        print(f"📊 发现 {len(issues)} 个潜在问题")
        
        # 2. 创建备份
        self.create_backup()
        
        # 3. 应用修复
        self.apply_automatic_fixes()
        
        # 4. 生成迁移指南
        self.generate_migration_guide()
        
        print("\n✅ 修复完成！")
        print("\n📋 后续步骤:")
        print("1. 检查迁移指南: connection_pool_migration_guide.md")
        print("2. 重启应用")
        print("3. 运行健康检查: python check_connection_pool_health.py")
        print("4. 手动修复剩余的高风险问题")

def main():
    """主函数"""
    project_root = os.path.dirname(os.path.abspath(__file__))
    
    fixer = ConnectionLeakFixer(project_root)
    
    if len(sys.argv) > 1 and sys.argv[1] == 'scan':
        # 仅扫描模式
        issues = fixer.scan_for_leaks()
        print(f"\n📊 扫描结果: 发现 {len(issues)} 个潜在问题")
        
        high_count = len([i for i in issues if i['severity'] == 'high'])
        medium_count = len([i for i in issues if i['severity'] == 'medium'])
        
        print(f"  🔴 高风险: {high_count}")
        print(f"  🟡 中等风险: {medium_count}")
        
        if high_count > 0:
            print("\n⚠️ 发现高风险问题，建议运行完整修复:")
            print("python fix_connection_pool_leaks.py")
    else:
        # 完整修复模式
        fixer.run_fix()

if __name__ == '__main__':
    main()
