#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
备用方案：使用WIP_STATE字段存储工步信息
"""

import pymysql

def implement_step_via_wip_state():
    """使用WIP_STATE字段存储工步信息"""
    print("🔄 备用方案：使用WIP_STATE字段存储工步信息")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(
            host='localhost', 
            user='root', 
            password='WWWwww123!', 
            database='aps', 
            charset='utf8mb4'
        )
        cursor = conn.cursor()
        
        # 1. 分析WIP_STATE字段当前使用情况
        print("1️⃣ 分析WIP_STATE字段当前状态:")
        cursor.execute("SELECT WIP_STATE, COUNT(*) FROM lotprioritydone GROUP BY WIP_STATE")
        wip_states = cursor.fetchall()
        
        for state, count in wip_states:
            print(f"   WIP_STATE='{state}': {count} 条记录")
        
        # 2. 检查源数据中的STEP值
        print(f"\n2️⃣ 源数据STEP值分析:")
        cursor.execute("SELECT STEP, COUNT(*) FROM et_wait_lot WHERE STEP IS NOT NULL AND STEP != '' GROUP BY STEP ORDER BY COUNT(*) DESC")
        step_values = cursor.fetchall()
        
        for step, count in step_values:
            print(f"   STEP='{step}': {count} 条记录")
        
        # 3. 执行替换方案
        print(f"\n3️⃣ 是否执行替换方案？")
        print(f"   将使用WIP_STATE字段存储工步(STEP)信息")
        print(f"   原有WIP_STATE数据将被工步数据覆盖")
        
        choice = input("确认执行？(y/N): ").strip().lower()
        
        if choice == 'y':
            print(f"\n🔄 开始替换...")
            
            # 备份现有数据
            backup_time = int(time.time())
            cursor.execute(f"""
                CREATE TABLE lotprioritydone_backup_{backup_time} AS 
                SELECT * FROM lotprioritydone
            """)
            print(f"   ✅ 已备份现有数据到 lotprioritydone_backup_{backup_time}")
            
            # 更新lotprioritydone表，将STEP值写入WIP_STATE字段
            cursor.execute("""
                UPDATE lotprioritydone lp
                LEFT JOIN et_wait_lot ew ON lp.LOT_ID = ew.LOT_ID
                SET lp.WIP_STATE = COALESCE(ew.STEP, lp.WIP_STATE)
                WHERE ew.STEP IS NOT NULL AND ew.STEP != ''
            """)
            
            updated_rows = cursor.rowcount
            conn.commit()
            
            print(f"   ✅ 已更新 {updated_rows} 条记录")
            
            # 验证结果
            cursor.execute("SELECT COUNT(*) FROM lotprioritydone WHERE WIP_STATE IN (SELECT DISTINCT STEP FROM et_wait_lot WHERE STEP IS NOT NULL AND STEP != '')")
            success_count = cursor.fetchone()[0]
            
            print(f"   ✅ 验证：{success_count} 条记录现在包含工步信息")
            
            print(f"\n4️⃣ 前端显示调整:")
            print(f"   需要将前端'WIP状态'列的标题改为'工步'")
            print(f"   文件: app/templates/production/done_lots.html")
            print(f"   将 'WIP状态' 改为 '工步'")
            
        else:
            print("   ❌ 用户取消操作")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")

if __name__ == "__main__":
    import time
    implement_step_via_wip_state()