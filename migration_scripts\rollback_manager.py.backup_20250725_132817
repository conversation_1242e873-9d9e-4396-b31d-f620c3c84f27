#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
渐进式数据库迁移 - 回滚管理器
负责备份和回滚操作
"""

import pymysql
import logging
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from migration_scripts.batch_configs import get_database_config, get_rollback_config

class RollbackManager:
    """回滚管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.db_config = get_database_config()
        self.rollback_config = get_rollback_config()
        self.connection_params = self.db_config['connection_params']
        
        # 创建备份目录
        self.backup_dir = Path('migration_backups')
        self.backup_dir.mkdir(exist_ok=True)
        
        # 清理过期备份
        self.cleanup_old_backups()
    
    def create_backup(self, tables, batch_number):
        """为指定表创建备份"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_info = {
                'batch_number': batch_number,
                'timestamp': timestamp,
                'tables': tables,
                'backup_files': {}
            }
            
            self.logger.info(f"📦 为批次 {batch_number} 创建备份...")
            
            # 为每个表创建备份
            for table in tables:
                backup_success = self.backup_single_table(table, timestamp)
                if backup_success:
                    backup_info['backup_files'][table] = f"{table}_{timestamp}.sql"
                    self.logger.info(f"✅ 表 {table} 备份完成")
                else:
                    self.logger.error(f"❌ 表 {table} 备份失败")
                    return False
            
            # 保存备份信息
            backup_info_file = self.backup_dir / f"batch_{batch_number}_{timestamp}.json"
            with open(backup_info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"📋 备份信息已保存: {backup_info_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"创建备份失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def backup_single_table(self, table_name, timestamp):
        """备份单个表"""
        source_conn = None
        target_conn = None
        
        try:
            # 连接源数据库和目标数据库
            source_conn = self.get_connection(self.db_config['source_db'])
            target_conn = self.get_connection(self.db_config['target_db'])
            
            if not source_conn and not target_conn:
                self.logger.error(f"无法连接到数据库进行表 {table_name} 的备份")
                return False
            
            backup_file = self.backup_dir / f"{table_name}_{timestamp}.sql"
            
            # 备份源数据库中的表（如果存在）
            if source_conn:
                success = self.dump_table_to_file(source_conn, table_name, backup_file, 'source')
                if success:
                    return True
            
            # 如果源数据库中没有，尝试备份目标数据库中的表
            if target_conn:
                success = self.dump_table_to_file(target_conn, table_name, backup_file, 'target')
                return success
            
            return False
            
        except Exception as e:
            self.logger.error(f"备份表 {table_name} 时发生错误: {e}")
            return False
        
        finally:
            if source_conn:
                source_conn.close()
            if target_conn:
                target_conn.close()
    
    def dump_table_to_file(self, connection, table_name, backup_file, db_type):
        """将表数据导出到文件"""
        try:
            cursor = connection.cursor()
            
            # 检查表是否存在
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            if not cursor.fetchone():
                self.logger.info(f"表 {table_name} 在 {db_type} 数据库中不存在，创建空备份")
                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(f"-- 表 {table_name} 在 {db_type} 数据库中不存在\n")
                    f.write(f"-- 备份时间: {datetime.now()}\n")
                return True
            
            with open(backup_file, 'w', encoding='utf-8') as f:
                # 写入备份头信息
                f.write(f"-- 表 {table_name} 备份文件\n")
                f.write(f"-- 来源: {db_type} 数据库\n")
                f.write(f"-- 备份时间: {datetime.now()}\n")
                f.write(f"-- 数据库: {connection.db.decode('utf-8') if hasattr(connection, 'db') else 'unknown'}\n\n")
                
                # 获取表结构
                cursor.execute(f"SHOW CREATE TABLE {table_name}")
                create_table_result = cursor.fetchone()
                if create_table_result:
                    f.write(f"DROP TABLE IF EXISTS `{table_name}`;\n")
                    f.write(f"{create_table_result[1]};\n\n")
                
                # 获取表数据
                cursor.execute(f"SELECT * FROM {table_name}")
                data = cursor.fetchall()
                
                if data:
                    # 获取字段名
                    cursor.execute(f"DESCRIBE {table_name}")
                    fields = [row[0] for row in cursor.fetchall()]
                    
                    f.write(f"-- 数据插入\n")
                    f.write(f"LOCK TABLES `{table_name}` WRITE;\n")
                    f.write(f"/*!40000 ALTER TABLE `{table_name}` DISABLE KEYS */;\n")
                    
                    # 分批写入数据
                    batch_size = 1000
                    for i in range(0, len(data), batch_size):
                        batch_data = data[i:i + batch_size]
                        
                        values_list = []
                        for row in batch_data:
                            # 处理NULL值和字符串转义
                            escaped_values = []
                            for value in row:
                                if value is None:
                                    escaped_values.append('NULL')
                                elif isinstance(value, str):
                                    # 转义单引号和反斜杠
                                    escaped_value = value.replace('\\', '\\\\').replace("'", "\\'")
                                    escaped_values.append(f"'{escaped_value}'")
                                elif isinstance(value, (int, float)):
                                    escaped_values.append(str(value))
                                else:
                                    escaped_values.append(f"'{str(value)}'")
                            
                            values_list.append(f"({','.join(escaped_values)})")
                        
                        if values_list:
                            field_names = ','.join([f"`{field}`" for field in fields])
                            f.write(f"INSERT INTO `{table_name}` ({field_names}) VALUES\n")
                            f.write(',\n'.join(values_list))
                            f.write(';\n')
                    
                    f.write(f"/*!40000 ALTER TABLE `{table_name}` ENABLE KEYS */;\n")
                    f.write(f"UNLOCK TABLES;\n")
                else:
                    f.write(f"-- 表 {table_name} 无数据\n")
            
            self.logger.info(f"表 {table_name} 已备份到 {backup_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出表 {table_name} 到文件时发生错误: {e}")
            return False
    
    def rollback_batch(self, batch_number):
        """回滚指定批次"""
        try:
            self.logger.info(f"🔄 开始回滚批次 {batch_number}")
            
            # 查找批次的备份信息
            backup_info = self.find_backup_info(batch_number)
            if not backup_info:
                self.logger.error(f"未找到批次 {batch_number} 的备份信息")
                return False
            
            # 执行回滚操作
            success_count = 0
            total_count = len(backup_info['tables'])
            
            for table in backup_info['tables']:
                if table in backup_info['backup_files']:
                    backup_file = self.backup_dir / backup_info['backup_files'][table]
                    if self.restore_table_from_backup(table, backup_file):
                        success_count += 1
                        self.logger.info(f"✅ 表 {table} 回滚成功")
                    else:
                        self.logger.error(f"❌ 表 {table} 回滚失败")
                else:
                    self.logger.warning(f"表 {table} 没有备份文件")
            
            if success_count == total_count:
                self.logger.info(f"✅ 批次 {batch_number} 回滚成功")
                return True
            else:
                self.logger.error(f"❌ 批次 {batch_number} 回滚部分失败: {success_count}/{total_count}")
                return False
                
        except Exception as e:
            self.logger.error(f"回滚批次 {batch_number} 时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def find_backup_info(self, batch_number):
        """查找批次的备份信息"""
        try:
            backup_files = list(self.backup_dir.glob(f"batch_{batch_number}_*.json"))
            if not backup_files:
                return None
            
            # 选择最新的备份
            latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)
            
            with open(latest_backup, 'r', encoding='utf-8') as f:
                backup_info = json.load(f)
            
            return backup_info
            
        except Exception as e:
            self.logger.error(f"查找备份信息时发生错误: {e}")
            return None
    
    def restore_table_from_backup(self, table_name, backup_file):
        """从备份文件恢复表"""
        target_conn = None
        
        try:
            if not backup_file.exists():
                self.logger.error(f"备份文件不存在: {backup_file}")
                return False
            
            target_conn = self.get_connection(self.db_config['target_db'])
            if not target_conn:
                return False
            
            cursor = target_conn.cursor()
            
            # 读取并执行备份文件
            with open(backup_file, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句并执行
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for statement in sql_statements:
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                    except Exception as e:
                        self.logger.warning(f"执行SQL语句失败: {statement[:100]}... 错误: {e}")
            
            target_conn.commit()
            self.logger.info(f"表 {table_name} 从备份恢复成功")
            return True
            
        except Exception as e:
            self.logger.error(f"从备份恢复表 {table_name} 时发生错误: {e}")
            return False
        
        finally:
            if target_conn:
                target_conn.close()
    
    def cleanup_old_backups(self):
        """清理过期备份"""
        try:
            retention_days = self.rollback_config['backup_retention_days']
            cutoff_date = datetime.now() - timedelta(days=retention_days)
            
            deleted_count = 0
            for backup_file in self.backup_dir.iterdir():
                if backup_file.is_file():
                    file_time = datetime.fromtimestamp(backup_file.stat().st_mtime)
                    if file_time < cutoff_date:
                        backup_file.unlink()
                        deleted_count += 1
            
            if deleted_count > 0:
                self.logger.info(f"🗑️ 清理了 {deleted_count} 个过期备份文件")
                
        except Exception as e:
            self.logger.warning(f"清理过期备份时发生错误: {e}")
    
    def list_backups(self):
        """列出所有备份"""
        try:
            backups = []
            for backup_file in self.backup_dir.glob("batch_*.json"):
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_info = json.load(f)
                    backups.append({
                        'file': backup_file.name,
                        'batch_number': backup_info['batch_number'],
                        'timestamp': backup_info['timestamp'],
                        'tables': backup_info['tables']
                    })
            
            return sorted(backups, key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            self.logger.error(f"列出备份时发生错误: {e}")
            return []
    
    def get_connection(self, database_name):
        """获取数据库连接"""
        try:
            conn = pymysql.connect(
                database=database_name,
                **self.connection_params
            )
            return conn
        except Exception as e:
            self.logger.error(f"连接数据库 {database_name} 失败: {e}")
            return None 