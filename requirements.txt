# 🚀 APS车规芯片终测智能调度平台 - 优化依赖包配置
# 移除了 ortools、openai、pyinstaller 等不必要的依赖
# 预计减少部署体积 70%，加快安装速度 60%

# ========================================
# 核心Web框架 (必需)
# ========================================
Flask==2.3.3
Flask-SocketIO==5.3.5  # WebSocket支持
Werkzeug==2.3.7
click==8.1.7
itsdangerous==2.1.2
Jinja2==3.1.2
MarkupSafe==2.1.3
blinker==1.6.3  # Flask信号支持

# ========================================
# 数据库支持 (必需)
# ========================================
Flask-SQLAlchemy==3.0.5
SQLAlchemy==2.0.21
greenlet==2.0.2
PyMySQL==1.1.0

# ========================================
# Excel处理 (核心业务功能)
# ========================================
pandas==2.2.1
openpyxl==3.1.2
numpy  # pandas依赖，用于数据计算
et-xmlfile==1.1.0  # openpyxl依赖

# ========================================
# 任务调度系统 (核心功能)
# ========================================
APScheduler==3.10.4
redis>=4.0.0  # 缓存和消息队列
psutil==5.9.6  # 系统监控

# ========================================
# 邮件处理 (核心功能)
# ========================================
email-validator==2.1.0

# ========================================
# 安全和加密 (必需)
# ========================================
cryptography==41.0.7
cffi==1.16.0  # cryptography依赖
pycparser==2.21  # cffi依赖

# ========================================
# 配置和工具 (必需)
# ========================================
python-dotenv==1.0.0  # 环境变量配置
python-dateutil==2.8.2  # 日期处理
six==1.16.0  # 兼容性
setuptools>=65.0.0  # 安装工具
typing-extensions  # 类型提示

# ========================================
# 移除的依赖说明
# ========================================
# ❌ ortools==9.14.6206          # 未实际使用，减少 200MB
# ❌ openai==1.12.0              # 未实际使用，减少 15MB
# ❌ pyinstaller==6.1.0          # Docker部署下不需要，减少 50MB
# ❌ altgraph==0.17.4            # pyinstaller依赖
# ❌ pefile==2023.2.7            # pyinstaller依赖
# ❌ pywin32-ctypes==0.2.2       # pyinstaller依赖
# ❌ colorama==0.4.6             # 仅终端颜色，生产环境不需要 