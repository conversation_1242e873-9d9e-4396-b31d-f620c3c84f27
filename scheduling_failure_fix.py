#!/usr/bin/env python3
"""
排产失败跟踪器 - 统一版本
用于记录和分析排产失败的批次信息
集成智能建议生成和数据库配置管理
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime
import json
import logging
import os
from typing import Dict, List, Optional

# 配置日志
logger = logging.getLogger(__name__)

class SchedulingFailureTracker:
    """排产失败跟踪器 - 增强版"""
    
    def __init__(self):
        self.failed_lots = []
        self.success_lots = []
        # 使用智能配置读取，支持exe环境
        self.db_config = self._get_database_config()
        
        # 🔥 新增：排产去重机制
        self.execution_cache = {}
        self.min_execution_interval = 300  # 5分钟最小间隔
    
    def _get_database_config(self) -> Dict:
        """获取数据库配置，优先级：外部配置文件 > 环境变量 > 默认值"""
        try:
            # 🔧 尝试使用应用内配置系统（如果可用）
            try:
                from app.utils.db_connection_pool import get_db_connection_context
                # 如果连接池可用，使用连接池配置
                logger.info("🔗 使用数据库连接池配置")
                return None  # 使用连接池，不需要直接配置
            except ImportError:
                logger.debug("连接池不可用，使用直接配置")
            
            # 🔧 尝试从配置文件读取
            try:
                import configparser
                config_paths = ['config.ini', '../config.ini', '../../config.ini']
                
                for config_path in config_paths:
                    if os.path.exists(config_path):
                        logger.info(f"📄 找到配置文件: {config_path}")
                        config = configparser.ConfigParser()
                        config.read(config_path, encoding='utf-8')
                        
                        if 'database' in config:
                            db_section = config['database']
                            return {
                                'host': db_section.get('host', 'localhost'),
                                'port': int(db_section.get('port', 3306)),
                                'user': db_section.get('user', 'root'),
                                'password': db_section.get('password', 'WWWwww123!'),
                                'database': db_section.get('database', 'aps'),
                                'charset': 'utf8mb4'
                            }
                
                logger.debug("未找到有效的配置文件")
            except Exception as e:
                logger.debug(f"读取配置文件失败: {e}")
                
        except Exception as e:
            logger.debug(f"获取数据库配置异常: {e}")
        
        # 🔧 回退到环境变量或默认配置
        return {
            'host': os.environ.get('MYSQL_HOST', os.environ.get('DB_HOST', 'localhost')),
            'port': int(os.environ.get('MYSQL_PORT', os.environ.get('DB_PORT', 3306))),
            'user': os.environ.get('MYSQL_USER', os.environ.get('DB_USER', 'root')),
            'password': os.environ.get('MYSQL_PASSWORD', os.environ.get('DB_PASSWORD', 'WWWwww123!')),
            'database': os.environ.get('MYSQL_DATABASE', os.environ.get('DB_NAME', 'aps')),
            'charset': 'utf8mb4'
        }
    
    def add_failed_lot(self, lot: Dict, failure_reason: str, failure_details: str = "", algorithm_version: str = "v2.0", session_id: str = None):
        """添加失败批次记录 - 增强版"""
        try:
            # 🔥 优化：使用稳定的session ID生成策略
            if not session_id:
                session_id = self.generate_stable_session_id()

            # 🔥 生成智能建议解决方案
            device = lot.get('DEVICE', '')
            stage = lot.get('STAGE', '')
            suggestion = self._generate_enhanced_suggestion(failure_reason, failure_details, device, stage)

            failed_lot = {
                'lot_id': lot.get('LOT_ID', ''),
                'device': device,
                'stage': stage,
                'good_qty': lot.get('GOOD_QTY', 0),
                'failure_reason': failure_reason,
                'failure_details': failure_details,
                'suggestion': suggestion,  # 🔥 新增：智能建议
                'algorithm_version': algorithm_version,
                'session_id': session_id,  # 🔥 会话ID跟踪
                'execution_context': json.dumps({
                    'pkg_pn': lot.get('PKG_PN', ''),
                    'chip_id': lot.get('CHIP_ID', ''),
                    'lot_type': lot.get('LOT_TYPE', ''),
                    'wip_state': lot.get('WIP_STATE', ''),
                    'proc_state': lot.get('PROC_STATE', ''),
                    'create_time': str(lot.get('CREATE_TIME', '')) if lot.get('CREATE_TIME') else '',
                    'timestamp': datetime.now().isoformat(),
                    'session_id': session_id
                }),
                'timestamp': datetime.now()
            }
            self.failed_lots.append(failed_lot)
            logger.warning(f"❌ 批次排产失败: {failed_lot['lot_id']} - {failure_reason} (会话:{session_id})")
            if suggestion:
                logger.info(f"💡 建议解决方案: {suggestion}")
        except Exception as e:
            logger.error(f"记录失败批次时出错: {e}")
    
    def _generate_enhanced_suggestion(self, failure_reason: str, failure_details: str, device: str = None, stage: str = None) -> str:
        """生成智能建议解决方案"""
        try:
            # 基础建议映射
            base_suggestions = {
                "配置需求获取失败": "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范",
                "无合适设备": "请检查设备状态和配置匹配，确保有可用的设备处理该产品",
                "设备ID无效": "请检查设备配置，确保HANDLER_ID字段正确",
                "算法执行异常": "请检查系统日志，可能需要技术支持",
                "测试规范缺失": "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范",
                "设备状态异常": "请检查设备运行状态，确保设备处于可用状态",
                "产能不足": "请考虑调整排产策略或增加设备资源",
                "工艺流程错误": "请检查工艺流程配置和版本是否正确"
            }
            
            # 匹配基础建议
            suggestion = ""
            for key, base_suggestion in base_suggestions.items():
                if key in failure_reason:
                    suggestion = base_suggestion
                    break
            
            # 如果没有精确匹配，使用模糊匹配
            if not suggestion:
                if "配置" in failure_reason or "config" in failure_reason.lower():
                    suggestion = "请检查相关配置设置和参数是否正确"
                elif "设备" in failure_reason or "equipment" in failure_reason.lower():
                    suggestion = "请检查设备兼容性、状态和配置"
                elif "规范" in failure_reason or "spec" in failure_reason.lower():
                    suggestion = "请检查相关规范配置是否完整"
                elif "数据" in failure_reason or "data" in failure_reason.lower():
                    suggestion = "请检查输入数据的完整性和有效性"
                else:
                    suggestion = "请联系技术支持人员进行详细诊断"
            
            # 🔥 根据设备和工序添加特定建议
            if device and stage:
                suggestion += f" (器件: {device}, 工序: {stage})"
                
                # 特殊情况的额外建议
                if stage == 'FT':
                    suggestion += " - 检查FT测试程序和测试机配置"
                elif stage == 'CP':
                    suggestion += " - 检查CP测试参数和探针台状态"
            
            return suggestion
            
        except Exception as e:
            logger.error(f"生成建议时出错: {e}")
            return "请联系技术支持人员"
    
    def add_success_lot(self, lot: Dict, handler_id: str):
        """添加成功批次记录"""
        try:
            success_lot = {
                'lot_id': lot.get('LOT_ID', ''),
                'device': lot.get('DEVICE', ''),
                'stage': lot.get('STAGE', ''),
                'handler_id': handler_id,
                'timestamp': datetime.now()
            }
            self.success_lots.append(success_lot)
            logger.info(f"✅ 批次排产成功: {success_lot['lot_id']} -> {handler_id}")
        except Exception as e:
            logger.error(f"记录成功批次时出错: {e}")
    
    def save_to_database(self):
        """保存失败批次到数据库 - 增强版（支持连接池和直接连接）"""
        try:
            # 🔧 优先尝试使用连接池
            connection = None
            cursor = None
            use_pool = False
            
            try:
                if self.db_config is None:
                    # 尝试使用连接池
                    from app.utils.db_connection_pool import get_db_connection_context
                    logger.debug("🔗 使用数据库连接池")
                    # 注意：在with语句外部无法使用连接，需要重新设计
                    self._save_with_connection_pool()
                    return
                else:
                    # 使用直接连接
                    connection = mysql.connector.connect(**self.db_config)
                    logger.debug("🔗 使用直接数据库连接")
                
                if not connection or (not use_pool and not connection.is_connected()):
                    logger.error("❌ 数据库连接失败")
                    return
                
                # 🔧 修复：使用try-finally确保cursor资源正确释放
                try:
                    if not use_pool:
                        cursor = connection.cursor()
                    else:
                        cursor = connection.cursor()
                    
                    # 确保表存在
                    self._ensure_table_exists(cursor, connection, use_pool)
                    
                    # 如果没有新的失败批次，直接返回
                    if not self.failed_lots:
                        logger.info("ℹ️ 没有新的失败批次需要保存")
                        return
                    
                    # 🔧 获取当前session_id，只清理当前session的记录
                    current_session_id = self.failed_lots[0].get('session_id') if self.failed_lots else None
                    if current_session_id:
                        logger.info(f"🧹 清理当前session的旧失败记录: {current_session_id}")
                        cursor.execute("DELETE FROM scheduling_failed_lots WHERE session_id = %s", (current_session_id,))
                        deleted_count = cursor.rowcount
                        if not use_pool:
                            connection.commit()
                        logger.info(f"✅ 已清理session {current_session_id} 的 {deleted_count} 条旧记录")
                        
                        # 🔧 清理过期的历史session（保留最近10个session）
                        self._cleanup_old_sessions(cursor, connection, use_pool)
                    
                    # 🔥 验证数据真正保存前的记录数
                    cursor.execute("SELECT COUNT(*) FROM scheduling_failed_lots")
                    count_before = cursor.fetchone()
                    count_before_value = count_before[0] if isinstance(count_before, (tuple, list)) else (count_before.get('COUNT(*)', 0) if isinstance(count_before, dict) else count_before)
                    logger.info(f"🔍 保存前数据库记录数: {count_before_value}")
                    
                    # 🔥 批量插入新的失败批次（包含suggestion字段）
                    insert_sql = """
                    INSERT INTO scheduling_failed_lots 
                    (lot_id, device, stage, good_qty, failure_reason, failure_details, 
                     suggestion, algorithm_version, session_id, execution_context, timestamp)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    failed_records = []
                    for lot in self.failed_lots:
                        failed_records.append((
                            lot['lot_id'],
                            lot['device'],
                            lot['stage'],
                            lot['good_qty'],
                            lot['failure_reason'],
                            lot['failure_details'],
                            lot.get('suggestion', ''),  # 🔥 新增建议字段
                            lot['algorithm_version'],
                            lot['session_id'],
                            lot['execution_context'],
                            lot['timestamp']
                        ))
                    
                    cursor.executemany(insert_sql, failed_records)
                    if not use_pool:
                        connection.commit()
                    
                    logger.info(f"✅ 已保存 {len(failed_records)} 条失败批次记录到数据库（包含智能建议）")
                    
                finally:
                    # 🔧 关键修复：无论是否出现异常，都确保cursor被正确关闭
                    if cursor:
                        try:
                            cursor.close()
                            logger.debug("✅ 直连Cursor已安全关闭")
                        except Exception as cursor_error:
                            logger.warning(f"⚠️ 关闭直连cursor时出现异常: {cursor_error}")
                    
                    if not use_pool and connection:
                        try:
                            connection.close()
                            logger.debug("✅ 直连Connection已安全关闭")
                        except Exception as conn_error:
                            logger.warning(f"⚠️ 关闭直连connection时出现异常: {conn_error}")
                
            except ImportError:
                # 连接池不可用，使用直接连接
                if self.db_config:
                    backup_connection = None
                    backup_cursor = None
                    try:
                        backup_connection = mysql.connector.connect(**self.db_config)
                        backup_cursor = backup_connection.cursor()
                        # 执行相同的保存逻辑
                        self._save_with_direct_connection(backup_connection, backup_cursor)
                    except Exception as backup_error:
                        logger.error(f"❌ 备用连接失败: {backup_error}")
                    finally:
                        # 🔧 确保备用连接的资源也被正确释放
                        if backup_cursor:
                            try:
                                backup_cursor.close()
                            except:
                                pass
                        if backup_connection:
                            try:
                                backup_connection.close()
                            except:
                                pass
                else:
                    logger.error("❌ 无法获取数据库配置")
                    
        except Error as e:
            logger.error(f"保存失败批次到数据库时出错: {e}")
        except Exception as e:
            logger.error(f"保存失败批次时发生异常: {e}")
    
    def _save_with_direct_connection(self, connection, cursor):
        """使用直接连接保存数据的辅助方法"""
        try:
            # 确保表存在
            self._ensure_table_exists(cursor, connection, False)
            
            # 执行保存逻辑（与上面相同）
            if not self.failed_lots:
                logger.info("ℹ️ 没有新的失败批次需要保存")
                return
            
            current_session_id = self.failed_lots[0].get('session_id') if self.failed_lots else None
            if current_session_id:
                cursor.execute("DELETE FROM scheduling_failed_lots WHERE session_id = %s", (current_session_id,))
                deleted_count = cursor.rowcount
                connection.commit()
                logger.info(f"✅ 已清理session {current_session_id} 的 {deleted_count} 条旧记录")
            
            # 批量插入
            insert_sql = """
            INSERT INTO scheduling_failed_lots 
            (lot_id, device, stage, good_qty, failure_reason, failure_details, 
             suggestion, algorithm_version, session_id, execution_context, timestamp)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """
            
            failed_records = []
            for lot in self.failed_lots:
                failed_records.append((
                    lot['lot_id'], lot['device'], lot['stage'], lot['good_qty'],
                    lot['failure_reason'], lot['failure_details'], lot.get('suggestion', ''),
                    lot['algorithm_version'], lot['session_id'], lot['execution_context'],
                    lot['timestamp']
                ))
            
            cursor.executemany(insert_sql, failed_records)
            connection.commit()
            
            logger.info(f"✅ 已保存 {len(failed_records)} 条失败批次记录到数据库")
            
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def _save_with_connection_pool(self):
        """使用连接池保存数据的专用方法 - 修复cursor资源泄漏"""
        try:
            from app.utils.db_connection_pool import get_db_connection_context
            
            # 如果没有新的失败批次，直接返回
            if not self.failed_lots:
                logger.info("ℹ️ 没有新的失败批次需要保存")
                return
                
            with get_db_connection_context() as connection:
                # 🔧 修复：使用try-finally确保cursor资源正确释放
                cursor = None
                try:
                    cursor = connection.cursor()
                    
                    # 确保表存在
                    self._ensure_table_exists(cursor, None, True)
                    
                    # 获取当前session_id，只清理当前session的记录
                    current_session_id = self.failed_lots[0].get('session_id') if self.failed_lots else None
                    if current_session_id:
                        logger.info(f"🧹 清理当前session的旧失败记录: {current_session_id}")
                        cursor.execute("DELETE FROM scheduling_failed_lots WHERE session_id = %s", (current_session_id,))
                        deleted_count = cursor.rowcount
                        logger.info(f"✅ 已清理session {current_session_id} 的 {deleted_count} 条旧记录")
                        
                        # 清理过期的历史session
                        self._cleanup_old_sessions(cursor, None, True)
                    
                    # 批量插入新的失败批次
                    insert_sql = """
                    INSERT INTO scheduling_failed_lots 
                    (lot_id, device, stage, good_qty, failure_reason, failure_details, 
                     suggestion, algorithm_version, session_id, execution_context, timestamp)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    
                    failed_records = []
                    for lot in self.failed_lots:
                        failed_records.append((
                            lot['lot_id'],
                            lot['device'],
                            lot['stage'],
                            lot['good_qty'],
                            lot['failure_reason'],
                            lot['failure_details'],
                            lot.get('suggestion', ''),
                            lot['algorithm_version'],
                            lot['session_id'],
                            lot['execution_context'],
                            lot['timestamp']
                        ))
                    
                    cursor.executemany(insert_sql, failed_records)
                    
                    # 🔧 修复：连接池需要显式提交事务
                    try:
                        connection.commit()
                        
                        # 🔥 验证数据真正保存后的记录数
                        cursor.execute("SELECT COUNT(*) FROM scheduling_failed_lots")
                        count_after = cursor.fetchone()
                        count_after_value = count_after[0] if isinstance(count_after, (tuple, list)) else (count_after.get('COUNT(*)', 0) if isinstance(count_after, dict) else count_after)
                        logger.info(f"🔍 保存后数据库记录数: {count_after_value}")
                        
                        if count_after_value >= len(failed_records):
                            logger.info(f"✅ 已保存 {len(failed_records)} 条失败批次记录到数据库（连接池，已提交事务，已验证）")
                        else:
                            logger.error(f"❌ 数据保存验证失败: 预期{len(failed_records)}条，实际{count_after_value}条")
                            
                    except Exception as commit_error:
                        logger.error(f"❌ 连接池事务提交失败: {commit_error}")
                        try:
                            connection.rollback()
                        except:
                            pass
                        raise commit_error
                
                finally:
                    # 🔧 关键修复：无论是否出现异常，都确保cursor被正确关闭
                    if cursor:
                        try:
                            cursor.close()
                            logger.debug("✅ Cursor已安全关闭")
                        except Exception as cursor_error:
                            logger.warning(f"⚠️ 关闭cursor时出现异常: {cursor_error}")
                
        except Exception as e:
            import traceback
            logger.error(f"使用连接池保存失败批次时出错: {e}")
            logger.error(f"详细错误: {traceback.format_exc()}")
    
    def _ensure_table_exists(self, cursor, connection=None, use_pool=False):
        """确保scheduling_failed_lots表存在 - 增强版（包含suggestion字段）"""
        try:
            # 检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
            """)
            
            result = cursor.fetchone()
            # 🔧 兼容不同类型的cursor结果格式
            if isinstance(result, dict):
                # 连接池返回字典格式
                count_value = result.get('COUNT(*)', 0)
            elif isinstance(result, (tuple, list)):
                # 标准cursor返回元组格式
                count_value = result[0]
            else:
                # 其他格式，尝试直接使用
                count_value = result
                
            table_exists = count_value > 0
            
            if not table_exists:
                # 🔥 创建增强表（包含suggestion和session_id字段）
                create_table_sql = """
                CREATE TABLE scheduling_failed_lots (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    lot_id VARCHAR(50) NOT NULL COMMENT '批次ID',
                    device VARCHAR(100) NOT NULL COMMENT '设备名称',
                    stage VARCHAR(50) NOT NULL COMMENT '工序',
                    good_qty INT DEFAULT 0 COMMENT '良品数量',
                    failure_reason VARCHAR(500) NOT NULL COMMENT '失败原因',
                    failure_details TEXT COMMENT '失败详情',
                    suggestion TEXT COMMENT '智能建议解决方案',
                    algorithm_version VARCHAR(50) COMMENT '算法版本',
                    session_id VARCHAR(100) COMMENT '排产会话ID',
                    execution_context JSON COMMENT '执行上下文',
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失败时间',
                    
                    INDEX idx_lot_id (lot_id),
                    INDEX idx_device (device),
                    INDEX idx_stage (stage),
                    INDEX idx_timestamp (timestamp),
                    INDEX idx_failure_reason (failure_reason),
                    INDEX idx_session_id (session_id)
                    
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
                COMMENT='排产失败批次记录表 - 增强版'
                """
                
                cursor.execute(create_table_sql)
                if not use_pool and connection:
                    connection.commit()
                logger.info("✅ 创建scheduling_failed_lots增强表成功（包含智能建议字段）")
            else:
                # 🔧 检查并添加缺失的字段（向后兼容）
                self._add_missing_columns(cursor, connection, use_pool)
                
        except Error as e:
            logger.error(f"检查/创建表时出错: {e}")
    
    def _add_missing_columns(self, cursor, connection=None, use_pool=False):
        """检查并添加缺失的列（向后兼容）"""
        try:
            # 检查suggestion字段是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots' 
                AND column_name = 'suggestion'
            """)
            
            result = cursor.fetchone()
            # 🔧 兼容不同类型的cursor结果格式
            if isinstance(result, dict):
                count_value = result.get('COUNT(*)', 0)
            elif isinstance(result, (tuple, list)):
                count_value = result[0]
            else:
                count_value = result
            suggestion_exists = count_value > 0
            
            if not suggestion_exists:
                logger.info("🔧 添加suggestion字段到现有表")
                cursor.execute("""
                    ALTER TABLE scheduling_failed_lots 
                    ADD COLUMN suggestion TEXT COMMENT '智能建议解决方案' 
                    AFTER failure_details
                """)
                if not use_pool and connection:
                    connection.commit()
                logger.info("✅ 成功添加suggestion字段")
            
            # 检查session_id字段是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots' 
                AND column_name = 'session_id'
            """)
            
            result = cursor.fetchone()
            # 🔧 兼容不同类型的cursor结果格式
            if isinstance(result, dict):
                count_value = result.get('COUNT(*)', 0)
            elif isinstance(result, (tuple, list)):
                count_value = result[0]
            else:
                count_value = result
            session_id_exists = count_value > 0
            
            if not session_id_exists:
                logger.info("🔧 添加session_id字段到现有表")
                cursor.execute("""
                    ALTER TABLE scheduling_failed_lots 
                    ADD COLUMN session_id VARCHAR(100) COMMENT '排产会话ID' 
                    AFTER algorithm_version
                """)
                # 添加索引
                cursor.execute("""
                    ALTER TABLE scheduling_failed_lots 
                    ADD INDEX idx_session_id (session_id)
                """)
                if not use_pool and connection:
                    connection.commit()
                logger.info("✅ 成功添加session_id字段和索引")
                
        except Error as e:
            logger.error(f"添加缺失字段时出错: {e}")
    
    def _cleanup_old_sessions(self, cursor, connection=None, use_pool=False):
        """清理过期的历史session记录，保留最近的session"""
        try:
            # 🔧 分步获取要保留的session列表
            # 1. 先获取最近10个session的ID
            cursor.execute("""
                SELECT session_id
                FROM (
                    SELECT session_id, MAX(timestamp) as latest_time
                    FROM scheduling_failed_lots 
                    WHERE session_id IS NOT NULL
                    GROUP BY session_id
                    ORDER BY latest_time DESC
                    LIMIT 10
                ) AS recent_sessions
            """)
            
            recent_sessions = [row[0] for row in cursor.fetchall()]
            
            if len(recent_sessions) > 0:
                # 2. 删除不在最近10个session中的记录
                placeholders = ', '.join(['%s'] * len(recent_sessions))
                cleanup_sql = f"""
                    DELETE FROM scheduling_failed_lots 
                    WHERE session_id IS NOT NULL
                    AND session_id NOT IN ({placeholders})
                """
                cursor.execute(cleanup_sql, recent_sessions)
                cleaned_count = cursor.rowcount
                
                if not use_pool and connection:
                    connection.commit()
                
                if cleaned_count > 0:
                    logger.info(f"🧹 清理了 {cleaned_count} 条过期的历史session记录，保留 {len(recent_sessions)} 个最新session")
            
        except Exception as e:
            logger.warning(f"⚠️ 清理历史session记录失败: {e}")
    
    def generate_stable_session_id(self, data_context: Dict = None) -> str:
        """生成稳定的Session ID，避免频繁变化"""
        import hashlib
        
        # 使用5分钟时间窗口，确保同一窗口内使用相同session
        current_time = datetime.now()
        time_window = current_time.replace(second=0, microsecond=0)
        minute = time_window.minute
        rounded_minute = (minute // 5) * 5
        time_window = time_window.replace(minute=rounded_minute)
        
        # 结合数据上下文hash（如果提供）
        if data_context:
            context_str = str(sorted(data_context.items()))
            context_hash = hashlib.md5(context_str.encode()).hexdigest()[:8]
            session_base = f"{time_window.strftime('%Y%m%d_%H%M')}_{context_hash}"
        else:
            session_base = time_window.strftime('%Y%m%d_%H%M')
        
        return f"stable_session_{session_base}"
    
    def should_skip_execution(self, execution_context: Dict) -> bool:
        """检查是否应该跳过重复执行"""
        import time
        import hashlib
        
        # 生成执行上下文的hash
        context_str = str(sorted(execution_context.items()))
        context_hash = hashlib.md5(context_str.encode()).hexdigest()[:8]
        
        current_time = time.time()
        
        # 检查是否在最小间隔内重复执行相同上下文
        if context_hash in self.execution_cache:
            last_time = self.execution_cache[context_hash]
            if current_time - last_time < self.min_execution_interval:
                time_remaining = self.min_execution_interval - (current_time - last_time)
                logger.warning(f"⚠️ 跳过重复执行，还需等待 {time_remaining:.1f} 秒")
                return True
        
        # 记录执行时间
        self.execution_cache[context_hash] = current_time
        return False

    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_lots = len(self.failed_lots) + len(self.success_lots)
        failed_lots = len(self.failed_lots)
        success_lots = len(self.success_lots)
        
        success_rate = (success_lots / total_lots * 100) if total_lots > 0 else 0
        
        # 统计失败原因
        failure_reasons = {}
        for lot in self.failed_lots:
            reason = lot['failure_reason']
            failure_reasons[reason] = failure_reasons.get(reason, 0) + 1
        
        return {
            'total_lots': total_lots,
            'failed_lots': failed_lots,
            'success_lots': success_lots,
            'success_rate': round(success_rate, 1),
            'failure_reasons': failure_reasons,
            'execution_cache_size': len(self.execution_cache),  # 🔥 新增
            'timestamp': datetime.now()
        }
    
    def get_failed_lots(self) -> List[Dict]:
        """获取失败批次列表"""
        return self.failed_lots.copy()
    
    def get_success_lots(self) -> List[Dict]:
        """获取成功批次列表"""
        return self.success_lots.copy()
    
    def clear(self):
        """清空记录"""
        self.failed_lots.clear()
        self.success_lots.clear()
        self.execution_cache.clear()  # 🔥 同时清空执行缓存
        logger.info("📋 已清空失败批次跟踪记录")
    
    def generate_stable_session_id(self, data_context: Dict = None) -> str:
        """生成稳定的Session ID，避免频繁变化"""
        import hashlib
        
        # 使用5分钟时间窗口，确保同一窗口内使用相同session
        current_time = datetime.now()
        time_window = current_time.replace(second=0, microsecond=0)
        minute = time_window.minute
        rounded_minute = (minute // 5) * 5
        time_window = time_window.replace(minute=rounded_minute)
        
        # 结合数据上下文hash（如果提供）
        if data_context:
            context_str = str(sorted(data_context.items()))
            context_hash = hashlib.md5(context_str.encode()).hexdigest()[:8]
            session_base = f"{time_window.strftime('%Y%m%d_%H%M')}_{context_hash}"
        else:
            session_base = time_window.strftime('%Y%m%d_%H%M')
        
        return f"stable_session_{session_base}"
    
    def should_skip_execution(self, execution_context: Dict) -> bool:
        """检查是否应该跳过重复执行"""
        import time
        import hashlib
        
        # 生成执行上下文的hash
        context_str = str(sorted(execution_context.items()))
        context_hash = hashlib.md5(context_str.encode()).hexdigest()[:8]
        
        current_time = time.time()
        
        # 检查是否在最小间隔内重复执行相同上下文
        if context_hash in self.execution_cache:
            last_time = self.execution_cache[context_hash]
            if current_time - last_time < self.min_execution_interval:
                time_remaining = self.min_execution_interval - (current_time - last_time)
                logger.warning(f"⚠️ 跳过重复执行，还需等待 {time_remaining:.1f} 秒")
                return True
        
        # 记录执行时间
        self.execution_cache[context_hash] = current_time
        return False
    
