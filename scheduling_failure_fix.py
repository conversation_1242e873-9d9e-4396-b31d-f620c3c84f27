#!/usr/bin/env python3
"""
排产失败跟踪器
用于记录和分析排产失败的批次信息
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime
import json
import logging
from typing import Dict, List, Optional

# 配置日志
logger = logging.getLogger(__name__)

class SchedulingFailureTracker:
    """排产失败跟踪器"""
    
    def __init__(self):
        self.failed_lots = []
        self.success_lots = []
        self.db_config = {
            'host': 'localhost',
            'database': 'aps',
            'user': 'root',
            'password': 'WWWwww123!'
        }
    
    def add_failed_lot(self, lot: Dict, failure_reason: str, failure_details: str = "", algorithm_version: str = "v2.0", session_id: str = None):
        """添加失败批次记录"""
        try:
            # 如果没有提供session_id，生成一个基于时间戳的ID
            if not session_id:
                session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            failed_lot = {
                'lot_id': lot.get('LOT_ID', ''),
                'device': lot.get('DEVICE', ''),
                'stage': lot.get('STAGE', ''),
                'good_qty': lot.get('GOOD_QTY', 0),
                'failure_reason': failure_reason,
                'failure_details': failure_details,
                'algorithm_version': algorithm_version,
                'session_id': session_id,  # 🔥 新增会话ID
                'execution_context': json.dumps({
                    'pkg_pn': lot.get('PKG_PN', ''),
                    'chip_id': lot.get('CHIP_ID', ''),
                    'lot_type': lot.get('LOT_TYPE', ''),
                    'wip_state': lot.get('WIP_STATE', ''),
                    'proc_state': lot.get('PROC_STATE', ''),
                    'create_time': lot.get('CREATE_TIME', ''),
                    'timestamp': datetime.now().isoformat(),
                    'session_id': session_id
                }),
                'timestamp': datetime.now()
            }
            self.failed_lots.append(failed_lot)
            logger.warning(f"❌ 批次排产失败: {failed_lot['lot_id']} - {failure_reason} (会话:{session_id})")
        except Exception as e:
            logger.error(f"记录失败批次时出错: {e}")
    
    def add_success_lot(self, lot: Dict, handler_id: str):
        """添加成功批次记录"""
        try:
            success_lot = {
                'lot_id': lot.get('LOT_ID', ''),
                'device': lot.get('DEVICE', ''),
                'stage': lot.get('STAGE', ''),
                'handler_id': handler_id,
                'timestamp': datetime.now()
            }
            self.success_lots.append(success_lot)
            logger.info(f"✅ 批次排产成功: {success_lot['lot_id']} -> {handler_id}")
        except Exception as e:
            logger.error(f"记录成功批次时出错: {e}")
    
    def save_to_database(self):
        """保存失败批次到数据库"""
        if not self.failed_lots:
            logger.info("ℹ️ 没有失败批次需要保存")
            return
        
        try:
            connection = mysql.connector.connect(**self.db_config)
            if connection.is_connected():
                cursor = connection.cursor()
                
                # 确保表存在
                self._ensure_table_exists(cursor)
                
                # 批量插入失败批次
                insert_sql = """
                INSERT INTO scheduling_failed_lots 
                (lot_id, device, stage, good_qty, failure_reason, failure_details, 
                 algorithm_version, session_id, execution_context, timestamp)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                
                failed_records = []
                for lot in self.failed_lots:
                    failed_records.append((
                        lot['lot_id'],
                        lot['device'],
                        lot['stage'],
                        lot['good_qty'],
                        lot['failure_reason'],
                        lot['failure_details'],
                        lot['algorithm_version'],
                        lot['session_id'],  # 🔥 新增会话ID
                        lot['execution_context'],
                        lot['timestamp']
                    ))
                
                cursor.executemany(insert_sql, failed_records)
                connection.commit()
                
                logger.info(f"✅ 已保存 {len(failed_records)} 条失败批次记录到数据库")
                
                cursor.close()
                connection.close()
                
        except Error as e:
            logger.error(f"保存失败批次到数据库时出错: {e}")
        except Exception as e:
            logger.error(f"保存失败批次时发生异常: {e}")
    
    def _ensure_table_exists(self, cursor):
        """确保scheduling_failed_lots表存在"""
        try:
            # 检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
            """)
            
            table_exists = cursor.fetchone()[0] > 0
            
            if not table_exists:
                # 创建表
                create_table_sql = """
                CREATE TABLE scheduling_failed_lots (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    lot_id VARCHAR(50) NOT NULL COMMENT '批次ID',
                    device VARCHAR(100) NOT NULL COMMENT '设备名称',
                    stage VARCHAR(50) NOT NULL COMMENT '工序',
                    good_qty INT DEFAULT 0 COMMENT '良品数量',
                    failure_reason VARCHAR(500) NOT NULL COMMENT '失败原因',
                    failure_details TEXT COMMENT '失败详情',
                    algorithm_version VARCHAR(50) COMMENT '算法版本',
                    execution_context JSON COMMENT '执行上下文',
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失败时间',
                    
                    INDEX idx_lot_id (lot_id),
                    INDEX idx_device (device),
                    INDEX idx_stage (stage),
                    INDEX idx_timestamp (timestamp),
                    INDEX idx_failure_reason (failure_reason)
                    
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
                COMMENT='排产失败批次记录表'
                """
                
                cursor.execute(create_table_sql)
                logger.info("✅ 创建scheduling_failed_lots表成功")
                
        except Error as e:
            logger.error(f"检查/创建表时出错: {e}")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_lots = len(self.failed_lots) + len(self.success_lots)
        failed_lots = len(self.failed_lots)
        success_lots = len(self.success_lots)
        
        success_rate = (success_lots / total_lots * 100) if total_lots > 0 else 0
        
        # 统计失败原因
        failure_reasons = {}
        for lot in self.failed_lots:
            reason = lot['failure_reason']
            failure_reasons[reason] = failure_reasons.get(reason, 0) + 1
        
        return {
            'total_lots': total_lots,
            'failed_lots': failed_lots,
            'success_lots': success_lots,
            'success_rate': round(success_rate, 1),
            'failure_reasons': failure_reasons
        }
    
    def get_failed_lots(self) -> List[Dict]:
        """获取失败批次列表"""
        return self.failed_lots.copy()
    
    def get_success_lots(self) -> List[Dict]:
        """获取成功批次列表"""
        return self.success_lots.copy()
    
    def clear(self):
        """清空记录"""
        self.failed_lots.clear()
        self.success_lots.clear()
        logger.info("📋 已清空失败批次跟踪记录")