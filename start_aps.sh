#!/bin/bash
# APS车规芯片终测智能调度平台启动脚本

echo "==============================================="
echo "APS车规芯片终测智能调度平台"
echo "==============================================="
echo

# 检查配置文件
if [ ! -f "config.ini" ]; then
    echo "错误：配置文件config.ini不存在"
    echo "请运行: python comprehensive_startup_fix.py"
    exit 1
fi

# 检查虚拟环境
if [ -d "venv" ]; then
    echo "激活虚拟环境..."
    source venv/bin/activate
fi

# 启动应用
echo "启动应用..."
python run.py

if [ $? -ne 0 ]; then
    echo
    echo "应用启动失败"
    echo "请运行: python comprehensive_startup_fix.py"
    read -p "按Enter键退出..."
fi
