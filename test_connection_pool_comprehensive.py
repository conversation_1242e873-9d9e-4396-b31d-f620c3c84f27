#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池完整测试方案 - 示例实现
基于新的测试规则框架，演示如何设计完整的测试方案

Author: AI Assistant
Date: 2025-01-16
Version: 1.0

此测试方案严格遵循README.md中的测试规则框架，覆盖所有真实使用场景
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

import threading
import time
import psutil
import pymysql
import logging
import concurrent.futures
import unittest
from datetime import datetime, timedelta
from contextlib import contextmanager
from unittest.mock import patch, MagicMock

# 导入被测试的模块
from app.utils.db_connection_pool import (
    DatabaseConnectionPool, 
    get_connection_pool,
    get_db_connection,
    get_db_connection_context,
    return_db_connection
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ComprehensiveConnectionPoolTest(unittest.TestCase):
    """
    数据库连接池完整测试套件
    
    遵循测试规则框架的六个维度：
    1. 基础功能测试维度
    2. 数据库连接池专项测试维度  
    3. 真实使用场景测试维度
    4. 性能测试维度
    5. 故障恢复测试维度
    6. 历史问题回归测试
    """
    
    @classmethod
    def setUpClass(cls):
        """测试套件初始化"""
        cls.pool = get_connection_pool()
        cls.test_start_time = time.time()
        cls.performance_metrics = {
            'connection_times': [],
            'query_times': [],
            'memory_usage': [],
            'cpu_usage': []
        }
        
        logger.info("🧪 开始数据库连接池完整测试套件")
        
    @classmethod
    def tearDownClass(cls):
        """测试套件清理"""
        test_duration = time.time() - cls.test_start_time
        logger.info(f"✅ 测试套件完成，总耗时: {test_duration:.2f}秒")
        
        # 输出性能报告
        cls._generate_performance_report()
        
    @classmethod
    def _generate_performance_report(cls):
        """生成性能测试报告"""
        report = {
            'test_summary': {
                'total_duration': time.time() - cls.test_start_time,
                'avg_connection_time': sum(cls.performance_metrics['connection_times']) / len(cls.performance_metrics['connection_times']) if cls.performance_metrics['connection_times'] else 0,
                'avg_query_time': sum(cls.performance_metrics['query_times']) / len(cls.performance_metrics['query_times']) if cls.performance_metrics['query_times'] else 0,
                'max_memory_usage': max(cls.performance_metrics['memory_usage']) if cls.performance_metrics['memory_usage'] else 0,
                'max_cpu_usage': max(cls.performance_metrics['cpu_usage']) if cls.performance_metrics['cpu_usage'] else 0
            }
        }
        
        logger.info("📊 性能测试报告:")
        for key, value in report['test_summary'].items():
            logger.info(f"   {key}: {value}")

    # =====================================
    # 1. 基础功能测试维度
    # =====================================
    
    def test_basic_connection_operations(self):
        """✅ 基础功能测试: 连接获取和释放"""
        logger.info("🔧 测试基础连接操作...")
        
        # 测试连接获取
        start_time = time.time()
        connection = self.pool.get_connection()
        connection_time = time.time() - start_time
        self.performance_metrics['connection_times'].append(connection_time)
        
        self.assertIsNotNone(connection, "连接应该成功获取")
        self.assertIsInstance(connection, pymysql.Connection, "应该返回正确的连接类型")
        
        # 测试连接有效性
        try:
            connection.ping()
            logger.info("✅ 连接有效性验证通过")
        except Exception as e:
            self.fail(f"连接无效: {e}")
        
        # 测试连接释放
        self.pool.return_connection(connection)
        logger.info("✅ 连接释放完成")
        
    def test_connection_pool_initialization(self):
        """✅ 基础功能测试: 连接池初始化"""
        logger.info("🔧 测试连接池初始化...")
        
        # 测试单例模式
        pool1 = get_connection_pool()
        pool2 = get_connection_pool()
        self.assertIs(pool1, pool2, "连接池应该是单例")
        
        # 测试统计信息
        stats = self.pool.get_stats()
        self.assertIn('connections_created', stats, "统计信息应该包含创建连接数")
        self.assertIn('connections_reused', stats, "统计信息应该包含复用连接数")
        
        logger.info("✅ 连接池初始化验证通过")
        
    def test_context_manager(self):
        """✅ 基础功能测试: 上下文管理器"""
        logger.info("🔧 测试上下文管理器...")
        
        # 测试连接上下文管理器
        with get_db_connection_context() as conn:
            self.assertIsNotNone(conn, "上下文管理器应该返回有效连接")
            conn.ping()  # 验证连接有效
        
        # 测试游标上下文管理器
        with self.pool.get_cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            self.assertEqual(result[0], 1, "查询结果应该正确")
            
        logger.info("✅ 上下文管理器验证通过")

    # =====================================
    # 2. 数据库连接池专项测试维度
    # =====================================
    
    def test_concurrent_connections(self):
        """✅ 连接池专项测试: 高并发连接"""
        logger.info("🚀 测试高并发连接场景...")
        
        concurrent_count = 100
        success_count = 0
        error_count = 0
        connection_times = []
        
        def get_and_release_connection():
            nonlocal success_count, error_count
            try:
                start_time = time.time()
                with get_db_connection_context() as conn:
                    conn.ping()
                    time.sleep(0.01)  # 模拟短暂的数据库操作
                connection_time = time.time() - start_time
                connection_times.append(connection_time)
                success_count += 1
            except Exception as e:
                error_count += 1
                logger.error(f"并发连接失败: {e}")
        
        # 执行并发测试
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrent_count) as executor:
            futures = [executor.submit(get_and_release_connection) for _ in range(concurrent_count)]
            concurrent.futures.wait(futures)
        
        # 验证结果
        self.assertEqual(error_count, 0, f"并发测试不应该有错误，但有 {error_count} 个错误")
        self.assertEqual(success_count, concurrent_count, f"应该有 {concurrent_count} 个成功连接")
        
        avg_time = sum(connection_times) / len(connection_times)
        self.assertLess(avg_time, 1.0, f"平均连接时间应该小于1秒，实际: {avg_time:.3f}秒")
        
        logger.info(f"✅ 并发测试完成: {success_count}/{concurrent_count} 成功，平均时间: {avg_time:.3f}秒")
        
    def test_connection_pool_limits(self):
        """✅ 连接池专项测试: 连接池大小限制"""
        logger.info("🔒 测试连接池大小限制...")
        
        connections = []
        max_connections = self.pool._max_connections
        
        try:
            # 尝试获取最大数量的连接
            for i in range(max_connections):
                conn = self.pool.get_connection()
                connections.append(conn)
                
            logger.info(f"✅ 成功获取 {len(connections)} 个连接")
            
            # 尝试获取超出限制的连接（应该等待或抛出异常）
            start_time = time.time()
            try:
                extra_conn = self.pool.get_connection()
                wait_time = time.time() - start_time
                
                # 如果成功获取，说明有连接被释放或者池子动态扩容了
                logger.info(f"获取额外连接成功，等待时间: {wait_time:.3f}秒")
                connections.append(extra_conn)
                
            except Exception as e:
                wait_time = time.time() - start_time
                logger.info(f"获取额外连接失败（符合预期），等待时间: {wait_time:.3f}秒")
                
        finally:
            # 释放所有连接
            for conn in connections:
                try:
                    self.pool.return_connection(conn)
                except:
                    pass
                    
        logger.info("✅ 连接池限制测试完成")
        
    def test_connection_health_check(self):
        """✅ 连接池专项测试: 连接健康检查"""
        logger.info("🏥 测试连接健康检查...")
        
        # 获取一个连接
        conn = self.pool.get_connection()
        
        # 验证连接健康
        try:
            conn.ping()
            logger.info("✅ 连接健康检查通过")
        except Exception as e:
            self.fail(f"连接健康检查失败: {e}")
        
        # 模拟连接失效（关闭连接）
        conn.close()
        
        # 尝试ping失效的连接
        with self.assertRaises(Exception):
            conn.ping()
            
        logger.info("✅ 失效连接检测正常")
        
    def test_long_running_stability(self):
        """✅ 连接池专项测试: 长期运行稳定性（模拟）"""
        logger.info("⏱️ 测试长期运行稳定性（快速模拟）...")
        
        # 模拟长期运行场景：持续获取和释放连接
        duration = 30  # 30秒的快速测试
        start_time = time.time()
        operation_count = 0
        error_count = 0
        
        while time.time() - start_time < duration:
            try:
                with get_db_connection_context() as conn:
                    conn.ping()
                    operation_count += 1
                    
                # 模拟真实操作间隔
                time.sleep(0.1)
                
            except Exception as e:
                error_count += 1
                logger.error(f"长期运行测试中的错误: {e}")
                
        # 验证稳定性
        error_rate = error_count / operation_count if operation_count > 0 else 1
        self.assertLess(error_rate, 0.01, f"错误率应该小于1%，实际: {error_rate:.2%}")
        
        logger.info(f"✅ 长期稳定性测试完成: {operation_count} 次操作，错误率: {error_rate:.2%}")

    # =====================================
    # 3. 真实使用场景测试维度
    # =====================================
    
    def test_realistic_data_volumes(self):
        """✅ 真实场景测试: 真实数据量模拟"""
        logger.info("📊 测试真实数据量场景...")
        
        # 模拟不同数据量的查询
        test_cases = [
            {'name': '小数据量', 'limit': 50, 'expected_time': 0.5},
            {'name': '中等数据量', 'limit': 1000, 'expected_time': 2.0},
            {'name': '大数据量', 'limit': 5000, 'expected_time': 5.0}
        ]
        
        for case in test_cases:
            start_time = time.time()
            
            try:
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    
                    # 模拟复杂查询（如果表存在的话）
                    try:
                        cursor.execute(f"SELECT * FROM et_wait_lot LIMIT {case['limit']}")
                        results = cursor.fetchall()
                        query_time = time.time() - start_time
                        
                        self.performance_metrics['query_times'].append(query_time)
                        
                        logger.info(f"✅ {case['name']}测试: {len(results)}条记录，耗时: {query_time:.3f}秒")
                        
                        # 验证性能要求
                        if query_time > case['expected_time']:
                            logger.warning(f"⚠️ {case['name']}查询超时: {query_time:.3f}s > {case['expected_time']}s")
                            
                    except pymysql.Error as e:
                        if "doesn't exist" in str(e):
                            logger.info(f"📝 表不存在，跳过 {case['name']} 测试")
                        else:
                            raise
                            
            except Exception as e:
                logger.error(f"❌ {case['name']}测试失败: {e}")
                
    def test_realistic_user_behavior(self):
        """✅ 真实场景测试: 用户行为模拟"""
        logger.info("👤 测试真实用户行为场景...")
        
        # 模拟用户行为：频繁的小查询
        def simulate_user_session():
            """模拟一个用户会话"""
            session_operations = 0
            session_errors = 0
            
            for _ in range(10):  # 每个用户做10次操作
                try:
                    with get_db_connection_context() as conn:
                        cursor = conn.cursor()
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                        session_operations += 1
                        
                    # 模拟用户思考时间
                    time.sleep(0.05)
                    
                except Exception as e:
                    session_errors += 1
                    logger.error(f"用户会话操作失败: {e}")
                    
            return session_operations, session_errors
        
        # 模拟多个并发用户
        user_count = 20
        total_operations = 0
        total_errors = 0
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=user_count) as executor:
            futures = [executor.submit(simulate_user_session) for _ in range(user_count)]
            
            for future in concurrent.futures.as_completed(futures):
                operations, errors = future.result()
                total_operations += operations
                total_errors += errors
        
        # 验证用户体验
        error_rate = total_errors / total_operations if total_operations > 0 else 1
        self.assertLess(error_rate, 0.05, f"用户操作错误率应该小于5%，实际: {error_rate:.2%}")
        
        logger.info(f"✅ 用户行为测试完成: {user_count}用户，{total_operations}操作，错误率: {error_rate:.2%}")
        
    def test_complete_business_scenarios(self):
        """✅ 真实场景测试: 完整业务场景"""
        logger.info("🎯 测试完整业务场景...")
        
        # 模拟完整的排产流程
        business_scenarios = [
            "数据查询阶段",
            "数据处理阶段", 
            "结果保存阶段",
            "历史记录阶段"
        ]
        
        for scenario in business_scenarios:
            start_time = time.time()
            
            try:
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    
                    # 模拟不同阶段的数据库操作
                    if "查询" in scenario:
                        cursor.execute("SELECT 1 as test_query")
                    elif "处理" in scenario:
                        cursor.execute("SELECT 1 as test_processing")
                    elif "保存" in scenario:
                        # 模拟事务操作
                        conn.begin()
                        cursor.execute("SELECT 1 as test_save")
                        conn.commit()
                    elif "历史" in scenario:
                        cursor.execute("SELECT 1 as test_history")
                        
                    result = cursor.fetchone()
                    scenario_time = time.time() - start_time
                    
                    self.assertIsNotNone(result, f"{scenario}应该返回结果")
                    logger.info(f"✅ {scenario}完成，耗时: {scenario_time:.3f}秒")
                    
            except Exception as e:
                logger.error(f"❌ {scenario}失败: {e}")
                raise

    # =====================================
    # 4. 性能测试维度
    # =====================================
    
    def test_response_time_requirements(self):
        """✅ 性能测试: 响应时间要求"""
        logger.info("⚡ 测试响应时间要求...")
        
        # 测试连接获取时间
        connection_times = []
        for _ in range(50):
            start_time = time.time()
            with get_db_connection_context() as conn:
                conn.ping()
            connection_time = time.time() - start_time
            connection_times.append(connection_time)
            
        avg_connection_time = sum(connection_times) / len(connection_times)
        max_connection_time = max(connection_times)
        
        # 验证性能要求
        self.assertLess(avg_connection_time, 0.1, f"平均连接时间应该<100ms，实际: {avg_connection_time*1000:.1f}ms")
        self.assertLess(max_connection_time, 0.5, f"最大连接时间应该<500ms，实际: {max_connection_time*1000:.1f}ms")
        
        logger.info(f"✅ 响应时间测试通过: 平均{avg_connection_time*1000:.1f}ms，最大{max_connection_time*1000:.1f}ms")
        
    def test_system_resource_usage(self):
        """✅ 性能测试: 系统资源使用"""
        logger.info("📈 测试系统资源使用...")
        
        # 获取当前进程信息
        process = psutil.Process()
        
        # 记录测试前的资源使用
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu = process.cpu_percent()
        
        # 执行一些数据库操作
        connections = []
        try:
            for _ in range(20):
                conn = self.pool.get_connection()
                connections.append(conn)
                
            # 记录峰值资源使用
            peak_memory = process.memory_info().rss / 1024 / 1024  # MB
            peak_cpu = process.cpu_percent(interval=1)
            
            self.performance_metrics['memory_usage'].append(peak_memory)
            self.performance_metrics['cpu_usage'].append(peak_cpu)
            
            # 验证资源使用合理性
            memory_increase = peak_memory - initial_memory
            self.assertLess(memory_increase, 100, f"内存增长应该<100MB，实际: {memory_increase:.1f}MB")
            
            logger.info(f"✅ 资源使用测试: 内存{peak_memory:.1f}MB (+{memory_increase:.1f}MB), CPU{peak_cpu:.1f}%")
            
        finally:
            # 释放连接
            for conn in connections:
                try:
                    self.pool.return_connection(conn)
                except:
                    pass

    # =====================================
    # 5. 故障恢复测试维度  
    # =====================================
    
    def test_database_service_failure_simulation(self):
        """✅ 故障恢复测试: 数据库服务故障模拟"""
        logger.info("🚨 测试数据库服务故障恢复...")
        
        # 注意：这是模拟测试，在真实环境中需要实际的故障注入
        
        # 模拟连接超时
        original_timeout = self.pool._connection_timeout
        self.pool._connection_timeout = 1  # 设置很短的超时时间
        
        try:
            # 使用mock模拟数据库连接失败
            with patch('pymysql.connect') as mock_connect:
                mock_connect.side_effect = pymysql.OperationalError("Connection failed")
                
                # 尝试获取连接，应该失败
                with self.assertRaises(Exception):
                    self.pool.get_connection()
                    
                logger.info("✅ 数据库故障检测正常")
                
            # 恢复正常连接
            mock_connect.stop()
            
            # 验证恢复后能正常工作
            with get_db_connection_context() as conn:
                conn.ping()
                
            logger.info("✅ 数据库故障恢复正常")
            
        finally:
            # 恢复原始超时设置
            self.pool._connection_timeout = original_timeout
            
    def test_data_consistency_under_failure(self):
        """✅ 故障恢复测试: 故障下的数据一致性"""
        logger.info("🔒 测试故障下的数据一致性...")
        
        # 模拟事务失败场景
        try:
            with get_db_connection_context() as conn:
                cursor = conn.cursor()
                
                # 开始事务
                conn.begin()
                
                try:
                    # 执行一些操作
                    cursor.execute("SELECT 1")
                    
                    # 模拟操作中的错误
                    raise Exception("模拟的业务错误")
                    
                except Exception as e:
                    # 确保事务被回滚
                    conn.rollback()
                    logger.info("✅ 事务回滚正常")
                    
                # 验证连接仍然可用
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                self.assertEqual(result[0], 1, "连接应该在错误后仍然可用")
                
        except Exception as e:
            logger.error(f"数据一致性测试失败: {e}")
            raise
            
        logger.info("✅ 数据一致性测试通过")

    # =====================================
    # 6. 历史问题回归测试
    # =====================================
    
    def test_connection_leak_regression(self):
        """✅ 历史问题回归: 连接泄漏问题"""
        logger.info("🔍 测试连接泄漏回归...")
        
        initial_stats = self.pool.get_stats()
        initial_connections = sum(pool_stats.get('active_connections', 0) 
                                for pool_stats in initial_stats.get('pools', {}).values())
        
        # 执行大量连接操作
        for _ in range(100):
            try:
                with get_db_connection_context() as conn:
                    conn.ping()
            except Exception as e:
                logger.warning(f"连接操作中的错误: {e}")
        
        # 等待一段时间让连接池清理
        time.sleep(2)
        
        final_stats = self.pool.get_stats()
        final_connections = sum(pool_stats.get('active_connections', 0) 
                              for pool_stats in final_stats.get('pools', {}).values())
        
        # 验证没有连接泄漏
        connection_diff = final_connections - initial_connections
        self.assertLessEqual(abs(connection_diff), 5, f"连接数变化应该很小，实际变化: {connection_diff}")
        
        logger.info(f"✅ 连接泄漏回归测试通过: 连接数变化 {connection_diff}")
        
    def test_concurrent_access_regression(self):
        """✅ 历史问题回归: 并发访问问题"""
        logger.info("🔍 测试并发访问回归...")
        
        # 基于历史问题：并发访问时的数据不一致
        shared_counter = {'value': 0}
        lock = threading.Lock()
        
        def concurrent_operation():
            """模拟并发数据库操作"""
            try:
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    
                    # 模拟基于查询结果的计算
                    with lock:
                        shared_counter['value'] += result[0]
                        
            except Exception as e:
                logger.error(f"并发操作错误: {e}")
        
        # 执行并发操作
        thread_count = 50
        threads = []
        
        for _ in range(thread_count):
            thread = threading.Thread(target=concurrent_operation)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证结果一致性
        expected_value = thread_count * 1  # 每个线程贡献1
        actual_value = shared_counter['value']
        
        self.assertEqual(actual_value, expected_value, 
                        f"并发操作结果应该一致: 期望{expected_value}, 实际{actual_value}")
        
        logger.info(f"✅ 并发访问回归测试通过: {actual_value}/{expected_value}")

    def test_memory_leak_regression(self):
        """✅ 历史问题回归: 内存泄漏问题"""
        logger.info("🔍 测试内存泄漏回归...")
        
        # 获取初始内存使用
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行大量数据库操作
        operation_count = 1000
        for i in range(operation_count):
            try:
                with get_db_connection_context() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    
                # 每100次操作检查一次内存
                if i % 100 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    memory_growth = current_memory - initial_memory
                    
                    # 如果内存增长超过100MB，可能有泄漏
                    if memory_growth > 100:
                        logger.warning(f"⚠️ 内存使用异常增长: {memory_growth:.1f}MB")
                        
            except Exception as e:
                logger.error(f"内存泄漏测试中的错误: {e}")
        
        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024
        total_growth = final_memory - initial_memory
        
        # 验证内存使用合理
        self.assertLess(total_growth, 200, f"内存增长应该<200MB，实际: {total_growth:.1f}MB")
        
        logger.info(f"✅ 内存泄漏回归测试通过: 内存增长 {total_growth:.1f}MB")


def run_comprehensive_test_suite():
    """运行完整的测试套件"""
    
    print("🧪 数据库连接池完整测试套件")
    print("=" * 80)
    print("遵循README.md中定义的测试规则框架")
    print("覆盖6个测试维度 × 真实使用场景")
    print("=" * 80)
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加所有测试方法
    test_methods = [
        # 基础功能测试
        'test_basic_connection_operations',
        'test_connection_pool_initialization', 
        'test_context_manager',
        
        # 连接池专项测试
        'test_concurrent_connections',
        'test_connection_pool_limits',
        'test_connection_health_check',
        'test_long_running_stability',
        
        # 真实场景测试
        'test_realistic_data_volumes',
        'test_realistic_user_behavior',
        'test_complete_business_scenarios',
        
        # 性能测试
        'test_response_time_requirements',
        'test_system_resource_usage',
        
        # 故障恢复测试
        'test_database_service_failure_simulation',
        'test_data_consistency_under_failure',
        
        # 历史问题回归测试
        'test_connection_leak_regression',
        'test_concurrent_access_regression',
        'test_memory_leak_regression'
    ]
    
    for method in test_methods:
        suite.addTest(ComprehensiveConnectionPoolTest(method))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试总结
    print("\n" + "=" * 80)
    print("🎯 测试总结")
    print("=" * 80)
    print(f"总测试数: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ 失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n⚠️ 错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n🎉 所有测试通过！数据库连接池功能完全正常。")
        return 0
    else:
        print("\n❌ 测试发现问题，请检查并修复。")
        return 1


if __name__ == '__main__':
    """
    使用示例:
    
    # 运行完整测试套件
    python test_connection_pool_comprehensive.py
    
    # 运行单个测试类
    python -m unittest ComprehensiveConnectionPoolTest.test_concurrent_connections
    
    # 运行特定维度的测试
    python -m unittest ComprehensiveConnectionPoolTest.test_realistic_data_volumes
    """
    
    import sys
    
    # 检查是否在合适的环境中运行
    try:
        from app.utils.db_connection_pool import get_connection_pool
        exit_code = run_comprehensive_test_suite()
        sys.exit(exit_code)
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保在项目根目录运行此测试，且所有依赖已安装")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试运行错误: {e}")
        sys.exit(1)
