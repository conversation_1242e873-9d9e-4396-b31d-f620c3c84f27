#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EXE环境日志配置测试脚本
用于验证exe打包后的日志功能是否正常
"""

import sys
import os
import logging
from datetime import datetime
from pathlib import Path

def test_exe_logging():
    """测试exe环境的日志配置"""
    
    print("=" * 60)
    print("🧪 EXE环境日志配置测试")
    print("=" * 60)
    
    # 检测运行环境
    is_exe = getattr(sys, 'frozen', False)
    print(f"运行环境: {'EXE打包环境' if is_exe else '开发环境'}")
    
    if is_exe:
        exe_dir = os.path.dirname(sys.executable)
        print(f"EXE文件目录: {exe_dir}")
        
        # 创建logs目录
        logs_dir = os.path.join(exe_dir, 'logs')
        os.makedirs(logs_dir, exist_ok=True)
        print(f"日志目录: {logs_dir}")
        
        # 测试日志文件创建
        test_log_file = os.path.join(logs_dir, 'test.log')
        
        try:
            # 配置测试日志器
            test_logger = logging.getLogger('test_exe_logger')
            test_logger.setLevel(logging.INFO)
            
            # 清除现有处理器
            for handler in test_logger.handlers[:]:
                test_logger.removeHandler(handler)
            
            # 创建文件处理器
            file_handler = logging.FileHandler(test_log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # 创建控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.INFO)
            
            # 设置格式
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(message)s',
                datefmt='%H:%M:%S'
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加处理器
            test_logger.addHandler(file_handler)
            test_logger.addHandler(console_handler)
            
            # 写入测试日志
            test_logger.info("🧪 EXE环境日志测试开始")
            test_logger.info(f"📁 EXE目录: {exe_dir}")
            test_logger.info(f"📝 日志文件: {test_log_file}")
            test_logger.warning("⚠️ 测试警告消息")
            test_logger.error("❌ 测试错误消息")
            test_logger.info("✅ EXE环境日志测试完成")
            
            # 检查文件是否创建成功
            if os.path.exists(test_log_file):
                file_size = os.path.getsize(test_log_file)
                print(f"✅ 测试日志文件创建成功: {test_log_file}")
                print(f"📊 文件大小: {file_size} 字节")
                
                # 读取并显示文件内容
                print("\n📄 日志文件内容:")
                print("-" * 40)
                with open(test_log_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(content)
                print("-" * 40)
                
                return True
            else:
                print(f"❌ 测试日志文件创建失败: {test_log_file}")
                return False
                
        except Exception as e:
            print(f"❌ 日志测试失败: {e}")
            return False
    else:
        print("📝 开发环境下的日志测试")
        # 开发环境测试
        logs_dir = "logs"
        os.makedirs(logs_dir, exist_ok=True)
        
        test_log_file = os.path.join(logs_dir, 'dev_test.log')
        
        # 简单的开发环境测试
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s | %(levelname)-8s | %(message)s',
            datefmt='%H:%M:%S',
            handlers=[
                logging.FileHandler(test_log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        logging.info("🧪 开发环境日志测试")
        logging.info(f"📝 日志文件: {test_log_file}")
        
        return os.path.exists(test_log_file)

def check_current_app_logs():
    """检查当前应用的日志文件"""
    
    print("\n" + "=" * 60)
    print("📋 当前应用日志文件检查")
    print("=" * 60)
    
    is_exe = getattr(sys, 'frozen', False)
    
    if is_exe:
        exe_dir = os.path.dirname(sys.executable)
        app_logs_dir = os.path.join(exe_dir, 'logs')
        app_log_file = os.path.join(app_logs_dir, 'app.log')
    else:
        app_logs_dir = "logs"
        app_log_file = os.path.join(app_logs_dir, 'app.log')
    
    print(f"应用日志目录: {app_logs_dir}")
    print(f"应用日志文件: {app_log_file}")
    
    # 检查目录
    if os.path.exists(app_logs_dir):
        print(f"✅ 日志目录存在")
        
        # 列出所有日志文件
        log_files = [f for f in os.listdir(app_logs_dir) if f.endswith('.log')]
        if log_files:
            print(f"📁 发现 {len(log_files)} 个日志文件:")
            for log_file in log_files:
                file_path = os.path.join(app_logs_dir, log_file)
                size = os.path.getsize(file_path)
                mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"   - {log_file} ({size} 字节, 修改于: {mtime.strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            print("📂 日志目录为空")
    else:
        print(f"❌ 日志目录不存在: {app_logs_dir}")
    
    # 检查主应用日志文件
    if os.path.exists(app_log_file):
        size = os.path.getsize(app_log_file)
        mtime = datetime.fromtimestamp(os.path.getmtime(app_log_file))
        print(f"✅ 应用日志文件存在: {size} 字节, 修改于: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 显示最后几行日志
        try:
            with open(app_log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"\n📄 最后 5 行日志内容:")
                    print("-" * 40)
                    for line in lines[-5:]:
                        print(line.rstrip())
                    print("-" * 40)
                else:
                    print("📄 日志文件为空")
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print(f"❌ 应用日志文件不存在: {app_log_file}")

if __name__ == '__main__':
    # 执行测试
    success = test_exe_logging()
    
    # 检查当前应用日志
    check_current_app_logs()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 日志测试完成 - 结果: 成功")
    else:
        print("❌ 日志测试完成 - 结果: 失败")
    print("=" * 60)
    
    input("\n按回车键退出...") 