#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 简化版排产一致性测试脚本

直接连接数据库进行测试，验证排产结果的一致性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import hashlib
import json
import time
import pymysql
from datetime import datetime
from typing import List, Dict, Any

def get_test_data_from_db():
    """直接从数据库获取测试数据"""
    try:
        # 数据库连接参数
        db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': 'WWWwww123!',
            'database': 'aps',
            'charset': 'utf8mb4'
        }
        
        print("📊 正在连接数据库...")
        connection = pymysql.connect(**db_config)
        
        with connection.cursor(pymysql.cursors.DictCursor) as cursor:
            # 获取待排产批次
            cursor.execute("""
                SELECT LOT_ID, LOT_TYPE, GOOD_QTY, PROD_ID, DEVICE, CHIP_ID, PKG_PN,
                       STAGE, WIP_STATE, PROC_STATE, HOLD_STATE, FAC_ID, CREATE_TIME
                FROM ET_WAIT_LOT 
                WHERE WIP_STATE = 'WAIT'
                LIMIT 10
            """)
            wait_lots = cursor.fetchall()
            
            # 获取设备状态
            cursor.execute("""
                SELECT HANDLER_ID, HANDLER_TYPE, DEVICE, STATUS, EQP_TYPE, STAGE
                FROM EQP_STATUS 
                WHERE STATUS IN ('Run', 'IDLE', 'Wait', 'READY', 'ONLINE')
                LIMIT 20
            """)
            equipment = cursor.fetchall()
            
        connection.close()
        
        print(f"✅ 获取到 {len(wait_lots)} 个待排产批次")
        print(f"✅ 获取到 {len(equipment)} 台可用设备")
        
        return wait_lots, equipment
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return [], []

def simulate_scheduling_algorithm(wait_lots, equipment):
    """模拟排产算法"""
    if not wait_lots or not equipment:
        return []
    
    scheduled_results = []
    
    # 确定性排序 - 使用稳定排序键
    sorted_lots = sorted(wait_lots, key=lambda lot: (
        -lot.get('GOOD_QTY', 0),      # 主要排序键：数量
        lot.get('LOT_ID', ''),        # 稳定键1：批次ID
        lot.get('CREATE_TIME', ''),   # 稳定键2：创建时间
        lot.get('DEVICE', ''),        # 稳定键3：产品名称
    ))
    
    # 确定性设备排序
    sorted_equipment = sorted(equipment, key=lambda eq: (
        eq.get('HANDLER_ID', ''),     # 稳定键1：设备ID
        eq.get('EQP_TYPE', ''),       # 稳定键2：设备类型
        eq.get('STATUS', ''),         # 稳定键3：状态
    ))
    
    # 简单的Round-Robin分配算法
    equipment_idx = 0
    for i, lot in enumerate(sorted_lots):
        # 选择设备（循环分配）
        selected_equipment = sorted_equipment[equipment_idx % len(sorted_equipment)]
        equipment_idx += 1
        
        # 计算确定性评分
        base_score = 100 - (i * 5)  # 基础分数随序号递减
        priority_score = lot.get('GOOD_QTY', 0) * 0.1  # 数量影响
        comprehensive_score = round(base_score + priority_score, 2)
        
        result = {
            'LOT_ID': lot.get('LOT_ID', ''),
            'HANDLER_ID': selected_equipment.get('HANDLER_ID', ''),
            'PRIORITY': i + 1,
            'EXECUTION_PRIORITY': i + 1,
            'COMPREHENSIVE_SCORE': comprehensive_score,
            'DEVICE': lot.get('DEVICE', ''),
            'STATUS': '已排产',
            'SCHEDULED_TIME': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        scheduled_results.append(result)
    
    return scheduled_results

def test_deterministic_sorting():
    """测试确定性排序"""
    print("🔧 测试确定性排序...")
    
    # 模拟相同分数的数据
    test_data = [
        {'LOT_ID': 'LOT001', 'SCORE': 100.0, 'CREATE_TIME': '2024-01-01 10:00:00'},
        {'LOT_ID': 'LOT003', 'SCORE': 100.0, 'CREATE_TIME': '2024-01-01 12:00:00'},  
        {'LOT_ID': 'LOT002', 'SCORE': 100.0, 'CREATE_TIME': '2024-01-01 11:00:00'},
        {'LOT_ID': 'LOT005', 'SCORE': 90.0, 'CREATE_TIME': '2024-01-01 13:00:00'},
        {'LOT_ID': 'LOT004', 'SCORE': 90.0, 'CREATE_TIME': '2024-01-01 14:00:00'},
    ]
    
    # 多次排序测试
    sort_results = []
    for i in range(5):
        # 使用确定性排序
        sorted_data = sorted(test_data, key=lambda x: (
            -x['SCORE'],           # 主要排序键
            x['LOT_ID'],           # 稳定键1
            x['CREATE_TIME'],      # 稳定键2
        ))
        
        lot_order = [item['LOT_ID'] for item in sorted_data]
        sort_results.append(lot_order)
    
    # 检查排序一致性
    if len(set(tuple(result) for result in sort_results)) == 1:
        print("✅ 确定性排序测试通过")
        print(f"✅ 稳定排序结果: {sort_results[0]}")
        return True
    else:
        print("❌ 确定性排序测试失败")
        for i, result in enumerate(sort_results):
            print(f"   第{i+1}次: {result}")
        return False

def test_scheduling_consistency():
    """测试排产一致性"""
    print("🧪 开始排产一致性测试...")
    print("="*60)
    
    # 获取测试数据
    wait_lots, equipment = get_test_data_from_db()
    
    if not wait_lots:
        print("⚠️ 没有获取到测试数据，使用模拟数据")
        # 使用模拟数据
        wait_lots = [
            {'LOT_ID': f'TEST_LOT_{i:03d}', 'GOOD_QTY': 1000-i*10, 'DEVICE': f'DEVICE_{i%3}', 
             'CREATE_TIME': f'2024-01-{1+i:02d} 10:00:00', 'STAGE': 'FT'} 
            for i in range(10)
        ]
        equipment = [
            {'HANDLER_ID': f'EQP_{i:03d}', 'EQP_TYPE': 'TESTER', 'STATUS': 'IDLE', 'STAGE': 'FT'}
            for i in range(5)
        ]
        print(f"📋 使用模拟数据: {len(wait_lots)} 个批次, {len(equipment)} 台设备")
    
    # 进行多次排产测试
    results = []
    test_rounds = 3
    
    for i in range(test_rounds):
        print(f"\n🔄 第{i+1}次排产测试...")
        
        try:
            start_time = time.time()
            
            # 执行模拟排产算法
            result = simulate_scheduling_algorithm(wait_lots, equipment)
            
            execution_time = time.time() - start_time
            
            if result:
                # 提取关键信息用于比较
                comparison_data = []
                for lot_result in result:
                    comparison_data.append({
                        'LOT_ID': lot_result.get('LOT_ID', ''),
                        'HANDLER_ID': lot_result.get('HANDLER_ID', ''),
                        'PRIORITY': lot_result.get('PRIORITY', 0),
                        'EXECUTION_PRIORITY': lot_result.get('EXECUTION_PRIORITY', 0),
                        'COMPREHENSIVE_SCORE': lot_result.get('COMPREHENSIVE_SCORE', 0),
                    })
                
                # 计算结果哈希值
                result_str = json.dumps(comparison_data, sort_keys=True, ensure_ascii=False)
                result_hash = hashlib.md5(result_str.encode('utf-8')).hexdigest()
                
                results.append({
                    'round': i+1,
                    'hash': result_hash,
                    'count': len(result),
                    'execution_time': round(execution_time, 4),
                    'first_lot': result[0].get('LOT_ID', '') if result else '',
                    'first_handler': result[0].get('HANDLER_ID', '') if result else '',
                    'data': comparison_data[:5]  # 保存前5个用于详细比较
                })
                
                print(f"   ✅ 完成: {len(result)}个批次, 用时{execution_time:.4f}s")
                print(f"   📊 哈希: {result_hash[:16]}...")
                
            else:
                print(f"   ❌ 排产结果为空")
                results.append({
                    'round': i+1,
                    'hash': 'EMPTY',
                    'count': 0,
                    'execution_time': execution_time,
                    'first_lot': '',
                    'first_handler': '',
                    'data': []
                })
                
        except Exception as e:
            print(f"   ❌ 排产失败: {e}")
            results.append({
                'round': i+1,
                'hash': 'ERROR',
                'count': 0,
                'execution_time': 0,
                'first_lot': '',
                'first_handler': '',
                'error': str(e),
                'data': []
            })
    
    # 分析结果
    print(f"\n📊 测试结果分析:")
    print("-"*60)
    
    for result in results:
        status = "✅" if result['hash'] not in ['ERROR', 'EMPTY'] else "❌"
        print(f"{status} 第{result['round']}次:")
        print(f"     哈希: {result['hash'][:20]}...")
        print(f"     批次数: {result['count']}")
        print(f"     用时: {result['execution_time']}s")
        if result.get('first_lot'):
            print(f"     首批次: {result['first_lot']} -> {result['first_handler']}")
    
    # 检查一致性
    print(f"\n🎯 一致性分析:")
    print("-"*60)
    
    valid_hashes = [r['hash'] for r in results if r['hash'] not in ['ERROR', 'EMPTY']]
    
    if len(valid_hashes) == 0:
        print("❌ 所有测试都失败了")
        return False
    elif len(set(valid_hashes)) == 1:
        print("✅ 所有有效测试的排产结果完全一致！")
        print(f"✅ 一致性哈希: {valid_hashes[0]}")
        
        # 显示详细数据验证
        if results[0]['data']:
            print(f"\n📋 前5个批次详细信息:")
            for i, lot in enumerate(results[0]['data']):
                print(f"  {i+1}. {lot['LOT_ID']} -> {lot['HANDLER_ID']} (优先级:{lot['PRIORITY']}, 分数:{lot['COMPREHENSIVE_SCORE']})")
        
        return True
    else:
        print("❌ 排产结果不一致！")
        print(f"❌ 发现 {len(set(valid_hashes))} 种不同的结果")
        
        # 详细比较差异
        if len(results) >= 2 and results[0]['data'] and results[1]['data']:
            print(f"\n🔍 详细差异分析（前5个批次）:")
            for i in range(min(5, len(results[0]['data']), len(results[1]['data']))):
                lot1 = results[0]['data'][i]
                lot2 = results[1]['data'][i]
                if lot1 != lot2:
                    print(f"  差异 {i+1}:")
                    print(f"    第1次: {lot1}")
                    print(f"    第2次: {lot2}")
        
        return False

def main():
    """主测试函数"""
    print("🚀 简化版排产一致性测试套件")
    print("="*60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print("="*60)
    
    # 测试1：确定性排序
    sort_success = test_deterministic_sorting()
    
    # 测试2：排产一致性
    consistency_success = test_scheduling_consistency()
    
    # 总结
    print("\n" + "="*60)
    print("🏁 测试总结")
    print("="*60)
    
    if sort_success:
        print("✅ 确定性排序: 通过")
    else:
        print("❌ 确定性排序: 失败")
    
    if consistency_success:
        print("✅ 排产一致性: 通过")
    else:
        print("❌ 排产一致性: 失败")
    
    overall_success = sort_success and consistency_success
    
    if overall_success:
        print("\n🎉 所有测试通过！排产结果已具备一致性")
        print("💡 建议：可以在不同性能的电脑上运行此测试进行验证")
        print("💡 修复方案验证：确定性排序和禁用并行计算生效")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
        print("💡 建议：检查排序逻辑和数据库连接")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)