#!/usr/bin/env python3
"""
Excel数据导入到MySQL数据库工具 - 字段卡控安全版本
专门针对排产系统，严格保护表结构，防止业务逻辑断裂

Author: AI Assistant  
Date: 2025-01-16
Version: 2.0 - 字段卡控版本
"""

import os
import sys
import pandas as pd
import pymysql
import json
import time
from datetime import datetime
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('import_mysql_safe.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ================================
# 业务表字段映射配置 - 排产核心表保护
# ================================

# 严格的表字段定义 - 基于数据库实际结构
BUSINESS_TABLE_SCHEMAS = {
    'ct': {
        'required_fields': ['LOT_ID', 'DEVICE', 'STAGE'],
        'field_mapping': {
            # Excel表头 -> 数据库字段
            'LOT_ID': 'LOT_ID',
            'DEVICE': 'DEVICE', 
            'STAGE': 'STAGE',
            'CHIP_ID': 'CHIP_ID',
            'PKG_PN': 'PKG_PN',
            'STEP': 'STEP',
            'START_TIME': 'START_TIME',
            'END_TIME': 'END_TIME',
            'QTY': 'QTY',
            'YIELD_RATE': 'YIELD_RATE',
            'HANDLER_ID': 'HANDLER_ID',
            'TESTER_ID': 'TESTER_ID'
        },
        'field_types': {
            'LOT_ID': 'VARCHAR(50)',
            'DEVICE': 'VARCHAR(50)',
            'STAGE': 'VARCHAR(50)',
            'CHIP_ID': 'VARCHAR(50)',
            'PKG_PN': 'VARCHAR(50)',
            'STEP': 'VARCHAR(50)',
            'START_TIME': 'DATETIME',
            'END_TIME': 'DATETIME',
            'QTY': 'INT',
            'YIELD_RATE': 'DECIMAL(5,2)',
            'HANDLER_ID': 'VARCHAR(50)',
            'TESTER_ID': 'VARCHAR(50)'
        },
        'description': '产品周期管理表',
        'protect_mode': True  # 严格保护模式
    },
    
    'wip_lot': {
        'required_fields': ['LOT_ID', 'DEVICE', 'STAGE'],
        'field_mapping': {
            'LOT_ID': 'LOT_ID',
            'DEVICE': 'DEVICE',
            'STAGE': 'STAGE',
            'QTY': 'QTY',
            'DUE_DATE': 'DUE_DATE',
            'PRIORITY': 'PRIORITY',
            'ORDER_INDEX': 'ORDER_INDEX',
            'PROD_ID': 'PROD_ID',
            'PKG_PN': 'PKG_PN',
            'CHIP_ID': 'CHIP_ID',
            'HANDLER_ID': 'HANDLER_ID',
            'TESTER_ID': 'TESTER_ID',
            'STATUS': 'STATUS'
        },
        'field_types': {
            'LOT_ID': 'VARCHAR(50)',
            'DEVICE': 'VARCHAR(50)',
            'STAGE': 'VARCHAR(50)',
            'QTY': 'INT',
            'DUE_DATE': 'DATE',
            'PRIORITY': 'VARCHAR(20)',
            'ORDER_INDEX': 'INT',
            'PROD_ID': 'VARCHAR(50)',
            'PKG_PN': 'VARCHAR(50)',
            'CHIP_ID': 'VARCHAR(50)',
            'HANDLER_ID': 'VARCHAR(50)',
            'TESTER_ID': 'VARCHAR(50)',
            'STATUS': 'VARCHAR(20)'
        },
        'description': 'WIP批次管理表 - 排产核心',
        'protect_mode': True
    },
    
    'ET_WAIT_LOT': {
        'required_fields': ['LOT_ID', 'DEVICE', 'STAGE'],
        'field_mapping': {
            'LOT_ID': 'LOT_ID',
            'DEVICE': 'DEVICE',
            'STAGE': 'STAGE',
            'QTY': 'QTY',
            'PRIORITY': 'PRIORITY',
            'PLAN_END_TIME': 'PLAN_END_TIME',
            'PKG_PN': 'PKG_PN',
            'CHIP_ID': 'CHIP_ID',
            'DUE_DATE': 'DUE_DATE'
        },
        'field_types': {
            'LOT_ID': 'VARCHAR(50)',
            'DEVICE': 'VARCHAR(50)',
            'STAGE': 'VARCHAR(50)',
            'QTY': 'INT',
            'PRIORITY': 'VARCHAR(20)',
            'PLAN_END_TIME': 'DATETIME',
            'PKG_PN': 'VARCHAR(50)',
            'CHIP_ID': 'VARCHAR(50)',
            'DUE_DATE': 'DATE'
        },
        'description': '待排产批次表 - 排产核心',
        'protect_mode': True
    },
    
    'ET_UPH_EQP': {
        'required_fields': ['DEVICE', 'STAGE'],
        'field_mapping': {
            'DEVICE': 'DEVICE',
            'STAGE': 'STAGE',
            'UPH': 'UPH',
            'EQUIPMENT_TYPE': 'EQUIPMENT_TYPE',
            'TESTER_ID': 'TESTER_ID',
            'HANDLER_ID': 'HANDLER_ID',
            'PKG_PN': 'PKG_PN',
            'CHIP_ID': 'CHIP_ID'
        },
        'field_types': {
            'DEVICE': 'VARCHAR(50)',
            'STAGE': 'VARCHAR(50)',
            'UPH': 'INT',
            'EQUIPMENT_TYPE': 'VARCHAR(50)',
            'TESTER_ID': 'VARCHAR(50)',
            'HANDLER_ID': 'VARCHAR(50)',
            'PKG_PN': 'VARCHAR(50)',
            'CHIP_ID': 'VARCHAR(50)'
        },
        'description': 'UPH设备效率表 - 排产算法依赖',
        'protect_mode': True
    },
    
    'eqp_status': {
        'required_fields': ['TESTER_ID'],
        'field_mapping': {
            'TESTER_ID': 'TESTER_ID',
            'HANDLER_ID': 'HANDLER_ID', 
            'STATUS': 'STATUS',
            'CURRENT_LOT_ID': 'CURRENT_LOT_ID',
            'EQUIPMENT_TYPE': 'EQUIPMENT_TYPE',
            'LOCATION': 'LOCATION'
        },
        'field_types': {
            'TESTER_ID': 'VARCHAR(50)',
            'HANDLER_ID': 'VARCHAR(50)',
            'STATUS': 'VARCHAR(20)',
            'CURRENT_LOT_ID': 'VARCHAR(50)',
            'EQUIPMENT_TYPE': 'VARCHAR(50)',
            'LOCATION': 'VARCHAR(50)'
        },
        'description': '设备状态表 - 排产调度依赖',
        'protect_mode': True
    },
    
    'et_ft_test_spec': {
        'required_fields': ['TEST_SPEC_ID'],
        'field_mapping': {
            'TEST_SPEC_ID': 'TEST_SPEC_ID',
            'TEST_SPEC_NAME': 'TEST_SPEC_NAME',
            'TEST_SPEC_VER': 'TEST_SPEC_VER',
            'STAGE': 'STAGE',
            'TESTER': 'TESTER',
            'DEVICE': 'DEVICE',
            'CHIP_ID': 'CHIP_ID',
            'PKG_PN': 'PKG_PN',
            'HANDLER': 'HANDLER',
            'TEMPERATURE': 'TEMPERATURE',
            'TEST_TIME': 'TEST_TIME',
            'UPH': 'UPH',
            'TB_PN': 'TB_PN',
            'HB_PN': 'HB_PN',
            'EQP_CLASS': 'EQP_CLASS',
            'APPROVAL_STATE': 'APPROVAL_STATE',
            'ACTV_YN': 'ACTV_YN'
        },
        'field_types': {
            'TEST_SPEC_ID': 'VARCHAR(50)',
            'TEST_SPEC_NAME': 'VARCHAR(100)',
            'TEST_SPEC_VER': 'VARCHAR(20)',
            'STAGE': 'VARCHAR(50)',
            'TESTER': 'VARCHAR(50)',
            'DEVICE': 'VARCHAR(50)',
            'CHIP_ID': 'VARCHAR(50)',
            'PKG_PN': 'VARCHAR(50)',
            'HANDLER': 'VARCHAR(50)',
            'TEMPERATURE': 'DECIMAL(5,2)',
            'TEST_TIME': 'DECIMAL(10,2)',
            'UPH': 'INT',
            'TB_PN': 'VARCHAR(50)',
            'HB_PN': 'VARCHAR(50)',
            'EQP_CLASS': 'VARCHAR(50)',
            'APPROVAL_STATE': 'VARCHAR(20)',
            'ACTV_YN': 'CHAR(1)'
        },
        'description': '测试规格表',
        'protect_mode': True
    },
    
    'devicepriorityconfig': {
        'required_fields': ['device'],
        'field_mapping': {
            'device': 'device',
            'priority': 'priority',
            'from_time': 'from_time',
            'end_time': 'end_time',
            'refresh_time': 'refresh_time',
            'user': 'user'
        },
        'field_types': {
            'device': 'VARCHAR(100)',
            'priority': 'INT',
            'from_time': 'DATETIME',
            'end_time': 'DATETIME',
            'refresh_time': 'DATETIME',
            'user': 'VARCHAR(50)'
        },
        'description': '产品优先级配置表 - 排产核心',
        'protect_mode': True
    },
    
    'lotpriorityconfig': {
        'required_fields': ['device'],
        'field_mapping': {
            'device': 'device',
            'stage': 'stage',
            'priority': 'priority',
            'refresh_time': 'refresh_time',
            'user': 'user'
        },
        'field_types': {
            'device': 'VARCHAR(100)',
            'stage': 'VARCHAR(50)',
            'priority': 'INT',
            'refresh_time': 'DATETIME',
            'user': 'VARCHAR(50)'
        },
        'description': '批次优先级配置表 - 排产核心',
        'protect_mode': True
    },
    
    'tcc_inv': {
        'required_fields': ['TCC_CELL'],
        'field_mapping': {
            'TCC_CELL': 'TCC_CELL',
            'DEVICE': 'DEVICE',
            'CHIP_ID': 'CHIP_ID',
            'PKG_PN': 'PKG_PN',
            'LOT_ID': 'LOT_ID',
            'STAGE': 'STAGE',
            'QTY': 'QTY',
            'DUE_DATE': 'DUE_DATE',
            'PRIORITY': 'PRIORITY',
            'STATUS': 'STATUS',
            'LOCATION': 'LOCATION',
            'HANDLER_ID': 'HANDLER_ID',
            'TESTER_ID': 'TESTER_ID'
        },
        'field_types': {
            'TCC_CELL': 'VARCHAR(50)',
            'DEVICE': 'VARCHAR(50)',
            'CHIP_ID': 'VARCHAR(50)',
            'PKG_PN': 'VARCHAR(50)',
            'LOT_ID': 'VARCHAR(50)',
            'STAGE': 'VARCHAR(50)',
            'QTY': 'INT',
            'DUE_DATE': 'DATE',
            'PRIORITY': 'VARCHAR(20)',
            'STATUS': 'VARCHAR(20)',
            'LOCATION': 'VARCHAR(100)',
            'HANDLER_ID': 'VARCHAR(50)',
            'TESTER_ID': 'VARCHAR(50)'
        },
        'description': '套件资源表',
        'protect_mode': True
    },
    
    'et_recipe_file': {
        'required_fields': ['DEVICE', 'STAGE', 'RECIPE_FILE_NAME'],
        'field_mapping': {
            'PROD_ID': 'PROD_ID',
            'COMPANY_ID': 'COMPANY_ID',
            'STAGE': 'STAGE',
            'DEVICE': 'DEVICE',
            'CHIP_ID': 'CHIP_ID',
            'PKG_PN': 'PKG_PN',
            'RECIPE_FILE_NAME': 'RECIPE_FILE_NAME',
            'RECIPE_FILE_PATH': 'RECIPE_FILE_PATH',
            'APPROVAL_STATE': 'APPROVAL_STATE',
            'FAC_ID': 'FAC_ID',
            'EDIT_STATE': 'EDIT_STATE',
            'EDIT_TIME': 'EDIT_TIME',
            'EDIT_USER': 'EDIT_USER',
            'EVENT': 'EVENT',
            'EVENT_KEY': 'EVENT_KEY',
            'EVENT_TIME': 'EVENT_TIME',
            'EVENT_USER': 'EVENT_USER',
            'EVENT_MSG': 'EVENT_MSG',
            'CREATE_TIME': 'CREATE_TIME',
            'CREATE_USER': 'CREATE_USER',
            'PROD_TYPE': 'PROD_TYPE',
            'EQP_TYPE': 'EQP_TYPE',
            'HANDLER_CONFIG': 'HANDLER_CONFIG', 
            'SIMP_RECIPE_FILE_PATH': 'SIMP_RECIPE_FILE_PATH',
            'RECIPE_VER': 'RECIPE_VER',
            'SUB_FAC': 'SUB_FAC',
            'KIT_PN': 'KIT_PN',
            'SOCKET_PN': 'SOCKET_PN',
            'FAMILY': 'FAMILY',
            'COORDINATE_ONE': 'COORDINATE_ONE',
            'COORDINATE_TWO': 'COORDINATE_TWO',
            'COORDINATE_THREE': 'COORDINATE_THREE'
        },
        'field_types': {
            'COORDINATE_ONE': 'DECIMAL(9,6)',
            'COORDINATE_TWO': 'DECIMAL(9,6)',
            'COORDINATE_THREE': 'DECIMAL(9,6)'
        },
        'description': '设备配方文件表 - 排产算法依赖',
        'protect_mode': True
    }
}

# ================================
# 字段卡控核心函数
# ================================

def validate_table_exists(conn, table_name):
    """验证表是否存在，如果是保护模式的表，必须预先存在"""
    try:
        with conn.cursor() as cursor:
            cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            result = cursor.fetchone()
            
            if not result:
                if table_name in BUSINESS_TABLE_SCHEMAS:
                    schema = BUSINESS_TABLE_SCHEMAS[table_name]
                    if schema.get('protect_mode', False):
                        raise ValueError(f"保护模式表 {table_name} 不存在！请先运行 init_db.py 创建表结构")
                return False
            return True
    except Exception as e:
        logger.error(f"检查表存在性失败: {table_name} - {e}")
        raise

def get_existing_table_structure(conn, table_name):
    """获取已存在表的结构信息"""
    try:
        with conn.cursor() as cursor:
            cursor.execute(f"DESCRIBE {table_name}")
            columns_info = cursor.fetchall()
            
            existing_fields = {}
            for col_info in columns_info:
                field_name = col_info[0]
                data_type = col_info[1]
                is_nullable = col_info[2] == 'YES'
                key_type = col_info[3]
                default_value = col_info[4]
                
                existing_fields[field_name] = {
                    'type': data_type,
                    'nullable': is_nullable,
                    'key': key_type,
                    'default': default_value
                }
            
            logger.info(f"获取表结构成功: {table_name}, 字段数: {len(existing_fields)}")
            return existing_fields
            
    except Exception as e:
        logger.error(f"获取表结构失败: {table_name} - {e}")
        raise

def validate_and_map_fields(df, table_name, existing_structure):
    """验证并映射Excel字段到数据库字段"""
    try:
        if table_name not in BUSINESS_TABLE_SCHEMAS:
            logger.warning(f"表 {table_name} 未在字段映射配置中，使用原始字段名")
            return df, list(df.columns)
        
        schema = BUSINESS_TABLE_SCHEMAS[table_name]
        field_mapping = schema['field_mapping']
        required_fields = schema['required_fields']
        
        # 记录映射过程
        logger.info(f"开始字段验证和映射: {table_name}")
        logger.info(f"Excel原始字段: {list(df.columns)}")
        
        # 1. 检查必填字段
        missing_required = []
        for req_field in required_fields:
            # 检查是否有对应的Excel字段能映射到必填字段
            excel_field_found = False
            for excel_col in df.columns:
                if field_mapping.get(excel_col) == req_field or excel_col == req_field:
                    excel_field_found = True
                    break
            
            if not excel_field_found:
                missing_required.append(req_field)
        
        if missing_required:
            raise ValueError(f"Excel缺少必填字段: {missing_required}")
        
        # 2. 执行字段映射
        mapped_df = pd.DataFrame()
        mapped_fields = []
        
        for excel_col in df.columns:
            # 检查是否有字段映射
            if excel_col in field_mapping:
                db_field = field_mapping[excel_col]
                
                # 验证目标字段在数据库中存在
                if db_field not in existing_structure:
                    logger.warning(f"映射的目标字段 {db_field} 在数据库表中不存在，跳过")
                    continue
                    
                mapped_df[db_field] = df[excel_col]
                mapped_fields.append(db_field)
                logger.info(f"字段映射: {excel_col} -> {db_field}")
                
            elif excel_col in existing_structure:
                # 直接匹配的字段
                mapped_df[excel_col] = df[excel_col]
                mapped_fields.append(excel_col)
                logger.info(f"字段直接匹配: {excel_col}")
                
            else:
                logger.warning(f"Excel字段 {excel_col} 无法映射到数据库，将被跳过")
        
        if mapped_df.empty:
            raise ValueError(f"没有任何字段能够成功映射到表 {table_name}")
        
        logger.info(f"字段映射完成: {len(mapped_fields)} 个字段")
        logger.info(f"最终映射字段: {mapped_fields}")
        
        return mapped_df, mapped_fields
        
    except Exception as e:
        logger.error(f"字段验证和映射失败: {table_name} - {e}")
        raise

def convert_data_types(mapped_df, table_name, mapped_fields, existing_structure):
    """根据数据库字段类型转换数据"""
    try:
        if table_name not in BUSINESS_TABLE_SCHEMAS:
            return mapped_df
        
        schema = BUSINESS_TABLE_SCHEMAS[table_name]
        field_types = schema.get('field_types', {})
        
        for field in mapped_fields:
            if field in field_types:
                expected_type = field_types[field]
                
                try:
                    if 'INT' in expected_type.upper():
                        # 转换为整数
                        mapped_df[field] = pd.to_numeric(mapped_df[field], errors='coerce').fillna(0).astype(int)
                    elif 'DECIMAL' in expected_type.upper() or 'FLOAT' in expected_type.upper():
                        # 转换为浮点数
                        mapped_df[field] = pd.to_numeric(mapped_df[field], errors='coerce').fillna(0.0)
                    elif 'DATE' in expected_type.upper():
                        # 转换为日期
                        mapped_df[field] = pd.to_datetime(mapped_df[field], errors='coerce').dt.date
                    elif 'DATETIME' in expected_type.upper() or 'TIMESTAMP' in expected_type.upper():
                        # 转换为日期时间
                        mapped_df[field] = pd.to_datetime(mapped_df[field], errors='coerce')
                    else:
                        # 字符串类型，限制长度
                        if 'VARCHAR' in expected_type.upper():
                            # 提取长度限制
                            import re
                            match = re.search(r'VARCHAR\((\d+)\)', expected_type.upper())
                            if match:
                                max_length = int(match.group(1))
                                mapped_df[field] = mapped_df[field].astype(str).apply(
                                    lambda x: x[:max_length] if len(str(x)) > max_length else x
                                )
                        else:
                            mapped_df[field] = mapped_df[field].astype(str)
                    
                    logger.info(f"字段类型转换成功: {field} -> {expected_type}")
                    
                except Exception as conv_error:
                    logger.warning(f"字段 {field} 类型转换失败: {conv_error}，保持原始类型")
        
        return mapped_df
        
    except Exception as e:
        logger.error(f"数据类型转换失败: {table_name} - {e}")
        return mapped_df

def safe_insert_data_to_existing_table(conn, table_name, mapped_df, mapped_fields):
    """安全地向已存在的表插入数据（不破坏表结构）"""
    if mapped_df.empty:
        logger.warning(f"没有数据需要插入到表 {table_name}")
        return 0

    try:
        with conn.cursor() as cursor:
            # 构建INSERT语句（只插入数据，不修改表结构）
            placeholders = ', '.join(['%s'] * len(mapped_fields))
            column_names = ', '.join([f"`{field}`" for field in mapped_fields])
            
            insert_sql = f"INSERT INTO `{table_name}` ({column_names}) VALUES ({placeholders})"
            
            # 批量插入数据
            inserted_count = 0
            batch_size = 500  # 减小批次大小，提高稳定性
            total_rows = len(mapped_df)
            
            logger.info(f"开始向表 {table_name} 插入数据: {total_rows} 条记录")
            
            for i in range(0, total_rows, batch_size):
                batch_df = mapped_df.iloc[i:i+batch_size]
                
                # 准备数据
                values = []
                for _, row in batch_df.iterrows():
                    row_values = []
                    for field in mapped_fields:
                        value = row[field]
                        if pd.isna(value):
                            row_values.append(None)
                        else:
                            # 确保字符串类型的安全处理
                            if isinstance(value, str):
                                row_values.append(value.strip())
                            else:
                                row_values.append(value)
                    values.append(tuple(row_values))
                
                # 执行批量插入
                cursor.executemany(insert_sql, values)
                inserted_count += len(values)
                
                logger.info(f"表 {table_name}: 已插入 {inserted_count}/{total_rows} 条记录")
            
            conn.commit()
            logger.info(f"✅ 表 {table_name} 数据插入成功: {inserted_count} 条记录")
            return inserted_count
            
    except Exception as e:
        logger.error(f"向表 {table_name} 插入数据失败: {e}")
        conn.rollback()
        raise

# ================================
# 保持原有的辅助函数，但修改核心处理逻辑
# ================================

def is_valid_excel_file(file_path):
    """检查Excel文件是否有效，过滤临时文件和无效文件"""
    try:
        file_name = file_path.name

        # 过滤临时文件
        if file_name.startswith('~$'):
            logger.debug(f"跳过Excel临时文件: {file_name}")
            return False

        # 过滤其他临时文件
        temp_patterns = ['.tmp', '.temp', '.bak', '.backup', '.~lock']
        if any(pattern in file_name.lower() for pattern in temp_patterns):
            logger.debug(f"跳过临时文件: {file_name}")
            return False

        # 检查文件大小（空文件）
        if file_path.stat().st_size == 0:
            logger.warning(f"跳过空文件: {file_name}")
            return False

        # 检查文件是否被占用（尝试打开）
        try:
            with open(file_path, 'rb') as f:
                # 读取前几个字节检查文件格式
                header = f.read(8)
                if len(header) < 8:
                    logger.warning(f"文件太小，可能损坏: {file_name}")
                    return False
        except (PermissionError, OSError) as e:
            logger.warning(f"文件被占用或无法访问: {file_name} - {e}")
            return False

        # 检查文件扩展名
        valid_extensions = ['.xlsx', '.xls']
        if not any(file_name.lower().endswith(ext) for ext in valid_extensions):
            logger.debug(f"跳过非Excel文件: {file_name}")
            return False

        return True

    except Exception as e:
        logger.error(f"检查文件有效性失败: {file_path} - {e}")
        return False

def clean_dataframe(df, file_name=""):
    """清理和验证DataFrame数据"""
    try:
        if df.empty:
            logger.warning(f"DataFrame为空: {file_name}")
            return df

        # 记录原始行数
        original_rows = len(df)

        # 1. 删除完全空白的行
        df = df.dropna(how='all')

        # 2. 简化列名清理（保持业务字段名不变）
        cleaned_columns = []
        for col in df.columns:
            # 转换为字符串并清理基本空白字符
            clean_col = str(col).strip()
            # 只做必要的清理，保持业务字段名
            clean_col = clean_col.replace('\n', '').replace('\r', '')
            
            if not clean_col:
                clean_col = f"column_{len(cleaned_columns)}"
            cleaned_columns.append(clean_col)

        df.columns = cleaned_columns

        # 3. 处理重复列名
        seen_columns = set()
        final_columns = []
        for col in df.columns:
            original_col = col
            counter = 1
            while col in seen_columns:
                col = f"{original_col}_{counter}"
                counter += 1
            seen_columns.add(col)
            final_columns.append(col)

        df.columns = final_columns

        # 4. 清理数据值（保持数据格式）
        for col in df.columns:
            try:
                # 处理NaN值
                df[col] = df[col].fillna('')
                # 对字符串进行基本清理
                df[col] = df[col].apply(lambda x: str(x).strip() if pd.notna(x) else '')
                # 替换 'nan', 'None' 等为空字符串
                df[col] = df[col].replace(['nan', 'None', 'NaT', '<NA>'], '')
                
            except Exception as e:
                logger.warning(f"清理列 {col} 时出现警告: {e}")
                df[col] = df[col].apply(lambda x: str(x) if pd.notna(x) else '')

        cleaned_rows = len(df)
        if original_rows != cleaned_rows:
            logger.info(f"数据清理完成: {file_name} - 原始行数: {original_rows}, 清理后: {cleaned_rows}")

        return df

    except Exception as e:
        logger.error(f"清理DataFrame失败: {file_name} - {e}")
        return df

def get_mysql_connection(database_name=None):
    """获取MySQL数据库连接，支持智能数据库路由"""
    try:
        # 如果没有指定数据库，使用默认的 aps 数据库
        if database_name is None:
            database_name = os.getenv('MYSQL_DATABASE', 'aps')
        
        # 优先从外部配置文件获取MySQL配置
        try:
            # 1. 尝试从外部配置文件读取
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from app.utils.config_reader import get_database_config
            external_config = get_database_config()
            mysql_config = {
                'host': external_config['host'],
                'port': external_config['port'],
                'user': external_config['user'],
                'password': external_config['password'],
                'database': database_name,
                'charset': 'utf8mb4',
                'cursorclass': pymysql.cursors.DictCursor
            }
        except Exception:
            # 2. 回退到环境变量获取MySQL配置
            mysql_config = {
                'host': os.getenv('MYSQL_HOST', os.getenv('DB_HOST', 'localhost')),
            'port': int(os.getenv('MYSQL_PORT', 3306)),
            'user': os.getenv('MYSQL_USER', 'root'),
            'password': os.getenv('MYSQL_PASSWORD', ''),
            'database': database_name,
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }

        # 如果环境变量中没有密码，使用正确的默认密码
        if not mysql_config['password']:
            mysql_config['password'] = 'WWWwww123!'  # 使用正确的密码

        # 添加连接超时和其他参数
        mysql_config.update({
            'autocommit': False,  # 禁用自动提交，手动控制事务
            'connect_timeout': 30,  # 连接超时
            'read_timeout': 30,     # 读取超时
            'write_timeout': 30     # 写入超时
        })
        
        conn = pymysql.connect(**mysql_config)
        
        # 测试连接
        with conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
            
        logger.info(f"成功连接到MySQL数据库: {mysql_config['host']}:{mysql_config['port']}/{mysql_config['database']}")
        return conn

    except Exception as e:
        logger.error(f"连接MySQL数据库失败: {e}")
        # 记录详细的连接配置（不包含密码）
        safe_config = {k: v for k, v in mysql_config.items() if k != 'password'}
        safe_config['password'] = '***' if mysql_config.get('password') else 'None'
        logger.error(f"连接配置: {safe_config}")
        raise

def update_progress(percent, message, current_file=None, files_processed=0, total_files=0, error=False):
    """更新导入进度到文件"""
    try:
        # 修复路径计算：从当前文件位置计算到项目根目录的instance文件夹
        current_dir = os.path.dirname(__file__)  # tools/data_import/
        project_root = os.path.dirname(os.path.dirname(current_dir))  # 项目根目录
        instance_path = os.path.join(project_root, 'instance')
        
        if not os.path.exists(instance_path):
            os.makedirs(instance_path)
        
        progress_file = os.path.join(instance_path, 'import_progress_safe.json')
        
        progress_data = {
            'percent': percent,
            'message': message,
            'files_processed': files_processed,
            'total_files': total_files,
            'timestamp': time.time(),
            'mode': 'safe_import'  # 标识为安全导入模式
        }
        
        if current_file:
            progress_data['current_file'] = current_file
            
        if error:
            progress_data['error'] = True
        
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump(progress_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"进度更新: {percent}% - {message}")
        
    except Exception as e:
        logger.error(f"更新进度失败: {e}")
        # 打印详细错误信息用于调试
        import traceback
        logger.error(f"进度更新详细错误: {traceback.format_exc()}")

def process_excel_file_safely(file_path, conn, file_index, total_files, processed_records_before, total_records_count):
    """安全处理单个Excel文件（字段卡控版本）"""
    max_retries = 3
    retry_delay = 1

    for attempt in range(max_retries):
        try:
            logger.info(f"🔒 安全模式处理文件: {file_path} (尝试 {attempt + 1}/{max_retries})")

            # 验证文件有效性
            if not is_valid_excel_file(Path(file_path)):
                raise ValueError(f"文件无效或被占用: {file_path}")

            # 读取Excel文件
            try:
                excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            except Exception as read_error:
                logger.warning(f"使用openpyxl引擎失败，尝试xlrd引擎: {read_error}")
                try:
                    excel_data = pd.read_excel(file_path, sheet_name=None, engine='xlrd')
                except Exception as read_error2:
                    raise Exception(f"无法读取Excel文件: openpyxl({read_error}), xlrd({read_error2})")

            if not excel_data:
                raise ValueError(f"Excel文件为空: {file_path}")

            # 处理成功，跳出重试循环
            break

        except Exception as e:
            logger.warning(f"处理文件失败 (尝试 {attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                retry_delay *= 2
            else:
                logger.error(f"处理文件 {file_path} 最终失败: {e}")
                return {
                    'success': False,
                    'file': Path(file_path).name,
                    'file_path': file_path,
                    'error': str(e)
                }

    # 继续处理Excel数据
    try:
        processed_sheets = []
        total_records = 0
        current_processed = processed_records_before
        
        for sheet_name, df in excel_data.items():
            if df.empty:
                logger.warning(f"工作表 {sheet_name} 为空，跳过")
                continue

            # 清理数据
            df = clean_dataframe(df, f"{Path(file_path).name}:{sheet_name}")

            if df.empty:
                logger.warning(f"工作表 {sheet_name} 清理后为空，跳过")
                continue
            
            # 确定表名（使用更安全的映射策略）
            table_name = determine_table_name_safely(file_path, sheet_name)
            logger.info(f"🎯 确定目标表: {table_name}")
            
            # 验证表是否存在
            if not validate_table_exists(conn, table_name):
                if table_name in BUSINESS_TABLE_SCHEMAS and BUSINESS_TABLE_SCHEMAS[table_name].get('protect_mode'):
                    raise ValueError(f"保护模式表 {table_name} 不存在，请先运行 init_db.py")
                else:
                    logger.warning(f"表 {table_name} 不存在，跳过")
                    continue
            
            # 获取现有表结构
            existing_structure = get_existing_table_structure(conn, table_name)
            
            # 验证和映射字段
            mapped_df, mapped_fields = validate_and_map_fields(df, table_name, existing_structure)
            
            # 转换数据类型
            mapped_df = convert_data_types(mapped_df, table_name, mapped_fields, existing_structure)
            
            # 安全插入数据
            record_count = safe_insert_data_to_existing_table(conn, table_name, mapped_df, mapped_fields)
            
            processed_sheets.append({
                'sheet_name': sheet_name,
                'table_name': table_name,
                'record_count': record_count,
                'mapped_fields': mapped_fields
            })
            total_records += record_count
            current_processed += record_count
        
        conn.commit()
        logger.info(f"✅ 文件 {file_path} 安全处理完成，共处理 {total_records} 条记录")
        
        return {
            'success': True,
            'file': Path(file_path).name,
            'table': f"{Path(file_path).stem}",
            'records': total_records,
            'file_path': file_path,
            'sheets': processed_sheets,
            'total_records': total_records,
            'mode': 'safe_import'
        }
        
    except Exception as e:
        logger.error(f"安全处理文件 {file_path} 失败: {e}")
        conn.rollback()
        return {
            'success': False,
            'file': Path(file_path).name,
            'file_path': file_path,
            'error': str(e)
        }

def determine_table_name_safely(file_path, sheet_name):
    """安全确定表名（优先使用已知的业务表映射）"""
    
    # 优先级1: 工作表名直接匹配业务表
    if sheet_name in BUSINESS_TABLE_SCHEMAS:
        return sheet_name
    
    # 优先级2: 文件名匹配
    file_name = os.path.basename(file_path)
    base_name = os.path.splitext(file_name)[0].lower()
    
    # 特殊映射规则
    special_mappings = {
        'devicepriorityconfig': 'devicepriorityconfig',
        'lotpriorityconfig': 'lotpriorityconfig',
        'tcc_inv': 'tcc_inv',
        'lotprioritydone': 'LotPriorityDone'
    }
    
    if base_name in special_mappings:
        return special_mappings[base_name]
    
    # 优先级3: 工作表名的变体匹配
    sheet_name_upper = sheet_name.upper()
    for business_table in BUSINESS_TABLE_SCHEMAS.keys():
        if business_table.upper() == sheet_name_upper:
            return business_table
    
    # 优先级4: 使用清理后的工作表名
    if sheet_name.lower() == 'sheet1':
        return base_name
    else:
        return sheet_name.lower()

def import_excel_files_safely(directory_path, mysql_config=None):
    """
    安全导入Excel文件到MySQL数据库（字段卡控版本）
    
    Args:
        directory_path: Excel文件目录路径
        mysql_config: MySQL配置字典（可选）
    
    Returns:
        tuple: (success, result)
    """
    start_time = time.time()
    
    try:
        logger.info("🔒 启动安全导入模式 - 字段卡控保护")
        
        # 初始化进度
        update_progress(0, "正在扫描Excel文件...")
        
        # 检查目录
        if not os.path.exists(directory_path):
            return False, {'error': f'目录不存在: {directory_path}'}
        
        # 获取有效Excel文件
        excel_files = []
        for ext in ['*.xlsx', '*.xls']:
            all_files = Path(directory_path).glob(ext)
            for file_path in all_files:
                if is_valid_excel_file(file_path):
                    excel_files.append(file_path)

        if not excel_files:
            return False, {'error': f'目录中没有找到有效的Excel文件: {directory_path}'}
        
        logger.info(f"🔍 找到 {len(excel_files)} 个Excel文件")
        
        # 连接数据库
        update_progress(10, "正在连接数据库...")
        conn = get_mysql_connection('aps')
        
        # 预检查关键业务表是否存在
        update_progress(15, "正在验证表结构...")
        critical_tables = ['ct', 'wip_lot', 'ET_WAIT_LOT', 'devicepriorityconfig', 'lotpriorityconfig']
        missing_tables = []
        
        for table in critical_tables:
            if not validate_table_exists(conn, table):
                missing_tables.append(table)
        
        if missing_tables:
            warning_msg = f"警告：以下关键业务表不存在: {missing_tables}。建议先运行 init_db.py 创建表结构"
            logger.warning(warning_msg)
        
        # 处理文件
        update_progress(20, "开始安全处理文件...")
        processed_files = []
        failed_files = []
        processed_records = 0
        
        for i, file_path in enumerate(excel_files):
            try:
                # 更新开始处理文件的进度
                current_percent = int((i / len(excel_files)) * 90)
                update_progress(current_percent, f"正在处理文件: {file_path.name}", 
                              current_file=file_path.name, files_processed=i, total_files=len(excel_files))
                
                logger.info(f"📄 处理文件 ({i+1}/{len(excel_files)}): {file_path.name}")
                
                # 读取Excel文件
                try:
                    df = pd.read_excel(file_path, engine='openpyxl')
                except Exception as e:
                    try:
                        df = pd.read_excel(file_path, engine='xlrd')
                    except Exception as e2:
                        raise Exception(f"无法读取Excel文件: {e}, {e2}")
                
                if df.empty:
                    logger.warning(f"文件为空: {file_path.name}")
                    continue
                
                # 清理数据
                df = clean_dataframe(df, file_path.name)
                
                # 确定表名（使用文件名，去除扩展名）
                table_name = file_path.stem.lower()
                
                # 获取现有表结构
                try:
                    existing_structure = get_existing_table_structure(conn, table_name)
                except:
                    # 如果表不存在且为保护模式，则跳过
                    if (table_name in BUSINESS_TABLE_SCHEMAS and 
                        BUSINESS_TABLE_SCHEMAS[table_name].get('protect_mode', False)):
                        logger.error(f"🔒 保护模式下表 {table_name} 不存在，跳过文件: {file_path.name}")
                        failed_files.append({
                            'file': file_path.name,
                            'error': f'保护模式下表 {table_name} 不存在'
                        })
                        continue
                    else:
                        # 非保护模式，表不存在时existing_structure为空
                        existing_structure = {}
                
                # 字段验证和映射
                if table_name in BUSINESS_TABLE_SCHEMAS:
                    mapped_df, mapped_fields = validate_and_map_fields(df, table_name, existing_structure)
                    mapped_df = convert_data_types(mapped_df, table_name, mapped_fields, existing_structure)
                else:
                    mapped_df = df
                    mapped_fields = list(df.columns)
                
                # 安全导入数据
                record_count = safe_insert_data_to_existing_table(conn, table_name, mapped_df, mapped_fields)
                
                processed_files.append({
                    'file': file_path.name,
                    'table': table_name,
                    'records': record_count,
                    'fields': len(mapped_fields),
                    'protected': table_name in BUSINESS_TABLE_SCHEMAS and BUSINESS_TABLE_SCHEMAS[table_name].get('protect_mode', False)
                })
                processed_records += record_count
                
                # 更新文件处理完成的进度
                completed_percent = int(((i + 1) / len(excel_files)) * 90)
                update_progress(completed_percent, f"已完成文件: {file_path.name} ({record_count} 条记录)", 
                              current_file=file_path.name, files_processed=i+1, total_files=len(excel_files))
                
                logger.info(f"✅ 文件处理完成: {file_path.name} -> {table_name} ({record_count} 条记录)")
                
            except Exception as e:
                logger.error(f"❌ 文件处理失败: {file_path.name} - {e}")
                failed_files.append({
                    'file': file_path.name,
                    'error': str(e)
                })
                
                # 更新失败文件的进度
                failed_percent = int(((i + 1) / len(excel_files)) * 90)
                update_progress(failed_percent, f"文件处理失败: {file_path.name}", 
                              current_file=file_path.name, files_processed=i+1, total_files=len(excel_files))
        
        conn.close()
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 最终进度更新
        update_progress(100, "安全导入完成！")
        
        # 准备返回结果
        result = {
            'message': f'安全导入完成，成功处理 {len(processed_files)} 个文件，失败 {len(failed_files)} 个',
            'processed_files': processed_files,
            'failed_files': failed_files,
            'total_files': len(excel_files),
            'failed_count': len(failed_files),
            'total_records': processed_records,
            'processing_time': round(processing_time, 2),
            'mode': 'safe_import',
            'field_protection': True
        }
        
        if missing_tables:
            result['warnings'] = [f"缺少关键业务表: {missing_tables}"]
        
        if failed_files:
            result['details'] = f'部分文件处理失败，请检查日志'
            return False, result
        else:
            result['details'] = f'所有文件安全处理成功，表结构完整保护，共导入 {processed_records} 条记录'
            return True, result
        
    except Exception as e:
        logger.error(f"安全导入过程失败: {e}")
        update_progress(0, f"导入失败: {str(e)}", error=True)
        return False, {'error': str(e), 'message': '安全导入过程失败'}

# ================================
# 兼容性接口
# ================================

def import_from_directory(directory_path, db_path=None):
    """
    兼容性函数，使用安全导入模式
    
    Args:
        directory_path: Excel文件目录路径
        db_path: 数据库路径（忽略，MySQL模式下不使用）
    
    Returns:
        tuple: (success, result)
    """
    return import_excel_files_safely(directory_path)

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("用法: python import_excel_to_mysql.py <Excel目录路径>")
        print("注意：这是字段卡控安全版本，会严格保护已有表结构")
        sys.exit(1)
    
    directory_path = sys.argv[1]
    success, result = import_excel_files_safely(directory_path)
    
    if success:
        print(f"✅ 安全导入成功: {result['message']}")
        print(f"   处理时间: {result['processing_time']} 秒")
        print(f"   总记录数: {result['total_records']}")
        print(f"   表结构保护: ✅ 已启用")
    else:
        print(f"❌ 安全导入失败: {result.get('error', '未知错误')}")
        if 'warnings' in result:
            for warning in result['warnings']:
                print(f"⚠️  {warning}")
        sys.exit(1) 