#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APS 车规芯片终测智能调度平台 - MySQL数据库初始化脚本
版本: v3.1 - 智能选择性数据清空
支持: 资源管理、智能排产、生产预览、用户权限管理
特性: 只清空业务数据表，保留系统配置和其他重要数据
"""

import os
import sys
import logging
import pymysql
from datetime import datetime
import json

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/init_db.log', encoding='utf-8')
    ]
)
logger = logging.getLogger('APS-Init')

def get_mysql_connection(database=None):
    """获取MySQL数据库连接 - 使用项目标准配置"""
    try:
        # 优先从外部配置文件读取MySQL配置
        try:
            # 添加项目路径以便导入配置模块
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from app.utils.config_reader import get_database_config
            external_config = get_database_config()
            config = {
                'host': external_config['host'],
                'port': external_config['port'],
                'user': external_config['user'],
                'password': external_config['password'],
                'charset': 'utf8mb4',
                'autocommit': True
            }
            logger.info(f"使用外部配置文件: {config['host']}:{config['port']}")
        except Exception as e:
            logger.debug(f"外部配置文件读取失败: {e}，使用默认配置")
            # 回退到默认配置（仅开发环境）
            if getattr(sys, 'frozen', False):
                raise Exception("❌ exe环境必须提供config.ini配置文件！")
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': 'WWWwww123!',
                'charset': 'utf8mb4',
                'autocommit': True
            }
        
        # 如果指定了数据库，添加到配置中
        if database:
            config['database'] = database
            
        connection = pymysql.connect(**config)
        logger.info(f"✅ MySQL连接成功: {config['host']}:{config['port']}" + (f"/{database}" if database else ""))
        return connection
        
    except Exception as e:
        logger.error(f"❌ MySQL连接失败: {e}")
        logger.error("请检查:")
        logger.error("  1. MySQL服务是否运行")
        logger.error("  2. 用户名密码是否正确: root / WWWwww123!")
        logger.error("  3. 端口3306是否可访问")
        raise

def create_databases():
    """创建MySQL数据库 - 单数据库模式"""
    connection = get_mysql_connection()  # 不指定数据库
    cursor = connection.cursor()
    
    # 只创建aps数据库 - 单数据库模式
    db_name = 'aps'
    
    try:
        # 检查数据库是否存在
        cursor.execute(f"SHOW DATABASES LIKE '{db_name}'")
        if cursor.fetchone():
            logger.info(f"✅ 数据库 '{db_name}' 已存在")
        else:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE {db_name} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            logger.info(f"✅ 创建数据库 '{db_name}' 成功")
    except Exception as e:
        logger.error(f"❌ 数据库 '{db_name}' 操作失败: {e}")
        raise
    
    cursor.close()
    connection.close()

def init_core_system_tables():
    """初始化核心系统表 - 与真实数据库结构一致"""
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    logger.info("🔧 创建核心系统表...")
    
    # 核心系统表定义 - 基于真实数据库结构
    system_tables = {
        'users': '''
        CREATE TABLE IF NOT EXISTS users (
            username VARCHAR(64) NOT NULL PRIMARY KEY,
            password_hash VARCHAR(1000) DEFAULT NULL,
            role VARCHAR(20) DEFAULT NULL,
            created_at DATETIME DEFAULT NULL,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            last_login DATETIME DEFAULT NULL,
            is_active TINYINT(1) DEFAULT 1,
            email VARCHAR(100) DEFAULT NULL,
            INDEX idx_users_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'user_permissions': '''
        CREATE TABLE IF NOT EXISTS user_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) DEFAULT NULL,
            menu_id INT DEFAULT NULL,
            granted_by VARCHAR(50) DEFAULT 'system',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_permission (username, menu_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'scheduler_config': '''
        CREATE TABLE IF NOT EXISTS scheduler_config (
            id INT AUTO_INCREMENT PRIMARY KEY,
            `key` VARCHAR(255) NOT NULL UNIQUE,
            value TEXT,
            description VARCHAR(500) DEFAULT NULL,
            updated_at DATETIME DEFAULT NULL,
            updated_by VARCHAR(100) DEFAULT NULL,
            UNIQUE KEY `key` (`key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'system_settings': '''
        CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            `key` VARCHAR(255) NOT NULL,
            value TEXT,
            description TEXT,
            user_id VARCHAR(50) DEFAULT NULL,
            setting_type VARCHAR(50) DEFAULT 'string',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            CREATE_TIME DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_key (user_id, `key`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'ai_settings': '''
        CREATE TABLE IF NOT EXISTS ai_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            settings JSON,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'menu_permissions': '''
        CREATE TABLE IF NOT EXISTS menu_permissions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            route VARCHAR(200),
            icon VARCHAR(50),
            parent_id INT,
            sort_order INT DEFAULT 0,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
    }
    
    # 创建核心系统表
    for table_name, create_sql in system_tables.items():
        try:
            cursor.execute(create_sql)
            logger.info(f"✅ 核心系统表 '{table_name}' 创建成功")
        except Exception as e:
            logger.error(f"❌ 创建核心系统表 '{table_name}' 失败: {e}")
            raise
    
    cursor.close()
    connection.close()

def init_business_tables():
    """初始化核心业务表 - 与真实数据库结构一致"""
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    logger.info("🔧 创建核心业务表...")
    
    # 核心业务表定义 - 基于真实数据库结构
    business_tables = {
        'ct': '''
        CREATE TABLE IF NOT EXISTS ct (
            id INT AUTO_INCREMENT PRIMARY KEY,
            LOT_ID TEXT,
            WORK_ORDER_ID TEXT,
            PROD_ID TEXT,
            DEVICE TEXT,
            PKG_PN TEXT,
            CHIP_ID TEXT,
            FLOW_ID TEXT,
            STAGE TEXT,
            LOT_QTY TEXT,
            ACT_QTY TEXT,
            GOOD_QTY TEXT,
            REJECT_QTY TEXT,
            LOSS_QTY TEXT,
            MAIN_EQP_ID TEXT,
            AUXILIARY_EQP_ID TEXT,
            LOT_START_TIME TEXT,
            LOT_END_TIME TEXT,
            SETUP_TIME TEXT,
            FT_TEST_PROGRAM TEXT,
            IS_HALF_LOT_DOWN TEXT,
            FIRST_PASS_YIELD TEXT,
            FINAL_YIELD TEXT,
            VM_QTY TEXT,
            ALARM_BIN TEXT,
            EVENT TEXT,
            EVENT_KEY TEXT,
            EVENT_TIME TEXT,
            EVENT_USER TEXT,
            EVENT_MSG TEXT,
            CREATE_TIME TEXT,
            CREATE_USER TEXT,
            FAC_ID TEXT,
            TRACK_CNT TEXT,
            COST_TIME TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'wip_lot': '''
        CREATE TABLE IF NOT EXISTS wip_lot (
            id INT AUTO_INCREMENT PRIMARY KEY,
            LOT_ID TEXT,
            LOT_TYPE TEXT,
            DET_LOT_TYPE TEXT,
            LOT_QTY TEXT,
            SUB_QTY TEXT,
            UNIT TEXT,
            SUB_UNIT TEXT,
            WIP_STATE TEXT,
            PROC_STATE TEXT,
            HOLD_STATE TEXT,
            RW_STATE TEXT,
            REPAIR_STATE TEXT,
            QC_STATE TEXT,
            PROD_ID TEXT,
            PROC_RULE_ID TEXT,
            PRP_ID TEXT,
            FLOW_ID TEXT,
            STAGE TEXT,
            PRP_VER TEXT,
            FLOW_VER TEXT,
            OPER_VER TEXT,
            CARR_ID TEXT,
            USE_SUB_LOT TEXT,
            AREA_ID TEXT,
            LOC_ID TEXT,
            EQP_ID TEXT,
            SUB_EQP_ID TEXT,
            PORT_ID TEXT,
            RECIPE_ID TEXT,
            SUB_RECIPE_ID TEXT,
            MARK_ID TEXT,
            LOT_IN_QTY TEXT,
            LOT_OUT_QTY TEXT,
            GOOD_QTY TEXT,
            NG_QTY TEXT,
            PREV_PROD_ID TEXT,
            PREV_PROC_RULE_ID TEXT,
            PREV_PRP_ID TEXT,
            PREV_PRP_VER TEXT,
            PREV_FLOW_ID TEXT,
            PREV_FLOW_VER TEXT,
            PREV_OPER_ID TEXT,
            PREV_OPER_VER TEXT,
            PREV_EQP_ID TEXT,
            PREV_PORT_ID TEXT,
            PREV_RECIPE_ID TEXT,
            PREV_SUB_RECIPE_ID TEXT,
            RTCL_ID TEXT,
            BATCH_ID TEXT,
            LAST_BATCH_ID TEXT,
            CTM_ID TEXT,
            LOT_GRP_ID TEXT,
            RESV_EQP_ID TEXT,
            HOT_TYPE TEXT,
            SEND_COMPANY_ID TEXT,
            OPER_CHANGE_TIME TEXT,
            JOB_START_TIME TEXT,
            JOB_END_TIME TEXT,
            PLAN_START_DATE TEXT,
            PLAN_DUE_DATE TEXT,
            GRADE TEXT,
            REASON_GRP TEXT,
            REASON_CODE TEXT,
            FR_RW_PROC_RULE_ID TEXT,
            FR_RW_PRP_ID TEXT,
            FR_RW_PRP_VER TEXT,
            FR_RW_FLOW_ID TEXT,
            FR_RW_FLOW_VER TEXT,
            FR_RW_OPER_ID TEXT,
            FR_RW_OPER_VER TEXT,
            RW_RT_PROC_RULE_ID TEXT,
            RW_RT_PRP_ID TEXT,
            RW_RT_PRP_VER TEXT,
            RW_RT_FLOW_ID TEXT,
            RW_RT_FLOW_VER TEXT,
            RW_RT_OPER_ID TEXT,
            RW_RT_OPER_VER TEXT,
            PILOT_TYPE TEXT,
            MERGE_OPER_ID TEXT,
            ACT_NM TEXT,
            LOT_JUDGE TEXT,
            FAC_ID TEXT,
            SUB_FAC TEXT,
            ROOT_LOT_ID TEXT,
            PARENT_LOT_ID TEXT,
            CHILD_LOT_ID TEXT,
            LOT_OBJ_ID TEXT,
            CUST_LOT_ID TEXT,
            WORK_ORDER_ID TEXT,
            WORK_ORDER_VER TEXT,
            BOM_ID TEXT,
            BOM_VER TEXT,
            PO_ID TEXT,
            LOT_OWNER TEXT,
            PKG_PN TEXT,
            RELEASE_TIME TEXT,
            SHIP_TIME TEXT,
            SHIP_ORDER_ID TEXT,
            SHIP_FAC_ID TEXT,
            CREATE_LOT_QTY TEXT,
            CREATE_SUB_QTY TEXT,
            ROOT_LOT_QTY TEXT,
            TRACK_CARD_ID TEXT,
            DBP_ID TEXT,
            CJOB_ID TEXT,
            PROC_CNT TEXT,
            RETEST_YN TEXT,
            DUT_ID TEXT,
            EVENT TEXT,
            EVENT_KEY TEXT,
            EVENT_TIME TEXT,
            EVENT_USER TEXT,
            EVENT_MSG TEXT,
            CREATE_TIME TEXT,
            CREATE_USER TEXT,
            STR_FLAG TEXT,
            MAIN_EQP_ID TEXT,
            AUXILIARY_EQP_ID TEXT,
            CONTAINER_ID TEXT,
            CHIP_ID TEXT,
            ACT_QTY TEXT,
            TEST_SPEC_ID TEXT,
            TEST_SPEC_NAME TEXT,
            TEST_SPEC_VER TEXT,
            ORT_QTY TEXT,
            OA_FLAG TEXT,
            DEVICE TEXT,
            WAREHOUSE_CONTAINER_ID TEXT,
            PROD_THICKNESS TEXT,
            IQC_QTY TEXT,
            UPH TEXT,
            SEAL_FLAG TEXT,
            PACK_SPEC_ID TEXT,
            PACK_SPEC_VER TEXT,
            FULL_INSP_QC TEXT,
            SPLIT_TYPE TEXT,
            WH_LOCATION_NO TEXT,
            RELEASE_HOLD_TYPE TEXT,
            DATA_CONFIRM_HOLD_YN TEXT,
            ORT_SAMP_QTY TEXT,
            IQC_SAMP_QTY TEXT,
            LOCATION TEXT,
            RETEST_FLOW_ID TEXT,
            HALF_LOT_HOLD TEXT,
            MERGE_LOT_ID TEXT,
            STRM_QTY TEXT,
            STRM_SAMP_QTY TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'et_wait_lot': '''
        CREATE TABLE IF NOT EXISTS et_wait_lot (
            id INT AUTO_INCREMENT PRIMARY KEY,
            LOT_ID TEXT,
            LOT_TYPE TEXT,
            GOOD_QTY TEXT,
            PROD_ID TEXT,
            DEVICE TEXT,
            CHIP_ID TEXT,
            PKG_PN TEXT,
            PO_ID TEXT,
            STAGE TEXT,
            WIP_STATE TEXT,
            PROC_STATE TEXT,
            HOLD_STATE TEXT,
            FLOW_ID TEXT,
            FLOW_VER TEXT,
            RELEASE_TIME TEXT,
            FAC_ID TEXT,
            CREATE_TIME TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'et_uph_eqp': '''
        CREATE TABLE IF NOT EXISTS et_uph_eqp (
            id INT AUTO_INCREMENT PRIMARY KEY,
            DEVICE TEXT,
            PKG_PN TEXT,
            STAGE TEXT,
            UPH TEXT,
            HANDLER TEXT,
            FAC_ID TEXT,
            EDIT_STATE TEXT,
            EDIT_TIME TEXT,
            EDIT_USER TEXT,
            EVENT TEXT,
            EVENT_KEY TEXT,
            EVENT_TIME TEXT,
            EVENT_USER TEXT,
            EVENT_MSG TEXT,
            CREATE_TIME TEXT,
            CREATE_USER TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'eqp_status': '''
        CREATE TABLE IF NOT EXISTS eqp_status (
            id INT AUTO_INCREMENT PRIMARY KEY,
            HANDLER_ID TEXT,
            HANDLER_TYPE TEXT,
            TESTER_ID TEXT,
            HANDLER_CONFIG TEXT,
            SOCKET_PN TEXT,
            KIT_PN TEXT,
            EQP_CLASS TEXT,
            EQP_TYPE TEXT,
            TEMPERATURE_RANGE TEXT,
            TEMPERATURE_CAPACITY TEXT,
            LOT_ID TEXT,
            DEVICE TEXT,
            STATUS TEXT,
            HB_PN TEXT,
            TB_PN TEXT,
            TESTER_CONFIG TEXT,
            STAGE TEXT,
            EVENT_TIME TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
        
        'tcc_inv': '''
        CREATE TABLE IF NOT EXISTS tcc_inv (
            id INT AUTO_INCREMENT PRIMARY KEY,
            unnamed_0 TEXT,
            硬件编码 TEXT,
            关键硬件 TEXT,
            图片 TEXT,
            寿命状态 TEXT,
            仓库 TEXT,
            初始库位 TEXT,
            当前储位1 TEXT,
            当前储位2 TEXT,
            责任人 TEXT,
            周期消耗数 TEXT,
            当前库位 TEXT,
            封装形式 TEXT,
            状态 TEXT,
            类别 TEXT,
            设备机型 TEXT,
            寄放方 TEXT,
            备注_状态_shipout信息_ TEXT,
            类型 TEXT,
            状态_1 TEXT,
            操作 TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci''',
    }
    
    # 创建核心业务表
    for table_name, create_sql in business_tables.items():
        try:
            cursor.execute(create_sql)
            logger.info(f"✅ 核心业务表 '{table_name}' 创建成功")
        except Exception as e:
            logger.error(f"❌ 创建核心业务表 '{table_name}' 失败: {e}")
            raise
    
    cursor.close()
    connection.close()

def clear_business_data_tables():
    """清空业务数据表 - 只清空指定的13个表，保留其他表数据"""
    logger.info("🧹 清空业务数据表（保留系统配置）...")
    
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    # 定义需要清空的13个表（与clear_tables_data.py保持一致）
    tables_to_clear = {
        '基础数据表': [
            'et_wait_lot',      # 等待批次表
            'et_uph_eqp',       # UPH设备表
            'et_recipe_file',   # 配方文件表
            'et_ft_test_spec',  # 测试规范表
            'ct',               # CT数据表
            'wip_lot',          # WIP批次表
            'eqp_status'        # 设备状态表
        ],
        'Done Lots页面相关表': [
            'lotprioritydone',          # 已排产批次表（主要数据源）
            'final_scheduling_result',  # 最终排产调整结果表
            'scheduling_sessions',      # 排产调整会话管理表
            'adjustment_operations'     # 调整操作历史表
        ],
        'Failed Lots页面相关表': [
            'scheduling_failed_lots'    # 排产失败记录表
        ],
        '统一管理相关表': [
            'unified_lot_management'    # 统一批次管理表
        ]
    }
    
    cleared_count = 0
    total_tables = sum(len(tables) for tables in tables_to_clear.values())
    
    try:
        for group_name, tables in tables_to_clear.items():
            logger.info(f"🔄 清空 {group_name}...")
            for table_name in tables:
                try:
                    # 检查表是否存在
                    cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if cursor.fetchone():
                        # 获取清空前的记录数
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        record_count = cursor.fetchone()[0]
                        
                        if record_count > 0:
                            # 使用TRUNCATE TABLE清空数据（保留表结构）
                            cursor.execute(f"TRUNCATE TABLE {table_name}")
                            logger.info(f"✅ 已清空 {table_name} ({record_count:,} 条记录)")
                        else:
                            logger.info(f"⏭️ 跳过空表 {table_name}")
                        cleared_count += 1
                    else:
                        logger.warning(f"⚠️ 表 {table_name} 不存在，跳过")
                except Exception as e:
                    # 如果TRUNCATE失败，尝试使用DELETE
                    try:
                        cursor.execute(f"DELETE FROM {table_name}")
                        logger.info(f"✅ 已清空 {table_name} (使用DELETE)")
                        cleared_count += 1
                    except Exception as e2:
                        logger.error(f"❌ 清空表 {table_name} 失败: {e2}")
        
        logger.info(f"✅ 业务数据清空完成: {cleared_count}/{total_tables} 个表")
        
    except Exception as e:
        logger.error(f"❌ 清空业务数据失败: {e}")
        raise
    finally:
        cursor.close()
        connection.close()

def insert_default_data():
    """插入默认数据和配置"""
    logger.info("🔧 插入初始数据...")
    
    # 插入系统设置 (aps - 单数据库模式)
    connection = get_mysql_connection('aps')
    cursor = connection.cursor()
    
    try:
        # 1. 插入默认用户 (admin)
        logger.info("👤 创建默认管理员账户...")
        from werkzeug.security import generate_password_hash
        admin_password = generate_password_hash('admin')
        
        cursor.execute("""
            INSERT IGNORE INTO users (username, email, password_hash, role, is_active) 
            VALUES ('admin', '<EMAIL>', %s, 'admin', 1)
        """, (admin_password,))
        
        # 2. 插入调度器配置
        logger.info("⚙️ 配置调度器设置...")
        scheduler_configs = [
            ('timezone', 'Asia/Shanghai', '系统时区配置'),
            ('max_workers', '4', '最大工作线程数'),
            ('job_defaults.coalesce', 'false', '任务合并配置'),
            ('job_defaults.max_instances', '1', '任务最大实例数')
        ]
        
        for key, value, desc in scheduler_configs:
            cursor.execute("""
                INSERT IGNORE INTO scheduler_config (`key`, value, description) 
                VALUES (%s, %s, %s)
            """, (key, value, desc))
        
        # 3. 插入系统设置
        logger.info("🔧 配置系统设置...")
        default_settings = [
            ('system_version', '3.0', '系统版本', None, 'string'),
            ('database_type', 'mysql', '数据库类型', None, 'string'),
            ('init_completed', 'true', '初始化完成标记', None, 'boolean')
        ]
        
        for key, value, description, user_id, setting_type in default_settings:
            cursor.execute("""
                INSERT IGNORE INTO system_settings 
                (`key`, value, description, user_id, setting_type) 
                VALUES (%s, %s, %s, %s, %s)
            """, (key, value, description, user_id, setting_type))
        
        # 4. 插入AI设置
        cursor.execute("SELECT COUNT(*) FROM ai_settings WHERE id = 1")
        if cursor.fetchone()[0] == 0:
            default_ai_settings = {
                "database": {
                    "enabled": True,
                    "type": "mysql",
                    "prioritize_database": True
                },
                "features": {
                    "intelligent_scheduling": True,
                    "resource_management": True,
                    "priority_optimization": True
                }
            }
            cursor.execute("""
                INSERT INTO ai_settings (id, settings) VALUES (%s, %s)
            """, (1, json.dumps(default_ai_settings, ensure_ascii=False)))
            logger.info("✅ AI设置配置完成")
        
        logger.info("✅ 系统初始数据插入完成")
        
    except Exception as e:
        logger.error(f"❌ 插入初始数据失败: {e}")
        raise
    finally:
        cursor.close()
        connection.close()

def main():
    """主初始化函数"""
    print("🚀 APS 车规芯片终测智能调度平台 - 数据库初始化 v3.1")
    print("=" * 60)
    print("📊 目标: 创建与真实数据库结构一致的表")
    print("🔧 包含: 核心系统表 + 核心业务表")
    print("🧹 特性: 智能选择性数据清空（保留系统配置）")
    print("=" * 60)
    
    try:
        # 检查MySQL连接
        logger.info("🔍 检查MySQL服务器连接...")
        try:
            test_conn = get_mysql_connection()
            test_conn.close()
            logger.info("✅ MySQL服务器连接正常")
        except Exception as e:
            logger.error(f"❌ MySQL服务器连接失败: {e}")
            print("\n💡 解决建议:")
            print("1. 确保MySQL服务正在运行")
            print("2. 检查用户名密码: root / WWWwww123!")
            print("3. 确认端口3306可访问")
            return False
        
        # 1. 创建数据库
        logger.info("📋 步骤1/5: 创建MySQL数据库...")
        create_databases()
        
        # 2. 初始化核心系统表
        logger.info("📋 步骤2/5: 创建核心系统表...")
        init_core_system_tables()
        
        # 3. 初始化核心业务表
        logger.info("📋 步骤3/5: 创建核心业务表...")
        init_business_tables()
        
        # 4. 清空业务数据表（保留系统配置）
        logger.info("📋 步骤4/5: 清空业务数据表...")
        clear_business_data_tables()
        
        # 5. 插入默认数据
        logger.info("📋 步骤5/5: 插入初始数据...")
        insert_default_data()
        
        print("\n" + "="*60)
        print("🎉 APS 数据库初始化完成！")
        print("="*60)
        print("📊 数据库状态:")
        print("   ✅ 统一数据库 (aps): 已创建，结构与真实数据库一致")
        print("   ✅ 核心系统表: users, scheduler_config, system_settings等")
        print("   ✅ 核心业务表: ct, wip_lot, et_wait_lot, eqp_status等")
        print("   🧹 业务数据清空: 仅清空13个指定表，保留其他数据")
        print("   ✅ 默认管理员: admin / admin")
        print("\n🚀 现在可以启动应用:")
        print("   python run.py")
        print("\n🌐 Web访问地址:")
        print("   http://localhost:5000")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库初始化失败: {e}")
        print(f"\n💥 初始化失败: {e}")
        print("\n🔧 故障排除:")
        print("1. 检查MySQL服务状态")
        print("2. 验证数据库连接配置")
        print("3. 确认有足够的权限创建数据库")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 