#!/usr/bin/env python3
"""
排产失败跟踪器
用于记录和分析排产失败的批次信息
"""

import mysql.connector
from mysql.connector import Error
from datetime import datetime
import json
import logging
from typing import Dict, List, Optional

# 配置日志
logger = logging.getLogger(__name__)

class SchedulingFailureTracker:
    """排产失败跟踪器"""
    
    def __init__(self):
        self.failed_lots = []
        self.success_lots = []
        # 使用统一配置读取，避免硬编码localhost
        self.db_config = self._get_database_config()
    
    def _get_database_config(self) -> Dict:
        """获取数据库配置，优先级：外部配置文件 > 环境变量 > 默认值"""
        try:
            # 尝试从外部配置文件读取
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            from app.utils.config_reader import get_database_config
            
            external_config = get_database_config()
            return {
                'host': external_config['host'],
                'port': external_config['port'],
                'user': external_config['user'],
                'password': external_config['password'],
                'database': external_config.get('database', 'aps'),
                'charset': 'utf8mb4'
            }
        except Exception as e:
            # 回退到环境变量或默认配置
            import os
            return {
                'host': os.environ.get('MYSQL_HOST', os.environ.get('DB_HOST', 'localhost')),
                'port': int(os.environ.get('MYSQL_PORT', os.environ.get('DB_PORT', 3306))),
                'user': os.environ.get('MYSQL_USER', os.environ.get('DB_USER', 'root')),
                'password': os.environ.get('MYSQL_PASSWORD', os.environ.get('DB_PASSWORD', 'WWWwww123!')),
                'database': os.environ.get('MYSQL_DATABASE', os.environ.get('DB_NAME', 'aps')),
                'charset': 'utf8mb4'
            }
    
    def add_failed_lot(self, lot: Dict, failure_reason: str, failure_details: str = "", algorithm_version: str = "v2.0", session_id: str = None):
        """添加失败批次记录"""
        try:
            # 如果没有提供session_id，生成一个基于时间戳的ID
            if not session_id:
                session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 🔥 高优先级任务2：生成包含历史推荐的建议解决方案
            device = lot.get('DEVICE', '')
            stage = lot.get('STAGE', '')
            suggestion = self._generate_enhanced_suggestion(failure_reason, failure_details, device, stage)

            failed_lot = {
                'lot_id': lot.get('LOT_ID', ''),
                'device': device,
                'stage': stage,
                'good_qty': lot.get('GOOD_QTY', 0),
                'failure_reason': failure_reason,
                'failure_details': failure_details,
                'suggestion': suggestion,  # 🔥 新增：包含历史推荐的建议
                'algorithm_version': algorithm_version,
                'session_id': session_id,  # 🔥 新增会话ID
                'execution_context': json.dumps({
                    'pkg_pn': lot.get('PKG_PN', ''),
                    'chip_id': lot.get('CHIP_ID', ''),
                    'lot_type': lot.get('LOT_TYPE', ''),
                    'wip_state': lot.get('WIP_STATE', ''),
                    'proc_state': lot.get('PROC_STATE', ''),
                    'create_time': lot.get('CREATE_TIME', ''),
                    'timestamp': datetime.now().isoformat(),
                    'session_id': session_id
                }),
                'timestamp': datetime.now()
            }
            self.failed_lots.append(failed_lot)
            logger.warning(f"❌ 批次排产失败: {failed_lot['lot_id']} - {failure_reason} (会话:{session_id})")
        except Exception as e:
            logger.error(f"记录失败批次时出错: {e}")

    def _generate_enhanced_suggestion(self, failure_reason: str, failure_details: str, device: str = None, stage: str = None) -> str:
        """
        🔥 高优先级任务2：生成包含历史推荐的增强建议解决方案
        """
        try:
            # 基础建议
            base_suggestion = ""

            if "配置需求获取失败" in failure_reason:
                base_suggestion = "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
            elif "无合适设备" in failure_reason:
                base_suggestion = "请检查设备状态和配置匹配，确保有可用的设备"
            elif "设备ID无效" in failure_reason:
                base_suggestion = "请检查设备配置，确保HANDLER_ID字段正确"
            elif "算法执行异常" in failure_reason:
                base_suggestion = "请检查系统日志，可能需要技术支持"
            elif "测试规范缺失" in failure_reason:
                base_suggestion = "请在ET_FT_TEST_SPEC表中补充对应器件和工序的测试规范"
            elif "配置" in failure_reason or "config" in failure_reason.lower():
                base_suggestion = "请检查配置设置和参数是否正确"
            elif "设备" in failure_reason or "不兼容" in failure_reason:
                base_suggestion = "请检查设备兼容性和状态"
            else:
                base_suggestion = "请联系技术支持进行详细分析"

            # 🔥 新增：基于历史生产记录的设备推荐
            historical_suggestion = self._get_historical_equipment_recommendation(device, stage)

            if historical_suggestion:
                return f"{base_suggestion}。{historical_suggestion}"
            else:
                return base_suggestion

        except Exception as e:
            logger.error(f"生成增强建议失败: {e}")
            return "请联系技术支持进行详细分析"

    def _get_historical_equipment_recommendation(self, device: str, stage: str) -> str:
        """
        🔥 高优先级任务2：基于历史生产记录的设备推荐
        """
        if not device or not stage:
            return None

        try:
            from datetime import timedelta

            # 计算6个月前的时间
            six_months_ago = datetime.now() - timedelta(days=180)

            connection = mysql.connector.connect(**self.db_config)
            if connection.is_connected():
                cursor = connection.cursor(dictionary=True)

                # 查询历史生产记录 - 使用ct表的AUXILIARY_EQP_ID字段（分选机编号）
                query = """
                    SELECT AUXILIARY_EQP_ID as HANDLER_ID, COUNT(*) as production_count,
                           MAX(CREATE_TIME) as last_production_time,
                           MIN(CREATE_TIME) as first_production_time
                    FROM ct
                    WHERE DEVICE = %s AND STAGE = %s
                    AND CREATE_TIME >= %s
                    AND AUXILIARY_EQP_ID IS NOT NULL AND AUXILIARY_EQP_ID != ''
                    GROUP BY AUXILIARY_EQP_ID
                    ORDER BY MAX(CREATE_TIME) DESC, COUNT(*) DESC
                    LIMIT 3
                """

                cursor.execute(query, (device, stage, six_months_ago))
                historical_records = cursor.fetchall()

                if historical_records:
                    # 获取最新的推荐设备
                    top_record = historical_records[0]
                    handler_id = top_record['HANDLER_ID']
                    production_count = top_record['production_count']
                    last_time = top_record['last_production_time']

                    # 检查推荐设备的当前状态
                    status_query = """
                        SELECT STATUS, HANDLER_CONFIG, EQP_CLASS
                        FROM eqp_status
                        WHERE HANDLER_ID = %s
                    """

                    cursor.execute(status_query, (handler_id,))
                    status_record = cursor.fetchone()

                    if status_record:
                        status = status_record['STATUS']
                        available_statuses = ['IDLE', 'Run', 'Wait', '0', 'READY', 'ONLINE', 'SetupRun', '']
                        is_available = status in available_statuses

                        # 格式化时间显示
                        if isinstance(last_time, datetime):
                            time_str = last_time.strftime('%Y-%m-%d')
                        else:
                            time_str = str(last_time)[:10] if last_time else '未知'

                        availability_text = "可用" if is_available else f"当前状态: {status}"

                        cursor.close()
                        connection.close()

                        return f"💡 历史推荐: {handler_id} (最后生产: {time_str}, 历史成功: {production_count}次, {availability_text})"
                    else:
                        cursor.close()
                        connection.close()
                        return f"💡 历史推荐: {handler_id} (最后生产: {last_time}, 历史成功: {production_count}次, 设备状态未知)"

                cursor.close()
                connection.close()
                return None

        except Exception as e:
            logger.error(f"获取历史设备推荐失败: {e}")
            return None

    def add_success_lot(self, lot: Dict, handler_id: str):
        """添加成功批次记录"""
        try:
            success_lot = {
                'lot_id': lot.get('LOT_ID', ''),
                'device': lot.get('DEVICE', ''),
                'stage': lot.get('STAGE', ''),
                'handler_id': handler_id,
                'timestamp': datetime.now()
            }
            self.success_lots.append(success_lot)
            logger.info(f"✅ 批次排产成功: {success_lot['lot_id']} -> {handler_id}")
        except Exception as e:
            logger.error(f"记录成功批次时出错: {e}")
    
    def save_to_database(self):
        """保存失败批次到数据库"""
        if not self.failed_lots:
            logger.info("ℹ️ 没有失败批次需要保存")
            return
        
        try:
            connection = mysql.connector.connect(**self.db_config)
            if connection.is_connected():
                cursor = connection.cursor()
                
                # 确保表存在
                self._ensure_table_exists(cursor)
                
                # 批量插入失败批次
                insert_sql = """
                INSERT INTO scheduling_failed_lots
                (lot_id, device, stage, good_qty, failure_reason, failure_details,
                 suggestion, algorithm_version, session_id, execution_context, timestamp)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                failed_records = []
                for lot in self.failed_lots:
                    failed_records.append((
                        lot['lot_id'],
                        lot['device'],
                        lot['stage'],
                        lot['good_qty'],
                        lot['failure_reason'],
                        lot['failure_details'],
                        lot.get('suggestion', '请联系技术支持进行详细分析'),  # 🔥 新增建议字段
                        lot['algorithm_version'],
                        lot['session_id'],  # 🔥 新增会话ID
                        lot['execution_context'],
                        lot['timestamp']
                    ))
                
                cursor.executemany(insert_sql, failed_records)
                connection.commit()
                
                logger.info(f"✅ 已保存 {len(failed_records)} 条失败批次记录到数据库")
                
                cursor.close()
                connection.close()
                
        except Error as e:
            logger.error(f"保存失败批次到数据库时出错: {e}")
        except Exception as e:
            logger.error(f"保存失败批次时发生异常: {e}")
    
    def _ensure_table_exists(self, cursor):
        """确保scheduling_failed_lots表存在"""
        try:
            # 检查表是否存在
            cursor.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE() 
                AND table_name = 'scheduling_failed_lots'
            """)
            
            table_exists = cursor.fetchone()[0] > 0
            
            if not table_exists:
                # 创建表
                create_table_sql = """
                CREATE TABLE scheduling_failed_lots (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    lot_id VARCHAR(50) NOT NULL COMMENT '批次ID',
                    device VARCHAR(100) NOT NULL COMMENT '设备名称',
                    stage VARCHAR(50) NOT NULL COMMENT '工序',
                    good_qty INT DEFAULT 0 COMMENT '良品数量',
                    failure_reason VARCHAR(500) NOT NULL COMMENT '失败原因',
                    failure_details TEXT COMMENT '失败详情',
                    suggestion TEXT COMMENT '建议解决方案',
                    algorithm_version VARCHAR(50) COMMENT '算法版本',
                    execution_context JSON COMMENT '执行上下文',
                    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失败时间',
                    
                    INDEX idx_lot_id (lot_id),
                    INDEX idx_device (device),
                    INDEX idx_stage (stage),
                    INDEX idx_timestamp (timestamp),
                    INDEX idx_failure_reason (failure_reason)
                    
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci 
                COMMENT='排产失败批次记录表'
                """
                
                cursor.execute(create_table_sql)
                logger.info("✅ 创建scheduling_failed_lots表成功")
                
        except Error as e:
            logger.error(f"检查/创建表时出错: {e}")
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        total_lots = len(self.failed_lots) + len(self.success_lots)
        failed_lots = len(self.failed_lots)
        success_lots = len(self.success_lots)
        
        success_rate = (success_lots / total_lots * 100) if total_lots > 0 else 0
        
        # 统计失败原因
        failure_reasons = {}
        for lot in self.failed_lots:
            reason = lot['failure_reason']
            failure_reasons[reason] = failure_reasons.get(reason, 0) + 1
        
        return {
            'total_lots': total_lots,
            'failed_lots': failed_lots,
            'success_lots': success_lots,
            'success_rate': round(success_rate, 1),
            'failure_reasons': failure_reasons
        }
    
    def get_failed_lots(self) -> List[Dict]:
        """获取失败批次列表"""
        return self.failed_lots.copy()
    
    def get_success_lots(self) -> List[Dict]:
        """获取成功批次列表"""
        return self.success_lots.copy()
    
    def clear(self):
        """清空记录"""
        self.failed_lots.clear()
        self.success_lots.clear()
        logger.info("📋 已清空失败批次跟踪记录")

# 为了向后兼容，提供一个简单的工厂函数
def create_failure_tracker():
    """创建失败跟踪器实例"""
    return SchedulingFailureTracker()

if __name__ == "__main__":
    # 测试代码
    tracker = SchedulingFailureTracker()
    
    # 模拟失败批次
    test_lot = {
        'LOT_ID': 'TEST_LOT_001',
        'DEVICE': 'TEST_DEVICE',
        'STAGE': 'COLD-FT',
        'GOOD_QTY': 100,
        'PKG_PN': 'TEST_PKG',
        'CHIP_ID': 'TEST_CHIP'
    }
    
    tracker.add_failed_lot(test_lot, "测试失败原因", "测试详细信息")
    
    # 模拟成功批次
    tracker.add_success_lot(test_lot, "TEST_HANDLER_001")
    
    # 获取统计信息
    stats = tracker.get_statistics()
    print(f"统计信息: {stats}")
    
    # 保存到数据库
    tracker.save_to_database()
    
    print("测试完成") 