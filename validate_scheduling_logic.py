#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排产逻辑验证脚本
验证真实排产结果是否符合设定的排产逻辑规则
"""

import logging
from typing import Dict, List, Tuple
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.utils.db_helper import get_mysql_connection
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduling_validation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SchedulingLogicValidator:
    """排产逻辑验证器"""
    
    def __init__(self):
        self.match_rules = {
            'same_setup': {
                'name': '同设置匹配',
                'score': 100,
                'changeover_time': 0,
                'description': '同设置：KIT_PN + TB_PN + HB_PN相同'
            },
            'small_change': {
                'name': '小改机匹配', 
                'score': 80,
                'changeover_time': 45,
                'description': '小改机：KIT_PN相同'
            },
            'big_change': {
                'name': '大改机匹配',
                'score': 60,
                'changeover_time': 120,
                'description': '大改机：HANDLER_CONFIG相同'
            }
        }
        
        self.validation_results = {
            'total_checked': 0,
            'correct_matches': 0,
            'incorrect_matches': 0,
            'missing_data': 0,
            'details': []
        }
    
    def get_scheduling_results(self) -> List[Dict]:
        """获取排产结果数据"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            query = """
            SELECT 
                HANDLER_ID, LOT_ID, DEVICE, STAGE, PKG_PN, CHIP_ID,
                PRIORITY, match_type, changeover_time, comprehensive_score
            FROM lotprioritydone 
            WHERE HANDLER_ID IS NOT NULL 
            AND LOT_ID IS NOT NULL
            ORDER BY PRIORITY DESC, HANDLER_ID
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            # 转换为字典格式
            columns = ['HANDLER_ID', 'LOT_ID', 'DEVICE', 'STAGE', 'PKG_PN', 'CHIP_ID',
                      'PRIORITY', 'match_type', 'changeover_time', 'comprehensive_score']
            
            scheduling_data = []
            for row in results:
                record = dict(zip(columns, row))
                scheduling_data.append(record)
            
            cursor.close()
            conn.close()
            
            logger.info(f"📋 获取到 {len(scheduling_data)} 条排产结果")
            return scheduling_data
            
        except Exception as e:
            logger.error(f"获取排产结果失败: {e}")
            return []
    
    def get_test_specs(self) -> Dict[str, Dict]:
        """获取测试规范数据（包含Recipe文件的KIT_PN和HANDLER_CONFIG）"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 首先获取基础测试规范
            query = """
            SELECT DEVICE, STAGE, PKG_PN, CHIP_ID, HANDLER as EQP_CLASS,
                   HB_PN, TB_PN
            FROM ET_FT_TEST_SPEC
            WHERE DEVICE IS NOT NULL AND STAGE IS NOT NULL
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            test_specs = {}
            for row in results:
                device, stage, pkg_pn, chip_id, eqp_class, hb_pn, tb_pn = row
                key = f"{device}_{stage}_{pkg_pn or ''}_{chip_id or ''}"
                test_specs[key] = {
                    'DEVICE': device,
                    'STAGE': stage,
                    'PKG_PN': pkg_pn,
                    'CHIP_ID': chip_id,
                    'EQP_CLASS': eqp_class,
                    'KIT_PN': None,
                    'HB_PN': hb_pn,
                    'TB_PN': tb_pn,
                    'HANDLER_CONFIG': None
                }
            
            # 尝试从Recipe文件获取KIT_PN和HANDLER_CONFIG
            try:
                recipe_query = """
                SELECT DEVICE, STAGE, PKG_PN, KIT_PN, HANDLER_CONFIG
                FROM ET_RECIPE_FILE
                WHERE DEVICE IS NOT NULL AND STAGE IS NOT NULL 
                AND APPROVAL_STATE = 'Released'
                """
                cursor.execute(recipe_query)
                recipe_results = cursor.fetchall()
                
                # 更新test_specs添加Recipe信息
                for row in recipe_results:
                    device, stage, pkg_pn, kit_pn, handler_config = row
                    key = f"{device}_{stage}_{pkg_pn or ''}_"
                    
                    # 更新现有规范或创建新的
                    matching_keys = [k for k in test_specs.keys() if k.startswith(key)]
                    for match_key in matching_keys:
                        test_specs[match_key]['KIT_PN'] = kit_pn
                        test_specs[match_key]['HANDLER_CONFIG'] = handler_config
                        
                logger.info(f"📋 从Recipe文件补充了 {len(recipe_results)} 条配置信息")
                        
            except Exception as recipe_error:
                logger.warning(f"获取Recipe配置失败，跳过: {recipe_error}")
            
            cursor.close()
            conn.close()
            
            logger.info(f"📋 获取到 {len(test_specs)} 条测试规范")
            return test_specs
            
        except Exception as e:
            logger.error(f"获取测试规范失败: {e}")
            return {}
    
    def get_equipment_status(self) -> Dict[str, Dict]:
        """获取设备状态数据"""
        try:
            conn = get_mysql_connection()
            cursor = conn.cursor()
            
            # 根据实际表结构修正字段名
            query = """
            SELECT HANDLER_ID, STATUS, KIT_PN, HB_PN, TB_PN, 
                   HANDLER_CONFIG, EQP_CLASS
            FROM eqp_status
            WHERE HANDLER_ID IS NOT NULL
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            equipment = {}
            for row in results:
                handler_id, status, kit_pn, hb_pn, tb_pn, handler_config, eqp_class = row
                equipment[handler_id] = {
                    'HANDLER_ID': handler_id,
                    'STATUS': status,
                    'KIT_PN': kit_pn,
                    'HB_PN': hb_pn,
                    'TB_PN': tb_pn,
                    'HANDLER_CONFIG': handler_config,
                    'EQP_CLASS': eqp_class
                }
            
            cursor.close()
            conn.close()
            
            logger.info(f"📋 获取到 {len(equipment)} 台设备")
            return equipment
            
        except Exception as e:
            logger.error(f"获取设备状态失败: {e}")
            return {}
    
    def calculate_expected_match(self, lot_requirements: Dict, equipment: Dict) -> Tuple[str, int]:
        """计算预期的匹配结果"""
        try:
            # 提取批次需求配置
            req_kit = (lot_requirements.get('KIT_PN') or '').strip()
            req_hb = (lot_requirements.get('HB_PN') or '').strip()
            req_tb = (lot_requirements.get('TB_PN') or '').strip()
            req_handler_config = (lot_requirements.get('HANDLER_CONFIG') or '').strip()
            req_eqp_class = (lot_requirements.get('EQP_CLASS') or '').strip()
            
            # 提取设备配置
            eqp_kit = (equipment.get('KIT_PN') or '').strip()
            eqp_hb = (equipment.get('HB_PN') or '').strip()
            eqp_tb = (equipment.get('TB_PN') or '').strip()
            eqp_handler_config = (equipment.get('HANDLER_CONFIG') or '').strip()
            eqp_class = (equipment.get('EQP_CLASS') or '').strip()
            
            # 1. 同设置匹配检查 - KIT_PN + TB_PN + HB_PN完全匹配
            if (req_kit and eqp_kit and req_kit == eqp_kit and
                req_hb and eqp_hb and req_hb == eqp_hb and
                req_tb and eqp_tb and req_tb == eqp_tb):
                return '同设置匹配', 0
            
            # 2. 小改机匹配检查 - KIT_PN相同（基于EQP_CLASS兼容性）
            if req_kit and eqp_kit and req_kit == eqp_kit:
                return '小改机匹配', 45
            elif req_eqp_class and eqp_class and req_eqp_class == eqp_class:
                # 如果没有KIT_PN但EQP_CLASS匹配，也认为是小改机
                return '小改机匹配', 45
            
            # 3. 大改机匹配检查 - HANDLER_CONFIG相同
            if (req_handler_config and eqp_handler_config and 
                req_handler_config.upper() == eqp_handler_config.upper()):
                return '大改机匹配', 120
            
            # 4. 简化判断：如果有任何配置信息但不完全匹配，可能是兼容的
            if (req_kit or req_hb or req_tb or req_handler_config) and (eqp_kit or eqp_hb or eqp_tb or eqp_handler_config):
                # 如果都有配置但不匹配任何规则，认为不兼容
                return '不兼容', 9999
            else:
                # 如果缺少配置信息，使用默认处理
                return '配置缺失', 60
            
        except Exception as e:
            logger.error(f"计算预期匹配失败: {e}")
            return '计算错误', -1
    
    def validate_single_result(self, result: Dict, test_specs: Dict, equipment: Dict) -> Dict:
        """验证单个排产结果"""
        lot_id = result['LOT_ID']
        handler_id = result['HANDLER_ID']
        actual_match_type = result.get('match_type', '未知')
        actual_changeover_time = result.get('changeover_time', 0)
        
        # 查找批次需求
        device = result['DEVICE']
        stage = result['STAGE'] 
        pkg_pn = result.get('PKG_PN', '')
        chip_id = result.get('CHIP_ID', '')
        
        # 构建测试规范查找键
        spec_keys = [
            f"{device}_{stage}_{pkg_pn}_{chip_id}",
            f"{device}_{stage}_{pkg_pn}_",
            f"{device}_{stage}__{chip_id}",
            f"{device}_{stage}__"
        ]
        
        lot_requirements = None
        for key in spec_keys:
            if key in test_specs:
                lot_requirements = test_specs[key]
                break
        
        if not lot_requirements:
            return {
                'lot_id': lot_id,
                'handler_id': handler_id,
                'status': 'missing_spec',
                'message': f'未找到测试规范: {device}_{stage}_{pkg_pn}_{chip_id}'
            }
        
        # 查找设备信息
        if handler_id not in equipment:
            return {
                'lot_id': lot_id,
                'handler_id': handler_id,
                'status': 'missing_equipment',
                'message': f'未找到设备信息: {handler_id}'
            }
        
        equipment_info = equipment[handler_id]
        
        # 计算预期匹配
        expected_match_type, expected_changeover_time = self.calculate_expected_match(
            lot_requirements, equipment_info
        )
        
        # 验证匹配类型
        match_correct = False
        if actual_match_type and expected_match_type:
            # 简化匹配类型比较
            actual_simple = actual_match_type.replace('匹配', '').strip()
            expected_simple = expected_match_type.replace('匹配', '').strip()
            match_correct = actual_simple == expected_simple
        
        # 验证改机时间（处理类型转换）
        time_correct = False
        try:
            actual_time = float(actual_changeover_time) if actual_changeover_time is not None else 0
            expected_time = float(expected_changeover_time) if expected_changeover_time >= 0 else 0
            time_correct = abs(actual_time - expected_time) <= 5  # 允许5分钟误差
        except (ValueError, TypeError):
            time_correct = False
        
        validation_status = 'correct' if match_correct and time_correct else 'incorrect'
        
        return {
            'lot_id': lot_id,
            'handler_id': handler_id,
            'status': validation_status,
            'actual_match_type': actual_match_type,
            'expected_match_type': expected_match_type,
            'actual_changeover_time': actual_changeover_time,
            'expected_changeover_time': expected_changeover_time,
            'match_correct': match_correct,
            'time_correct': time_correct,
            'lot_requirements': {
                'KIT_PN': lot_requirements.get('KIT_PN'),
                'HB_PN': lot_requirements.get('HB_PN'),
                'TB_PN': lot_requirements.get('TB_PN'),
                'HANDLER_CONFIG': lot_requirements.get('HANDLER_CONFIG')
            },
            'equipment_config': {
                'KIT_PN': equipment_info.get('KIT_PN'),
                'HB_PN': equipment_info.get('HB_PN'),
                'TB_PN': equipment_info.get('TB_PN'),
                'HANDLER_CONFIG': equipment_info.get('HANDLER_CONFIG')
            }
        }
    
    def run_validation(self) -> Dict:
        """执行完整验证"""
        logger.info("🔍 开始排产逻辑验证...")
        
        # 获取数据
        scheduling_results = self.get_scheduling_results()
        if not scheduling_results:
            logger.error("无法获取排产结果，验证终止")
            return self.validation_results
        
        test_specs = self.get_test_specs()
        equipment = self.get_equipment_status()
        
        # 验证每个结果
        for result in scheduling_results:
            validation = self.validate_single_result(result, test_specs, equipment)
            
            self.validation_results['total_checked'] += 1
            
            if validation['status'] == 'correct':
                self.validation_results['correct_matches'] += 1
            elif validation['status'] == 'incorrect':
                self.validation_results['incorrect_matches'] += 1
            else:
                self.validation_results['missing_data'] += 1
            
            self.validation_results['details'].append(validation)
        
        # 计算统计信息
        total = self.validation_results['total_checked']
        correct = self.validation_results['correct_matches']
        incorrect = self.validation_results['incorrect_matches']
        missing = self.validation_results['missing_data']
        
        accuracy_rate = (correct / total * 100) if total > 0 else 0
        
        logger.info("✅ 验证完成!")
        logger.info(f"📊 验证统计:")
        logger.info(f"   总检查数: {total}")
        logger.info(f"   正确匹配: {correct} ({correct/total*100:.1f}%)")
        logger.info(f"   错误匹配: {incorrect} ({incorrect/total*100:.1f}%)")
        logger.info(f"   数据缺失: {missing} ({missing/total*100:.1f}%)")
        logger.info(f"   准确率: {accuracy_rate:.1f}%")
        
        return self.validation_results
    
    def generate_report(self, results: Dict):
        """生成详细报告"""
        report_file = f"scheduling_validation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("排产逻辑验证报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 统计信息
            total = results['total_checked']
            correct = results['correct_matches']
            incorrect = results['incorrect_matches']
            missing = results['missing_data']
            
            f.write("验证统计:\n")
            f.write(f"  总检查数: {total}\n")
            f.write(f"  正确匹配: {correct} ({correct/total*100:.1f}%)\n")
            f.write(f"  错误匹配: {incorrect} ({incorrect/total*100:.1f}%)\n")
            f.write(f"  数据缺失: {missing} ({missing/total*100:.1f}%)\n\n")
            
            # 错误详情
            f.write("错误详情:\n")
            f.write("-" * 30 + "\n")
            
            for detail in results['details']:
                if detail['status'] == 'incorrect':
                    f.write(f"批次: {detail['lot_id']}, 设备: {detail['handler_id']}\n")
                    f.write(f"  实际匹配: {detail['actual_match_type']} (改机{detail['actual_changeover_time']}分钟)\n")
                    f.write(f"  预期匹配: {detail['expected_match_type']} (改机{detail['expected_changeover_time']}分钟)\n")
                    f.write(f"  批次配置: {detail['lot_requirements']}\n")
                    f.write(f"  设备配置: {detail['equipment_config']}\n")
                    f.write("-" * 30 + "\n")
        
        logger.info(f"📋 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    try:
        validator = SchedulingLogicValidator()
        results = validator.run_validation()
        validator.generate_report(results)
        
        # 根据准确率返回退出码
        accuracy = results['correct_matches'] / results['total_checked'] * 100 if results['total_checked'] > 0 else 0
        
        if accuracy >= 90:
            logger.info("🎉 验证通过! 排产逻辑符合预期")
            return 0
        elif accuracy >= 80:
            logger.warning("⚠️ 验证基本通过，但存在一些问题需要关注")
            return 1
        else:
            logger.error("❌ 验证失败! 排产逻辑存在严重问题")
            return 2
            
    except Exception as e:
        logger.error(f"验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 3

if __name__ == "__main__":
    exit(main()) 