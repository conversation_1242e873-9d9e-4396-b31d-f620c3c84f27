#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复排产失败问题的脚本
主要解决测试规范未激活和设备状态问题
"""

import sys
import os
import json
from datetime import datetime, timedelta
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入数据库连接
from app.utils.db_helper import get_mysql_connection

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_test_spec_activation():
    """修复测试规范激活和审批状态"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        print("🔧 修复1: 激活和审批测试规范")
        print("=" * 60)
        
        # 先查看当前状态
        status_sql = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN ACTV_YN = 1 THEN 1 END) as active,
            COUNT(CASE WHEN APPROVAL_STATE = 1 THEN 1 END) as approved
        FROM et_ft_test_spec
        """
        cursor.execute(status_sql)
        before_stats = cursor.fetchone()
        
        if isinstance(before_stats, dict):
            total = before_stats['total']
            active_before = before_stats['active']
            approved_before = before_stats['approved']
        else:
            total, active_before, approved_before = before_stats
        
        print(f"修复前状态: 总数={total}, 激活={active_before}, 已审批={approved_before}")
        
        # 获取失败批次的DEVICE+STAGE组合
        failed_combinations_sql = """
        SELECT DISTINCT sfl.device, sfl.stage
        FROM scheduling_failed_lots sfl
        WHERE sfl.failure_reason = '配置需求获取失败'
        AND sfl.session_id = (
            SELECT session_id 
            FROM scheduling_failed_lots 
            ORDER BY timestamp DESC 
            LIMIT 1
        )
        """
        cursor.execute(failed_combinations_sql)
        failed_combinations = cursor.fetchall()
        
        print(f"需要修复的失败批次DEVICE+STAGE组合: {len(failed_combinations)}个")
        
        # 对于失败的组合，激活和审批对应的测试规范
        fixed_count = 0
        missing_combinations = []
        
        for combo in failed_combinations:
            if isinstance(combo, dict):
                device = combo['device']
                stage = combo['stage']
            else:
                device, stage = combo
            
            # 查找匹配的测试规范
            find_spec_sql = """
            SELECT TEST_SPEC_ID, TEST_SPEC_NAME, ACTV_YN, APPROVAL_STATE
            FROM et_ft_test_spec 
            WHERE DEVICE = %s AND STAGE = %s
            """
            cursor.execute(find_spec_sql, (device, stage))
            specs = cursor.fetchall()
            
            if specs:
                # 激活和审批找到的规范
                for spec in specs:
                    if isinstance(spec, dict):
                        spec_id = spec['TEST_SPEC_ID']
                        spec_name = spec['TEST_SPEC_NAME']
                        current_active = spec['ACTV_YN']
                        current_approved = spec['APPROVAL_STATE']
                    else:
                        spec_id, spec_name, current_active, current_approved = spec
                    
                    if not current_active or current_approved != 1:
                        update_sql = """
                        UPDATE et_ft_test_spec 
                        SET ACTV_YN = 1, 
                            APPROVAL_STATE = 1,
                            APPROVE_USER = 'admin',
                            APPROVE_TIME = NOW()
                        WHERE TEST_SPEC_ID = %s
                        """
                        cursor.execute(update_sql, (spec_id,))
                        fixed_count += 1
                        print(f"  ✅ 已修复: {device} + {stage} (规范ID: {spec_id})")
            else:
                missing_combinations.append((device, stage))
                print(f"  ❌ 缺失规范: {device} + {stage}")
        
        # 提交激活和审批的更改
        conn.commit()
        
        # 检查修复后的状态
        cursor.execute(status_sql)
        after_stats = cursor.fetchone()
        
        if isinstance(after_stats, dict):
            total_after = after_stats['total']
            active_after = after_stats['active']
            approved_after = after_stats['approved']
        else:
            total_after, active_after, approved_after = after_stats
        
        print(f"\n修复后状态: 总数={total_after}, 激活={active_after}, 已审批={approved_after}")
        print(f"本次修复了 {fixed_count} 个测试规范")
        
        # 如果还有缺失的组合，创建新的测试规范
        if missing_combinations:
            print(f"\n🔧 修复2: 创建缺失的测试规范 ({len(missing_combinations)}个)")
            print("=" * 60)
            
            created_count = 0
            for device, stage in missing_combinations:
                # 生成UUID
                import uuid
                spec_id = str(uuid.uuid4())
                spec_name = f"{device}_{stage}_测试规范"
                
                # 插入新的测试规范
                insert_sql = """
                INSERT INTO et_ft_test_spec (
                    TEST_SPEC_ID, TEST_SPEC_NAME, STAGE, DEVICE, 
                    ACTV_YN, APPROVAL_STATE, TEST_SPEC_TYPE,
                    CREATE_TIME, CREATE_USER, APPROVE_USER, APPROVE_TIME
                ) VALUES (
                    %s, %s, %s, %s,
                    1, 1, 'FT',
                    NOW(), 'admin', 'admin', NOW()
                )
                """
                try:
                    cursor.execute(insert_sql, (spec_id, spec_name, stage, device))
                    created_count += 1
                    print(f"  ✅ 已创建: {device} + {stage} (规范ID: {spec_id})")
                except Exception as e:
                    print(f"  ❌ 创建失败: {device} + {stage}, 错误: {e}")
            
            conn.commit()
            print(f"成功创建了 {created_count} 个新的测试规范")
        
        cursor.close()
        
        return fixed_count, len(missing_combinations) if missing_combinations else 0
        
    except Exception as e:
        logger.error(f"❌ 修复测试规范出错: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0

def fix_equipment_status():
    """修复设备状态问题"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        print(f"\n🔧 修复3: 检查和修复设备状态")
        print("=" * 60)
        
        # 查看当前设备状态
        eqp_status_sql = """
        SELECT 
            STAGE,
            COUNT(*) as total,
            COUNT(CASE WHEN STATUS = 'Run' THEN 1 END) as running,
            COUNT(CASE WHEN STATUS = 'IDLE' THEN 1 END) as idle,
            COUNT(CASE WHEN STATUS = 'DOWN' THEN 1 END) as down,
            COUNT(CASE WHEN STATUS = 'Wait' THEN 1 END) as wait
        FROM eqp_status
        GROUP BY STAGE
        ORDER BY STAGE
        """
        cursor.execute(eqp_status_sql)
        stage_stats = cursor.fetchall()
        
        print(f"各工序设备状态:")
        problematic_stages = []
        
        for stat in stage_stats:
            if isinstance(stat, dict):
                stage = stat['STAGE']
                total = stat['total']
                running = stat['running']
                idle = stat['idle']
                down = stat['down']
                wait = stat['wait']
            else:
                stage, total, running, idle, down, wait = stat
            
            available = running + idle
            availability = available / total * 100 if total > 0 else 0
            
            print(f"  {stage}: 总数={total}, 可用={available}({availability:.1f}%), 故障={down}")
            
            # 如果可用率低于50%，标记为有问题的工序
            if availability < 50:
                problematic_stages.append(stage)
        
        # 针对有问题的工序，尝试修复一些设备状态
        if problematic_stages:
            print(f"\n发现问题工序: {problematic_stages}")
            print(f"尝试将Wait状态的设备改为IDLE状态...")
            
            fixed_equipment = 0
            for stage in problematic_stages:
                update_wait_sql = """
                UPDATE eqp_status 
                SET STATUS = 'IDLE', EVENT_TIME = NOW()
                WHERE STAGE = %s AND STATUS = 'Wait'
                """
                cursor.execute(update_wait_sql, (stage,))
                affected = cursor.rowcount
                fixed_equipment += affected
                
                if affected > 0:
                    print(f"  ✅ {stage}工序: 将{affected}台Wait设备改为IDLE")
            
            conn.commit()
            print(f"总共修复了 {fixed_equipment} 台设备状态")
        
        cursor.close()
        return len(problematic_stages)
        
    except Exception as e:
        logger.error(f"❌ 修复设备状态出错: {e}")
        import traceback
        traceback.print_exc()
        return 0

def verify_fixes():
    """验证修复效果"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        print(f"\n🔍 验证修复效果")
        print("=" * 60)
        
        # 检查最新会话的失败批次
        latest_session_sql = """
        SELECT session_id 
        FROM scheduling_failed_lots 
        ORDER BY timestamp DESC 
        LIMIT 1
        """
        cursor.execute(latest_session_sql)
        result = cursor.fetchone()
        latest_session_id = result['session_id'] if isinstance(result, dict) else result[0]
        
        # 统计失败原因
        failure_stats_sql = """
        SELECT failure_reason, COUNT(*) as count
        FROM scheduling_failed_lots
        WHERE session_id = %s
        GROUP BY failure_reason
        ORDER BY count DESC
        """
        cursor.execute(failure_stats_sql, (latest_session_id,))
        failure_stats = cursor.fetchall()
        
        print(f"最新会话 {latest_session_id} 的失败统计:")
        total_failures = 0
        for stat in failure_stats:
            if isinstance(stat, dict):
                reason = stat['failure_reason']
                count = stat['count']
            else:
                reason, count = stat
            total_failures += count
            print(f"  {reason}: {count}次")
        
        print(f"总失败批次: {total_failures}")
        
        # 检查测试规范状态
        spec_status_sql = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN ACTV_YN = 1 THEN 1 END) as active,
            COUNT(CASE WHEN APPROVAL_STATE = 1 THEN 1 END) as approved
        FROM et_ft_test_spec
        """
        cursor.execute(spec_status_sql)
        spec_stats = cursor.fetchone()
        
        if isinstance(spec_stats, dict):
            total = spec_stats['total']
            active = spec_stats['active']
            approved = spec_stats['approved']
        else:
            total, active, approved = spec_stats
        
        print(f"\n测试规范状态:")
        print(f"  总数: {total}")
        print(f"  激活: {active} ({active/total*100:.1f}%)")
        print(f"  已审批: {approved} ({approved/total*100:.1f}%)")
        
        # 检查设备状态
        eqp_status_sql = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN STATUS = 'Run' THEN 1 END) as running,
            COUNT(CASE WHEN STATUS = 'IDLE' THEN 1 END) as idle,
            COUNT(CASE WHEN STATUS = 'DOWN' THEN 1 END) as down
        FROM eqp_status
        """
        cursor.execute(eqp_status_sql)
        eqp_stats = cursor.fetchone()
        
        if isinstance(eqp_stats, dict):
            total_eqp = eqp_stats['total']
            running = eqp_stats['running']
            idle = eqp_stats['idle']
            down = eqp_stats['down']
        else:
            total_eqp, running, idle, down = eqp_stats
        
        available = running + idle
        
        print(f"\n设备状态:")
        print(f"  总数: {total_eqp}")
        print(f"  可用: {available} ({available/total_eqp*100:.1f}%)")
        print(f"  故障: {down} ({down/total_eqp*100:.1f}%)")
        
        cursor.close()
        
        # 给出修复建议
        print(f"\n📋 修复建议:")
        if active < total * 0.5:
            print(f"  ⚠️  建议激活更多测试规范 (当前激活率: {active/total*100:.1f}%)")
        
        if available < total_eqp * 0.8:
            print(f"  ⚠️  建议修复更多设备 (当前可用率: {available/total_eqp*100:.1f}%)")
        
        if active >= total * 0.5 and available >= total_eqp * 0.8:
            print(f"  ✅ 系统状态良好，可以重新尝试排产")
        
    except Exception as e:
        logger.error(f"❌ 验证修复效果出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🔥 开始修复排产失败问题...")
    print("=" * 80)
    
    # 修复测试规范
    fixed_specs, created_specs = fix_test_spec_activation()
    
    # 修复设备状态
    fixed_stages = fix_equipment_status()
    
    # 验证修复效果
    verify_fixes()
    
    print("\n" + "=" * 80)
    print(f"🎯 修复完成总结:")
    print(f"  - 修复了 {fixed_specs} 个测试规范的激活状态")
    print(f"  - 创建了 {created_specs} 个新的测试规范")
    print(f"  - 改善了 {fixed_stages} 个工序的设备状态")
    print(f"")
    print(f"💡 建议下一步:")
    print(f"  1. 重新运行排产任务，验证修复效果")
    print(f"  2. 监控排产成功率的改善情况")
    print(f"  3. 如有必要，进一步优化设备配置")

if __name__ == "__main__":
    main()