#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证排产修复效果脚本
用于检查修复后的排产成功率改善情况
"""

import sys
import os
from datetime import datetime
import logging

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from app.utils.db_helper import get_mysql_connection

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_fix_effectiveness():
    """检查修复效果"""
    try:
        conn = get_mysql_connection()
        cursor = conn.cursor()
        
        print("🔍 验证排产修复效果")
        print("=" * 50)
        
        # 1. 检查测试规范状态
        spec_sql = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN ACTV_YN = 1 THEN 1 END) as active,
            COUNT(CASE WHEN APPROVAL_STATE = 1 THEN 1 END) as approved
        FROM et_ft_test_spec
        """
        cursor.execute(spec_sql)
        spec_result = cursor.fetchone()
        
        if isinstance(spec_result, dict):
            total = spec_result['total']
            active = spec_result['active']
            approved = spec_result['approved']
        else:
            total, active, approved = spec_result
        
        print(f"📋 测试规范状态:")
        print(f"  总数: {total}")
        print(f"  激活: {active} ({active/total*100:.1f}%)")
        print(f"  已审批: {approved} ({approved/total*100:.1f}%)")
        
        # 2. 检查设备状态
        eqp_sql = """
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN STATUS IN ('Run', 'IDLE') THEN 1 END) as available
        FROM eqp_status
        """
        cursor.execute(eqp_sql)
        eqp_result = cursor.fetchone()
        
        if isinstance(eqp_result, dict):
            total_eqp = eqp_result['total']
            available = eqp_result['available']
        else:
            total_eqp, available = eqp_result
        
        print(f"\n🏭 设备状态:")
        print(f"  总数: {total_eqp}")
        print(f"  可用: {available} ({available/total_eqp*100:.1f}%)")
        
        # 3. 检查最新排产会话状态
        latest_session_sql = """
        SELECT 
            session_id,
            COUNT(*) as failures,
            MAX(timestamp) as latest_time
        FROM scheduling_failed_lots
        GROUP BY session_id
        ORDER BY MAX(timestamp) DESC
        LIMIT 3
        """
        cursor.execute(latest_session_sql)
        sessions = cursor.fetchall()
        
        print(f"\n📊 最近排产会话失败统计:")
        for i, session in enumerate(sessions, 1):
            if isinstance(session, dict):
                session_id = session['session_id']
                failures = session['failures']
                time = session['latest_time']
            else:
                session_id, failures, time = session
            
            print(f"  {i}. {session_id}: {failures}次失败 ({time})")
        
        # 4. 给出建议
        print(f"\n💡 当前状态评估:")
        
        if active >= total * 0.5:
            print("  ✅ 测试规范激活率良好")
        else:
            print("  ⚠️  建议继续激活更多测试规范")
        
        if available >= total_eqp * 0.8:
            print("  ✅ 设备可用率良好") 
        else:
            print("  ⚠️  建议修复更多故障设备")
        
        cursor.close()
        
    except Exception as e:
        logger.error(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    check_fix_effectiveness()